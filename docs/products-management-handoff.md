# Products Management Development Handoff

## 🎯 **Objective**
Implement complete CRUD (Create, Read, Update, Delete) operations for Products Management in the NutriPro admin panel.

## 📋 **Current State**

### ✅ **What's Already Working**
- **Product Listing**: `/dashboard/inventory` shows products with search and filtering
- **Product Detail View**: `/dashboard/products/[id]` displays individual product information
- **Database Schema**: Complete products table with all necessary fields
- **TypeScript Types**: Product interfaces defined in `packages/types/src/database.ts`
- **Query Layer**: `ProductQueries` class in `packages/database/src/queries/products.ts`
- **UI Components**: Cards, forms, and layout components from shadcn/ui

### ❌ **What's Missing**
- **Add Product**: No "Add Product" functionality
- **Edit Product**: No product editing capabilities
- **Delete Product**: No product deletion
- **Category Management**: Categories exist but no CRUD operations
- **Brand Management**: Brands exist but no CRUD operations
- **Image Upload**: Product images not implemented
- **Bulk Operations**: No bulk import/export

## 🗂️ **File Structure**

### **Current Files**
```
apps/admin-panel/src/app/dashboard/
├── inventory/page.tsx          # Product listing (read-only)
└── products/[id]/page.tsx      # Product detail view (read-only)

packages/database/src/
├── queries/products.ts         # ProductQueries class
└── types/supabase.ts          # Generated database types

packages/types/src/
└── database.ts                # Product interfaces
```

### **Files to Create/Modify**
```
apps/admin-panel/src/app/dashboard/
├── inventory/
│   ├── new/page.tsx           # Add new product
│   ├── [id]/edit/page.tsx     # Edit existing product
│   └── categories/page.tsx    # Category management
└── components/
    ├── products/
    │   ├── ProductForm.tsx    # Reusable product form
    │   ├── CategorySelect.tsx # Category dropdown
    │   └── BrandSelect.tsx    # Brand dropdown
    └── ui/
        └── ImageUpload.tsx    # Image upload component
```

## 🏗️ **Database Schema Reference**

### **Products Table**
```sql
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sku varchar(50) UNIQUE NOT NULL,
  barcode varchar(50),
  name varchar(200) NOT NULL,
  description text,
  category_id uuid REFERENCES categories(id),
  brand_id uuid REFERENCES brands(id),
  vendor_id uuid REFERENCES vendors(id),
  
  -- Pricing (all in AWG unless specified)
  retail_price decimal(10,2) NOT NULL,
  landing_cost decimal(10,2),
  purchase_price decimal(10,2),
  purchase_currency varchar(3) DEFAULT 'AWG',
  wholesale_price decimal(10,2),
  wholesale_available boolean DEFAULT false,
  
  -- Inventory
  stock_quantity integer DEFAULT 0,
  min_stock_level integer DEFAULT 0,
  max_stock_level integer,
  
  -- Product Details
  notes text,
  weight decimal(8,2), -- in grams
  dimensions jsonb, -- {length, width, height}
  images text[], -- Array of image URLs
  
  -- Supplement-specific fields
  serving_size varchar(50),
  servings_per_container integer,
  ingredients text[],
  allergens text[],
  expiry_tracking boolean DEFAULT false,
  
  -- Product Variants
  has_variants boolean DEFAULT false,
  variant_type varchar(50),
  
  -- Wholesale specific
  min_order_quantity integer DEFAULT 1,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);
```

### **Related Tables**
- **Categories**: `id`, `name`, `description`, `sort_order`
- **Brands**: `id`, `name`, `description`, `website_url`
- **Vendors**: `id`, `name`, `contact_email`, `preferred_currency`

## 🎨 **UI/UX Requirements**

### **Product Form Fields**
1. **Basic Information**
   - Product Name (required)
   - SKU (required, auto-generate option)
   - Barcode
   - Description
   - Category (dropdown)
   - Brand (dropdown)
   - Vendor (dropdown)

2. **Pricing**
   - Retail Price (required)
   - Wholesale Price
   - Purchase Price
   - Landing Cost
   - Wholesale Available (checkbox)

3. **Inventory**
   - Current Stock
   - Minimum Stock Level
   - Maximum Stock Level

4. **Product Details**
   - Weight (grams)
   - Dimensions (L x W x H)
   - Images (multiple upload)
   - Notes

5. **Supplement Specific**
   - Serving Size
   - Servings per Container
   - Ingredients (array)
   - Allergens (array)
   - Expiry Tracking (checkbox)

6. **Variants**
   - Has Variants (checkbox)
   - Variant Type (flavor, size, etc.)

### **Navigation Updates Needed**
- Update inventory page "Add Product" button to work
- Add edit buttons on product cards
- Add delete confirmation modals

## 🔧 **Technical Implementation Notes**

### **Form Validation**
- Required fields: name, sku, retail_price
- SKU uniqueness validation
- Price validation (positive numbers)
- Stock quantity validation (non-negative integers)

### **Image Upload**
- Use Supabase Storage for image hosting
- Support multiple images per product
- Image compression and optimization
- Drag & drop interface

### **Auto-generation**
- SKU auto-generation based on brand/category
- Barcode generation option
- Slug generation for URLs

### **Error Handling**
- Form validation errors
- Database constraint violations
- Network errors
- File upload errors

## 🧪 **Testing Requirements**

### **Test Cases**
1. **Create Product**
   - Valid product creation
   - Duplicate SKU handling
   - Required field validation
   - Image upload functionality

2. **Edit Product**
   - Update all fields
   - Maintain data integrity
   - Handle concurrent edits

3. **Delete Product**
   - Soft delete vs hard delete
   - Check for dependencies (orders, etc.)
   - Confirmation workflow

### **Sample Data**
Use existing sample products from `packages/database/src/seed/001_sample_data.sql`

## 🚀 **Implementation Priority**

### **Phase 1: Basic CRUD**
1. Add Product form and functionality
2. Edit Product form and functionality
3. Delete Product with confirmation

### **Phase 2: Enhanced Features**
1. Image upload and management
2. Category and Brand management
3. Bulk operations

### **Phase 3: Advanced Features**
1. Product variants system
2. Bulk import/export
3. Advanced search and filtering

## 📁 **Key Files to Reference**

1. **Current Product Queries**: `packages/database/src/queries/products.ts`
2. **Database Types**: `packages/types/src/database.ts`
3. **Existing UI**: `apps/admin-panel/src/app/dashboard/inventory/page.tsx`
4. **Sample Data**: `packages/database/src/seed/001_sample_data.sql`
5. **Navigation**: `apps/admin-panel/src/app/dashboard/layout.tsx`

## 🎯 **Success Criteria**

- [ ] Staff can add new products with all required information
- [ ] Staff can edit existing products
- [ ] Staff can delete products (with proper safeguards)
- [ ] Form validation prevents invalid data
- [ ] Images can be uploaded and displayed
- [ ] Categories and brands can be managed
- [ ] All operations work smoothly without errors

---

**Ready for development!** All necessary context, database schema, and requirements are documented above.
