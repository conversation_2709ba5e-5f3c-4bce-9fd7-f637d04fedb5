#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/git-raw-commits@2.0.11/node_modules/git-raw-commits/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/git-raw-commits@2.0.11/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/git-raw-commits@2.0.11/node_modules/git-raw-commits/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/git-raw-commits@2.0.11/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../git-raw-commits@2.0.11/node_modules/git-raw-commits/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../git-raw-commits@2.0.11/node_modules/git-raw-commits/cli.js" "$@"
fi
