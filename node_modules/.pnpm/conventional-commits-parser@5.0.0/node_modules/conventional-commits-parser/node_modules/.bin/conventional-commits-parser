#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/conventional-commits-parser@5.0.0/node_modules/conventional-commits-parser/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/conventional-commits-parser@5.0.0/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/conventional-commits-parser@5.0.0/node_modules/conventional-commits-parser/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/conventional-commits-parser@5.0.0/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.mjs" "$@"
else
  exec node  "$basedir/../../cli.mjs" "$@"
fi
