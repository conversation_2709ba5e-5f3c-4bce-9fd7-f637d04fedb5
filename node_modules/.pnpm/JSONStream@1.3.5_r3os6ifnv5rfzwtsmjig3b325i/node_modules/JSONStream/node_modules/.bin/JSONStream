#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/JSONStream@1.3.5_r3os6ifnv5rfzwtsmjig3b325i/node_modules/JSONStream/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/JSONStream@1.3.5_r3os6ifnv5rfzwtsmjig3b325i/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/JSONStream@1.3.5_r3os6ifnv5rfzwtsmjig3b325i/node_modules/JSONStream/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/JSONStream@1.3.5_r3os6ifnv5rfzwtsmjig3b325i/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin.js" "$@"
else
  exec node  "$basedir/../../bin.js" "$@"
fi
