#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@5.8.3/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@5.8.3/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
