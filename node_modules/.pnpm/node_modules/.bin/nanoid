#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules/nanoid/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/nanoid@3.3.11/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../nanoid/bin/nanoid.cjs" "$@"
else
  exec node  "$basedir/../nanoid/bin/nanoid.cjs" "$@"
fi
