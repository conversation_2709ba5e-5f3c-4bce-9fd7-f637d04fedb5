#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/lib/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/lib/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/lib/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/lib/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules/webpack-bundle-analyzer/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/webpack-bundle-analyzer@4.10.1/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../webpack-bundle-analyzer/lib/bin/analyzer.js" "$@"
else
  exec node  "$basedir/../webpack-bundle-analyzer/lib/bin/analyzer.js" "$@"
fi
