#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged/bin/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules/lint-staged/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/lint-staged@15.5.2/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../lint-staged/bin/lint-staged.js" "$@"
else
  exec node  "$basedir/../lint-staged/bin/lint-staged.js" "$@"
fi
