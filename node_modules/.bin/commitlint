#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules/@commitlint/cli/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules/@commitlint/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules/@commitlint/cli/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules/@commitlint/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/@commitlint+cli@18.6.1_@types+node@20.19.4_typescript@5.8.3/node_modules:/Users/<USER>/Desktop/NutriPro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@commitlint/cli/cli.js" "$@"
else
  exec node  "$basedir/../@commitlint/cli/cli.js" "$@"
fi
