hoistPattern:
  - '*'
hoistedDependencies:
  /@adobe/css-tools/4.4.3:
    '@adobe/css-tools': private
  /@alloc/quick-lru/5.2.0:
    '@alloc/quick-lru': private
  /@ampproject/remapping/2.3.0:
    '@ampproject/remapping': private
  /@babel/code-frame/7.27.1:
    '@babel/code-frame': private
  /@babel/compat-data/7.28.0:
    '@babel/compat-data': private
  /@babel/core/7.28.0:
    '@babel/core': private
  /@babel/generator/7.28.0:
    '@babel/generator': private
  /@babel/helper-compilation-targets/7.27.2:
    '@babel/helper-compilation-targets': private
  /@babel/helper-globals/7.28.0:
    '@babel/helper-globals': private
  /@babel/helper-module-imports/7.27.1:
    '@babel/helper-module-imports': private
  /@babel/helper-module-transforms/7.27.3(@babel/core@7.28.0):
    '@babel/helper-module-transforms': private
  /@babel/helper-plugin-utils/7.27.1:
    '@babel/helper-plugin-utils': private
  /@babel/helper-string-parser/7.27.1:
    '@babel/helper-string-parser': private
  /@babel/helper-validator-identifier/7.27.1:
    '@babel/helper-validator-identifier': private
  /@babel/helper-validator-option/7.27.1:
    '@babel/helper-validator-option': private
  /@babel/helpers/7.27.6:
    '@babel/helpers': private
  /@babel/parser/7.28.0:
    '@babel/parser': private
  /@babel/plugin-syntax-async-generators/7.8.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-async-generators': private
  /@babel/plugin-syntax-bigint/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-bigint': private
  /@babel/plugin-syntax-class-properties/7.12.13(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-properties': private
  /@babel/plugin-syntax-class-static-block/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-class-static-block': private
  /@babel/plugin-syntax-import-attributes/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-attributes': private
  /@babel/plugin-syntax-import-meta/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-import-meta': private
  /@babel/plugin-syntax-json-strings/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-json-strings': private
  /@babel/plugin-syntax-jsx/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-jsx': private
  /@babel/plugin-syntax-logical-assignment-operators/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-logical-assignment-operators': private
  /@babel/plugin-syntax-nullish-coalescing-operator/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  /@babel/plugin-syntax-numeric-separator/7.10.4(@babel/core@7.28.0):
    '@babel/plugin-syntax-numeric-separator': private
  /@babel/plugin-syntax-object-rest-spread/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-object-rest-spread': private
  /@babel/plugin-syntax-optional-catch-binding/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-catch-binding': private
  /@babel/plugin-syntax-optional-chaining/7.8.3(@babel/core@7.28.0):
    '@babel/plugin-syntax-optional-chaining': private
  /@babel/plugin-syntax-private-property-in-object/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-private-property-in-object': private
  /@babel/plugin-syntax-top-level-await/7.14.5(@babel/core@7.28.0):
    '@babel/plugin-syntax-top-level-await': private
  /@babel/plugin-syntax-typescript/7.27.1(@babel/core@7.28.0):
    '@babel/plugin-syntax-typescript': private
  /@babel/runtime-corejs3/7.28.0:
    '@babel/runtime-corejs3': private
  /@babel/runtime/7.27.6:
    '@babel/runtime': private
  /@babel/template/7.27.2:
    '@babel/template': private
  /@babel/traverse/7.28.0:
    '@babel/traverse': private
  /@babel/types/7.28.0:
    '@babel/types': private
  /@bcoe/v8-coverage/0.2.3:
    '@bcoe/v8-coverage': private
  /@commitlint/config-validator/18.6.1:
    '@commitlint/config-validator': private
  /@commitlint/ensure/18.6.1:
    '@commitlint/ensure': private
  /@commitlint/execute-rule/18.6.1:
    '@commitlint/execute-rule': private
  /@commitlint/format/18.6.1:
    '@commitlint/format': private
  /@commitlint/is-ignored/18.6.1:
    '@commitlint/is-ignored': private
  /@commitlint/lint/18.6.1:
    '@commitlint/lint': private
  /@commitlint/load/18.6.1(@types/node@20.19.4)(typescript@5.8.3):
    '@commitlint/load': private
  /@commitlint/message/18.6.1:
    '@commitlint/message': private
  /@commitlint/parse/18.6.1:
    '@commitlint/parse': private
  /@commitlint/read/18.6.1:
    '@commitlint/read': private
  /@commitlint/resolve-extends/18.6.1:
    '@commitlint/resolve-extends': private
  /@commitlint/rules/18.6.1:
    '@commitlint/rules': private
  /@commitlint/to-lines/18.6.1:
    '@commitlint/to-lines': private
  /@commitlint/top-level/18.6.1:
    '@commitlint/top-level': private
  /@commitlint/types/18.6.1:
    '@commitlint/types': private
  /@cspotcode/source-map-support/0.8.1:
    '@cspotcode/source-map-support': private
  /@discoveryjs/json-ext/0.5.7:
    '@discoveryjs/json-ext': private
  /@esbuild/aix-ppc64/0.25.5:
    '@esbuild/aix-ppc64': private
  /@esbuild/android-arm/0.25.5:
    '@esbuild/android-arm': private
  /@esbuild/android-arm64/0.25.5:
    '@esbuild/android-arm64': private
  /@esbuild/android-x64/0.25.5:
    '@esbuild/android-x64': private
  /@esbuild/darwin-arm64/0.25.5:
    '@esbuild/darwin-arm64': private
  /@esbuild/darwin-x64/0.25.5:
    '@esbuild/darwin-x64': private
  /@esbuild/freebsd-arm64/0.25.5:
    '@esbuild/freebsd-arm64': private
  /@esbuild/freebsd-x64/0.25.5:
    '@esbuild/freebsd-x64': private
  /@esbuild/linux-arm/0.25.5:
    '@esbuild/linux-arm': private
  /@esbuild/linux-arm64/0.25.5:
    '@esbuild/linux-arm64': private
  /@esbuild/linux-ia32/0.25.5:
    '@esbuild/linux-ia32': private
  /@esbuild/linux-loong64/0.25.5:
    '@esbuild/linux-loong64': private
  /@esbuild/linux-mips64el/0.25.5:
    '@esbuild/linux-mips64el': private
  /@esbuild/linux-ppc64/0.25.5:
    '@esbuild/linux-ppc64': private
  /@esbuild/linux-riscv64/0.25.5:
    '@esbuild/linux-riscv64': private
  /@esbuild/linux-s390x/0.25.5:
    '@esbuild/linux-s390x': private
  /@esbuild/linux-x64/0.25.5:
    '@esbuild/linux-x64': private
  /@esbuild/netbsd-arm64/0.25.5:
    '@esbuild/netbsd-arm64': private
  /@esbuild/netbsd-x64/0.25.5:
    '@esbuild/netbsd-x64': private
  /@esbuild/openbsd-arm64/0.25.5:
    '@esbuild/openbsd-arm64': private
  /@esbuild/openbsd-x64/0.25.5:
    '@esbuild/openbsd-x64': private
  /@esbuild/sunos-x64/0.25.5:
    '@esbuild/sunos-x64': private
  /@esbuild/win32-arm64/0.25.5:
    '@esbuild/win32-arm64': private
  /@esbuild/win32-ia32/0.25.5:
    '@esbuild/win32-ia32': private
  /@esbuild/win32-x64/0.25.5:
    '@esbuild/win32-x64': private
  /@eslint-community/eslint-utils/4.7.0(eslint@8.57.1):
    '@eslint-community/eslint-utils': public
  /@eslint-community/regexpp/4.12.1:
    '@eslint-community/regexpp': public
  /@eslint/eslintrc/2.1.4:
    '@eslint/eslintrc': public
  /@eslint/js/8.57.1:
    '@eslint/js': public
  /@floating-ui/core/1.7.2:
    '@floating-ui/core': private
  /@floating-ui/dom/1.7.2:
    '@floating-ui/dom': private
  /@floating-ui/react-dom/1.3.0(react-dom@18.3.1)(react@18.3.1):
    '@floating-ui/react-dom': private
  /@floating-ui/react/0.19.2(react-dom@18.3.1)(react@18.3.1):
    '@floating-ui/react': private
  /@floating-ui/utils/0.2.10:
    '@floating-ui/utils': private
  /@headlessui/react/2.2.0(react-dom@18.3.1)(react@18.3.1):
    '@headlessui/react': private
  /@hookform/resolvers/3.10.0(react-hook-form@7.60.0):
    '@hookform/resolvers': private
  /@humanwhocodes/config-array/0.13.0:
    '@humanwhocodes/config-array': private
  /@humanwhocodes/module-importer/1.0.1:
    '@humanwhocodes/module-importer': private
  /@humanwhocodes/object-schema/2.0.3:
    '@humanwhocodes/object-schema': private
  /@hutson/parse-repository-url/5.0.0:
    '@hutson/parse-repository-url': private
  /@isaacs/cliui/8.0.2:
    '@isaacs/cliui': private
  /@istanbuljs/load-nyc-config/1.1.0:
    '@istanbuljs/load-nyc-config': private
  /@istanbuljs/schema/0.1.3:
    '@istanbuljs/schema': private
  /@jest/console/29.7.0:
    '@jest/console': private
  /@jest/core/29.7.0:
    '@jest/core': private
  /@jest/environment/29.7.0:
    '@jest/environment': private
  /@jest/expect-utils/29.7.0:
    '@jest/expect-utils': private
  /@jest/expect/29.7.0:
    '@jest/expect': private
  /@jest/fake-timers/29.7.0:
    '@jest/fake-timers': private
  /@jest/globals/29.7.0:
    '@jest/globals': private
  /@jest/reporters/29.7.0:
    '@jest/reporters': private
  /@jest/schemas/29.6.3:
    '@jest/schemas': private
  /@jest/source-map/29.6.3:
    '@jest/source-map': private
  /@jest/test-result/29.7.0:
    '@jest/test-result': private
  /@jest/test-sequencer/29.7.0:
    '@jest/test-sequencer': private
  /@jest/transform/29.7.0:
    '@jest/transform': private
  /@jest/types/29.6.3:
    '@jest/types': private
  /@jridgewell/gen-mapping/0.3.12:
    '@jridgewell/gen-mapping': private
  /@jridgewell/resolve-uri/3.1.2:
    '@jridgewell/resolve-uri': private
  /@jridgewell/sourcemap-codec/1.5.4:
    '@jridgewell/sourcemap-codec': private
  /@jridgewell/trace-mapping/0.3.29:
    '@jridgewell/trace-mapping': private
  /@next/bundle-analyzer/14.2.30:
    '@next/bundle-analyzer': private
  /@next/env/14.0.4:
    '@next/env': private
  /@next/eslint-plugin-next/14.2.30:
    '@next/eslint-plugin-next': public
  /@next/swc-darwin-arm64/14.0.4:
    '@next/swc-darwin-arm64': private
  /@next/swc-darwin-x64/14.0.4:
    '@next/swc-darwin-x64': private
  /@next/swc-linux-arm64-gnu/14.0.4:
    '@next/swc-linux-arm64-gnu': private
  /@next/swc-linux-arm64-musl/14.0.4:
    '@next/swc-linux-arm64-musl': private
  /@next/swc-linux-x64-gnu/14.0.4:
    '@next/swc-linux-x64-gnu': private
  /@next/swc-linux-x64-musl/14.0.4:
    '@next/swc-linux-x64-musl': private
  /@next/swc-win32-arm64-msvc/14.0.4:
    '@next/swc-win32-arm64-msvc': private
  /@next/swc-win32-ia32-msvc/14.0.4:
    '@next/swc-win32-ia32-msvc': private
  /@next/swc-win32-x64-msvc/14.0.4:
    '@next/swc-win32-x64-msvc': private
  /@nodelib/fs.scandir/2.1.5:
    '@nodelib/fs.scandir': private
  /@nodelib/fs.stat/2.0.5:
    '@nodelib/fs.stat': private
  /@nodelib/fs.walk/1.2.8:
    '@nodelib/fs.walk': private
  /@nolyfill/is-core-module/1.0.39:
    '@nolyfill/is-core-module': private
  /@pkgjs/parseargs/0.11.0:
    '@pkgjs/parseargs': private
  /@polka/url/1.0.0-next.29:
    '@polka/url': private
  /@radix-ui/number/1.1.1:
    '@radix-ui/number': private
  /@radix-ui/primitive/1.1.2:
    '@radix-ui/primitive': private
  /@radix-ui/react-accordion/1.2.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-accordion': private
  /@radix-ui/react-alert-dialog/1.1.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-alert-dialog': private
  /@radix-ui/react-arrow/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-arrow': private
  /@radix-ui/react-avatar/1.1.10(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-avatar': private
  /@radix-ui/react-checkbox/1.3.2(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-checkbox': private
  /@radix-ui/react-collapsible/1.1.11(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-collapsible': private
  /@radix-ui/react-collection/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-collection': private
  /@radix-ui/react-compose-refs/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-compose-refs': private
  /@radix-ui/react-context/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-context': private
  /@radix-ui/react-dialog/1.1.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dialog': private
  /@radix-ui/react-direction/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-direction': private
  /@radix-ui/react-dismissable-layer/1.1.10(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dismissable-layer': private
  /@radix-ui/react-dropdown-menu/2.1.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-dropdown-menu': private
  /@radix-ui/react-focus-guards/1.1.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-focus-guards': private
  /@radix-ui/react-focus-scope/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-focus-scope': private
  /@radix-ui/react-id/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-id': private
  /@radix-ui/react-label/2.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-label': private
  /@radix-ui/react-menu/2.1.15(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-menu': private
  /@radix-ui/react-popover/1.1.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-popover': private
  /@radix-ui/react-popper/1.2.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-popper': private
  /@radix-ui/react-portal/1.1.9(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-portal': private
  /@radix-ui/react-presence/1.1.4(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-presence': private
  /@radix-ui/react-primitive/2.1.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-primitive': private
  /@radix-ui/react-roving-focus/1.1.10(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-roving-focus': private
  /@radix-ui/react-scroll-area/1.2.9(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-scroll-area': private
  /@radix-ui/react-select/2.2.5(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-select': private
  /@radix-ui/react-separator/1.1.7(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-separator': private
  /@radix-ui/react-slot/1.2.3(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-slot': private
  /@radix-ui/react-switch/1.2.5(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-switch': private
  /@radix-ui/react-tabs/1.1.12(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-tabs': private
  /@radix-ui/react-toast/1.2.14(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-toast': private
  /@radix-ui/react-use-callback-ref/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-callback-ref': private
  /@radix-ui/react-use-controllable-state/1.2.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-controllable-state': private
  /@radix-ui/react-use-effect-event/0.0.2(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-effect-event': private
  /@radix-ui/react-use-escape-keydown/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-escape-keydown': private
  /@radix-ui/react-use-is-hydrated/0.1.0(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-is-hydrated': private
  /@radix-ui/react-use-layout-effect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-layout-effect': private
  /@radix-ui/react-use-previous/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-previous': private
  /@radix-ui/react-use-rect/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-rect': private
  /@radix-ui/react-use-size/1.1.1(@types/react@18.3.23)(react@18.3.1):
    '@radix-ui/react-use-size': private
  /@radix-ui/react-visually-hidden/1.2.3(@types/react-dom@18.3.7)(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@radix-ui/react-visually-hidden': private
  /@radix-ui/rect/1.1.1:
    '@radix-ui/rect': private
  /@react-aria/focus/3.20.5(react-dom@18.3.1)(react@18.3.1):
    '@react-aria/focus': private
  /@react-aria/interactions/3.25.3(react-dom@18.3.1)(react@18.3.1):
    '@react-aria/interactions': private
  /@react-aria/ssr/3.9.9(react@18.3.1):
    '@react-aria/ssr': private
  /@react-aria/utils/3.29.1(react-dom@18.3.1)(react@18.3.1):
    '@react-aria/utils': private
  /@react-stately/flags/3.1.2:
    '@react-stately/flags': private
  /@react-stately/utils/3.10.7(react@18.3.1):
    '@react-stately/utils': private
  /@react-types/shared/3.30.0(react@18.3.1):
    '@react-types/shared': private
  /@rtsao/scc/1.1.0:
    '@rtsao/scc': private
  /@rushstack/eslint-patch/1.12.0:
    '@rushstack/eslint-patch': public
  /@sinclair/typebox/0.27.8:
    '@sinclair/typebox': private
  /@sinonjs/commons/3.0.1:
    '@sinonjs/commons': private
  /@sinonjs/fake-timers/10.3.0:
    '@sinonjs/fake-timers': private
  /@supabase/auth-js/2.70.0:
    '@supabase/auth-js': private
  /@supabase/functions-js/2.4.5:
    '@supabase/functions-js': private
  /@supabase/node-fetch/2.6.15:
    '@supabase/node-fetch': private
  /@supabase/postgrest-js/1.19.4:
    '@supabase/postgrest-js': private
  /@supabase/realtime-js/2.11.15:
    '@supabase/realtime-js': private
  /@supabase/ssr/0.1.0(@supabase/supabase-js@2.50.3):
    '@supabase/ssr': private
  /@supabase/storage-js/2.7.1:
    '@supabase/storage-js': private
  /@supabase/supabase-js/2.50.3:
    '@supabase/supabase-js': private
  /@swc/helpers/0.5.2:
    '@swc/helpers': private
  /@tanstack/query-core/5.81.5:
    '@tanstack/query-core': private
  /@tanstack/react-query/5.81.5(react@18.3.1):
    '@tanstack/react-query': private
  /@tanstack/react-table/8.21.3(react-dom@18.3.1)(react@18.3.1):
    '@tanstack/react-table': private
  /@tanstack/react-virtual/3.13.12(react-dom@18.3.1)(react@18.3.1):
    '@tanstack/react-virtual': private
  /@tanstack/table-core/8.21.3:
    '@tanstack/table-core': private
  /@tanstack/virtual-core/3.13.12:
    '@tanstack/virtual-core': private
  /@testing-library/dom/9.3.4:
    '@testing-library/dom': private
  /@testing-library/jest-dom/6.6.3:
    '@testing-library/jest-dom': private
  /@testing-library/react/14.3.1(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    '@testing-library/react': private
  /@testing-library/user-event/14.6.1(@testing-library/dom@10.4.0):
    '@testing-library/user-event': private
  /@tootallnate/once/2.0.0:
    '@tootallnate/once': private
  /@tootallnate/quickjs-emscripten/0.23.0:
    '@tootallnate/quickjs-emscripten': private
  /@tremor/react/3.18.7(react-dom@18.3.1)(react@18.3.1):
    '@tremor/react': private
  /@tsconfig/node10/1.0.11:
    '@tsconfig/node10': private
  /@tsconfig/node12/1.0.11:
    '@tsconfig/node12': private
  /@tsconfig/node14/1.0.3:
    '@tsconfig/node14': private
  /@tsconfig/node16/1.0.4:
    '@tsconfig/node16': private
  /@turbo/workspaces/1.13.4:
    '@turbo/workspaces': private
  /@types/aria-query/5.0.4:
    '@types/aria-query': private
  /@types/babel__core/7.20.5:
    '@types/babel__core': private
  /@types/babel__generator/7.27.0:
    '@types/babel__generator': private
  /@types/babel__template/7.4.4:
    '@types/babel__template': private
  /@types/babel__traverse/7.20.7:
    '@types/babel__traverse': private
  /@types/d3-array/3.2.1:
    '@types/d3-array': private
  /@types/d3-color/3.1.3:
    '@types/d3-color': private
  /@types/d3-ease/3.0.2:
    '@types/d3-ease': private
  /@types/d3-interpolate/3.0.4:
    '@types/d3-interpolate': private
  /@types/d3-path/3.1.1:
    '@types/d3-path': private
  /@types/d3-scale/4.0.9:
    '@types/d3-scale': private
  /@types/d3-shape/3.1.7:
    '@types/d3-shape': private
  /@types/d3-time/3.0.4:
    '@types/d3-time': private
  /@types/d3-timer/3.0.2:
    '@types/d3-timer': private
  /@types/glob/7.2.0:
    '@types/glob': private
  /@types/graceful-fs/4.1.9:
    '@types/graceful-fs': private
  /@types/inquirer/6.5.0:
    '@types/inquirer': private
  /@types/istanbul-lib-coverage/2.0.6:
    '@types/istanbul-lib-coverage': private
  /@types/istanbul-lib-report/3.0.3:
    '@types/istanbul-lib-report': private
  /@types/istanbul-reports/3.0.4:
    '@types/istanbul-reports': private
  /@types/jest/29.5.14:
    '@types/jest': private
  /@types/jsdom/20.0.1:
    '@types/jsdom': private
  /@types/json-schema/7.0.15:
    '@types/json-schema': private
  /@types/json5/0.0.29:
    '@types/json5': private
  /@types/minimatch/6.0.0:
    '@types/minimatch': private
  /@types/minimist/1.2.5:
    '@types/minimist': private
  /@types/node/20.19.4:
    '@types/node': private
  /@types/normalize-package-data/2.4.4:
    '@types/normalize-package-data': private
  /@types/phoenix/1.6.6:
    '@types/phoenix': private
  /@types/prop-types/15.7.15:
    '@types/prop-types': private
  /@types/react-dom/18.3.7(@types/react@18.3.23):
    '@types/react-dom': private
  /@types/react/18.3.23:
    '@types/react': private
  /@types/semver/7.7.0:
    '@types/semver': private
  /@types/stack-utils/2.0.3:
    '@types/stack-utils': private
  /@types/through/0.0.33:
    '@types/through': private
  /@types/tinycolor2/1.4.6:
    '@types/tinycolor2': private
  /@types/tough-cookie/4.0.5:
    '@types/tough-cookie': private
  /@types/ws/8.18.1:
    '@types/ws': private
  /@types/yargs-parser/21.0.3:
    '@types/yargs-parser': private
  /@types/yargs/17.0.33:
    '@types/yargs': private
  /@typescript-eslint/eslint-plugin/6.21.0(@typescript-eslint/parser@6.21.0)(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/eslint-plugin': public
  /@typescript-eslint/parser/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/parser': public
  /@typescript-eslint/scope-manager/6.21.0:
    '@typescript-eslint/scope-manager': public
  /@typescript-eslint/type-utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/type-utils': public
  /@typescript-eslint/types/6.21.0:
    '@typescript-eslint/types': public
  /@typescript-eslint/typescript-estree/6.21.0(typescript@5.8.3):
    '@typescript-eslint/typescript-estree': public
  /@typescript-eslint/utils/6.21.0(eslint@8.57.1)(typescript@5.8.3):
    '@typescript-eslint/utils': public
  /@typescript-eslint/visitor-keys/6.21.0:
    '@typescript-eslint/visitor-keys': public
  /@ungap/structured-clone/1.3.0:
    '@ungap/structured-clone': private
  /@unrs/resolver-binding-android-arm-eabi/1.11.0:
    '@unrs/resolver-binding-android-arm-eabi': private
  /@unrs/resolver-binding-android-arm64/1.11.0:
    '@unrs/resolver-binding-android-arm64': private
  /@unrs/resolver-binding-darwin-arm64/1.11.0:
    '@unrs/resolver-binding-darwin-arm64': private
  /@unrs/resolver-binding-darwin-x64/1.11.0:
    '@unrs/resolver-binding-darwin-x64': private
  /@unrs/resolver-binding-freebsd-x64/1.11.0:
    '@unrs/resolver-binding-freebsd-x64': private
  /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.0:
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  /@unrs/resolver-binding-linux-arm-musleabihf/1.11.0:
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  /@unrs/resolver-binding-linux-arm64-gnu/1.11.0:
    '@unrs/resolver-binding-linux-arm64-gnu': private
  /@unrs/resolver-binding-linux-arm64-musl/1.11.0:
    '@unrs/resolver-binding-linux-arm64-musl': private
  /@unrs/resolver-binding-linux-ppc64-gnu/1.11.0:
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-gnu/1.11.0:
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  /@unrs/resolver-binding-linux-riscv64-musl/1.11.0:
    '@unrs/resolver-binding-linux-riscv64-musl': private
  /@unrs/resolver-binding-linux-s390x-gnu/1.11.0:
    '@unrs/resolver-binding-linux-s390x-gnu': private
  /@unrs/resolver-binding-linux-x64-gnu/1.11.0:
    '@unrs/resolver-binding-linux-x64-gnu': private
  /@unrs/resolver-binding-linux-x64-musl/1.11.0:
    '@unrs/resolver-binding-linux-x64-musl': private
  /@unrs/resolver-binding-wasm32-wasi/1.11.0:
    '@unrs/resolver-binding-wasm32-wasi': private
  /@unrs/resolver-binding-win32-arm64-msvc/1.11.0:
    '@unrs/resolver-binding-win32-arm64-msvc': private
  /@unrs/resolver-binding-win32-ia32-msvc/1.11.0:
    '@unrs/resolver-binding-win32-ia32-msvc': private
  /@unrs/resolver-binding-win32-x64-msvc/1.11.0:
    '@unrs/resolver-binding-win32-x64-msvc': private
  /JSONStream/1.3.5:
    JSONStream: private
  /abab/2.0.6:
    abab: private
  /acorn-globals/7.0.1:
    acorn-globals: private
  /acorn-jsx/5.3.2(acorn@8.15.0):
    acorn-jsx: private
  /acorn-walk/8.3.4:
    acorn-walk: private
  /acorn/8.15.0:
    acorn: private
  /add-stream/1.0.0:
    add-stream: private
  /agent-base/7.1.3:
    agent-base: private
  /aggregate-error/3.1.0:
    aggregate-error: private
  /ajv/6.12.6:
    ajv: private
  /ansi-escapes/4.3.2:
    ansi-escapes: private
  /ansi-regex/5.0.1:
    ansi-regex: private
  /ansi-styles/3.2.1:
    ansi-styles: private
  /any-promise/1.3.0:
    any-promise: private
  /anymatch/3.1.3:
    anymatch: private
  /arg/5.0.2:
    arg: private
  /argparse/2.0.1:
    argparse: private
  /aria-hidden/1.2.6:
    aria-hidden: private
  /aria-query/5.3.2:
    aria-query: private
  /array-buffer-byte-length/1.0.2:
    array-buffer-byte-length: private
  /array-ify/1.0.0:
    array-ify: private
  /array-includes/3.1.9:
    array-includes: private
  /array-union/2.1.0:
    array-union: private
  /array.prototype.findlast/1.2.5:
    array.prototype.findlast: private
  /array.prototype.findlastindex/1.2.6:
    array.prototype.findlastindex: private
  /array.prototype.flat/1.3.3:
    array.prototype.flat: private
  /array.prototype.flatmap/1.3.3:
    array.prototype.flatmap: private
  /array.prototype.tosorted/1.1.4:
    array.prototype.tosorted: private
  /arraybuffer.prototype.slice/1.0.4:
    arraybuffer.prototype.slice: private
  /arrify/1.0.1:
    arrify: private
  /ast-types-flow/0.0.8:
    ast-types-flow: private
  /ast-types/0.13.4:
    ast-types: private
  /async-function/1.0.0:
    async-function: private
  /async/3.2.6:
    async: private
  /asynckit/0.4.0:
    asynckit: private
  /autoprefixer/10.4.21(postcss@8.5.6):
    autoprefixer: private
  /available-typed-arrays/1.0.7:
    available-typed-arrays: private
  /axe-core/4.10.3:
    axe-core: private
  /axobject-query/4.1.0:
    axobject-query: private
  /babel-jest/29.7.0(@babel/core@7.28.0):
    babel-jest: private
  /babel-plugin-istanbul/6.1.1:
    babel-plugin-istanbul: private
  /babel-plugin-jest-hoist/29.6.3:
    babel-plugin-jest-hoist: private
  /babel-preset-current-node-syntax/1.1.0(@babel/core@7.28.0):
    babel-preset-current-node-syntax: private
  /babel-preset-jest/29.6.3(@babel/core@7.28.0):
    babel-preset-jest: private
  /balanced-match/1.0.2:
    balanced-match: private
  /base64-js/1.5.1:
    base64-js: private
  /basic-ftp/5.0.5:
    basic-ftp: private
  /binary-extensions/2.3.0:
    binary-extensions: private
  /bl/4.1.0:
    bl: private
  /brace-expansion/1.1.12:
    brace-expansion: private
  /braces/3.0.3:
    braces: private
  /browserslist/4.25.1:
    browserslist: private
  /bs-logger/0.2.6:
    bs-logger: private
  /bser/2.1.1:
    bser: private
  /buffer-from/1.1.2:
    buffer-from: private
  /buffer/5.7.1:
    buffer: private
  /busboy/1.6.0:
    busboy: private
  /call-bind-apply-helpers/1.0.2:
    call-bind-apply-helpers: private
  /call-bind/1.0.8:
    call-bind: private
  /call-bound/1.0.4:
    call-bound: private
  /callsites/3.1.0:
    callsites: private
  /camel-case/3.0.0:
    camel-case: private
  /camelcase-css/2.0.1:
    camelcase-css: private
  /camelcase-keys/6.2.2:
    camelcase-keys: private
  /camelcase/6.3.0:
    camelcase: private
  /caniuse-lite/1.0.30001727:
    caniuse-lite: private
  /chalk/3.0.0:
    chalk: private
  /change-case/3.1.0:
    change-case: private
  /char-regex/1.0.2:
    char-regex: private
  /chardet/0.7.0:
    chardet: private
  /chokidar/3.6.0:
    chokidar: private
  /ci-info/3.9.0:
    ci-info: private
  /cjs-module-lexer/1.4.3:
    cjs-module-lexer: private
  /class-variance-authority/0.7.1:
    class-variance-authority: private
  /clean-stack/2.2.0:
    clean-stack: private
  /cli-cursor/3.1.0:
    cli-cursor: private
  /cli-spinners/2.9.2:
    cli-spinners: private
  /cli-truncate/4.0.0:
    cli-truncate: private
  /cli-width/3.0.0:
    cli-width: private
  /client-only/0.0.1:
    client-only: private
  /cliui/8.0.1:
    cliui: private
  /clone/1.0.4:
    clone: private
  /clsx/2.1.1:
    clsx: private
  /cmdk/0.2.1(@types/react@18.3.23)(react-dom@18.3.1)(react@18.3.1):
    cmdk: private
  /co/4.6.0:
    co: private
  /collect-v8-coverage/1.0.2:
    collect-v8-coverage: private
  /color-convert/1.9.3:
    color-convert: private
  /color-name/1.1.3:
    color-name: private
  /colorette/2.0.20:
    colorette: private
  /combined-stream/1.0.8:
    combined-stream: private
  /commander/10.0.1:
    commander: private
  /compare-func/2.0.0:
    compare-func: private
  /concat-map/0.0.1:
    concat-map: private
  /constant-case/2.0.0:
    constant-case: private
  /conventional-changelog-angular/7.0.0:
    conventional-changelog-angular: private
  /conventional-changelog-atom/4.0.0:
    conventional-changelog-atom: private
  /conventional-changelog-codemirror/4.0.0:
    conventional-changelog-codemirror: private
  /conventional-changelog-conventionalcommits/7.0.2:
    conventional-changelog-conventionalcommits: private
  /conventional-changelog-core/7.0.0:
    conventional-changelog-core: private
  /conventional-changelog-ember/4.0.0:
    conventional-changelog-ember: private
  /conventional-changelog-eslint/5.0.0:
    conventional-changelog-eslint: public
  /conventional-changelog-express/4.0.0:
    conventional-changelog-express: private
  /conventional-changelog-jquery/5.0.0:
    conventional-changelog-jquery: private
  /conventional-changelog-jshint/4.0.0:
    conventional-changelog-jshint: private
  /conventional-changelog-preset-loader/4.1.0:
    conventional-changelog-preset-loader: private
  /conventional-changelog-writer/7.0.1:
    conventional-changelog-writer: private
  /conventional-changelog/5.1.0:
    conventional-changelog: private
  /conventional-commits-filter/4.0.0:
    conventional-commits-filter: private
  /conventional-commits-parser/5.0.0:
    conventional-commits-parser: private
  /convert-source-map/2.0.0:
    convert-source-map: private
  /cookie/0.5.0:
    cookie: private
  /core-js-pure/3.44.0:
    core-js-pure: private
  /cosmiconfig-typescript-loader/5.1.0(@types/node@20.19.4)(cosmiconfig@8.3.6)(typescript@5.8.3):
    cosmiconfig-typescript-loader: private
  /cosmiconfig/8.3.6(typescript@5.8.3):
    cosmiconfig: private
  /create-jest/29.7.0(@types/node@20.19.4):
    create-jest: private
  /create-require/1.1.1:
    create-require: private
  /cross-spawn/7.0.6:
    cross-spawn: private
  /css.escape/1.5.1:
    css.escape: private
  /cssesc/3.0.0:
    cssesc: private
  /cssom/0.5.0:
    cssom: private
  /cssstyle/2.3.0:
    cssstyle: private
  /csstype/3.1.3:
    csstype: private
  /d3-array/3.2.4:
    d3-array: private
  /d3-color/3.1.0:
    d3-color: private
  /d3-ease/3.0.1:
    d3-ease: private
  /d3-format/3.1.0:
    d3-format: private
  /d3-interpolate/3.0.1:
    d3-interpolate: private
  /d3-path/3.1.0:
    d3-path: private
  /d3-scale/4.0.2:
    d3-scale: private
  /d3-shape/3.2.0:
    d3-shape: private
  /d3-time-format/4.1.0:
    d3-time-format: private
  /d3-time/3.1.0:
    d3-time: private
  /d3-timer/3.0.1:
    d3-timer: private
  /damerau-levenshtein/1.0.8:
    damerau-levenshtein: private
  /dargs/7.0.0:
    dargs: private
  /data-uri-to-buffer/6.0.2:
    data-uri-to-buffer: private
  /data-urls/3.0.2:
    data-urls: private
  /data-view-buffer/1.0.2:
    data-view-buffer: private
  /data-view-byte-length/1.0.2:
    data-view-byte-length: private
  /data-view-byte-offset/1.0.1:
    data-view-byte-offset: private
  /date-fns/3.6.0:
    date-fns: private
  /debounce/1.2.1:
    debounce: private
  /debug/4.4.1:
    debug: private
  /decamelize-keys/1.1.1:
    decamelize-keys: private
  /decamelize/1.2.0:
    decamelize: private
  /decimal.js-light/2.5.1:
    decimal.js-light: private
  /decimal.js/10.6.0:
    decimal.js: private
  /dedent/1.6.0:
    dedent: private
  /deep-equal/2.2.3:
    deep-equal: private
  /deep-extend/0.6.0:
    deep-extend: private
  /deep-is/0.1.4:
    deep-is: private
  /deepmerge/4.3.1:
    deepmerge: private
  /defaults/1.0.4:
    defaults: private
  /define-data-property/1.1.4:
    define-data-property: private
  /define-properties/1.2.1:
    define-properties: private
  /degenerator/5.0.1:
    degenerator: private
  /del/5.1.0:
    del: private
  /delayed-stream/1.0.0:
    delayed-stream: private
  /dequal/2.0.3:
    dequal: private
  /detect-newline/3.1.0:
    detect-newline: private
  /detect-node-es/1.1.0:
    detect-node-es: private
  /didyoumean/1.2.2:
    didyoumean: private
  /diff-sequences/29.6.3:
    diff-sequences: private
  /diff/4.0.2:
    diff: private
  /dir-glob/3.0.1:
    dir-glob: private
  /dlv/1.1.3:
    dlv: private
  /doctrine/2.1.0:
    doctrine: private
  /dom-accessibility-api/0.6.3:
    dom-accessibility-api: private
  /dom-helpers/5.2.1:
    dom-helpers: private
  /domexception/4.0.0:
    domexception: private
  /dot-case/2.1.1:
    dot-case: private
  /dot-prop/5.3.0:
    dot-prop: private
  /dunder-proto/1.0.1:
    dunder-proto: private
  /duplexer/0.1.2:
    duplexer: private
  /eastasianwidth/0.2.0:
    eastasianwidth: private
  /ejs/3.1.10:
    ejs: private
  /electron-to-chromium/1.5.179:
    electron-to-chromium: private
  /emittery/0.13.1:
    emittery: private
  /emoji-regex/9.2.2:
    emoji-regex: private
  /entities/6.0.1:
    entities: private
  /environment/1.1.0:
    environment: private
  /error-ex/1.3.2:
    error-ex: private
  /es-abstract/1.24.0:
    es-abstract: private
  /es-define-property/1.0.1:
    es-define-property: private
  /es-errors/1.3.0:
    es-errors: private
  /es-get-iterator/1.1.3:
    es-get-iterator: private
  /es-iterator-helpers/1.2.1:
    es-iterator-helpers: private
  /es-object-atoms/1.1.1:
    es-object-atoms: private
  /es-set-tostringtag/2.1.0:
    es-set-tostringtag: private
  /es-shim-unscopables/1.1.0:
    es-shim-unscopables: private
  /es-to-primitive/1.3.0:
    es-to-primitive: private
  /esbuild/0.25.5:
    esbuild: private
  /escalade/3.2.0:
    escalade: private
  /escape-string-regexp/4.0.0:
    escape-string-regexp: private
  /escodegen/2.1.0:
    escodegen: private
  /eslint-config-next/14.0.4(eslint@8.57.1)(typescript@5.8.3):
    eslint-config-next: public
  /eslint-config-prettier/9.1.0(eslint@8.57.1):
    eslint-config-prettier: public
  /eslint-import-resolver-node/0.3.9:
    eslint-import-resolver-node: public
  /eslint-import-resolver-typescript/3.10.1(eslint-plugin-import@2.32.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: public
  /eslint-module-utils/2.12.1(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-module-utils: public
  /eslint-plugin-import/2.32.0(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-typescript@3.10.1)(eslint@8.57.1):
    eslint-plugin-import: public
  /eslint-plugin-jsx-a11y/6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: public
  /eslint-plugin-react-hooks/4.6.2(eslint@8.57.1):
    eslint-plugin-react-hooks: public
  /eslint-plugin-react-native-globals/0.1.2:
    eslint-plugin-react-native-globals: public
  /eslint-plugin-react-native/4.1.0(eslint@8.57.1):
    eslint-plugin-react-native: public
  /eslint-plugin-react/7.37.5(eslint@8.57.1):
    eslint-plugin-react: public
  /eslint-scope/7.2.2:
    eslint-scope: public
  /eslint-visitor-keys/3.4.3:
    eslint-visitor-keys: public
  /eslint/8.57.1:
    eslint: public
  /espree/9.6.1:
    espree: private
  /esprima/4.0.1:
    esprima: private
  /esquery/1.6.0:
    esquery: private
  /esrecurse/4.3.0:
    esrecurse: private
  /estraverse/5.3.0:
    estraverse: private
  /esutils/2.0.3:
    esutils: private
  /eventemitter3/4.0.7:
    eventemitter3: private
  /execa/5.1.1:
    execa: private
  /exit/0.1.2:
    exit: private
  /expect/29.7.0:
    expect: private
  /external-editor/3.1.0:
    external-editor: private
  /fast-deep-equal/3.1.3:
    fast-deep-equal: private
  /fast-equals/5.2.2:
    fast-equals: private
  /fast-glob/3.3.3:
    fast-glob: private
  /fast-json-stable-stringify/2.1.0:
    fast-json-stable-stringify: private
  /fast-levenshtein/2.0.6:
    fast-levenshtein: private
  /fast-uri/3.0.6:
    fast-uri: private
  /fastq/1.19.1:
    fastq: private
  /fb-watchman/2.0.2:
    fb-watchman: private
  /fdir/6.4.6(picomatch@4.0.2):
    fdir: private
  /figures/3.2.0:
    figures: private
  /file-entry-cache/6.0.1:
    file-entry-cache: private
  /filelist/1.0.4:
    filelist: private
  /fill-range/7.1.1:
    fill-range: private
  /find-up/5.0.0:
    find-up: private
  /flat-cache/3.2.0:
    flat-cache: private
  /flatted/3.3.3:
    flatted: private
  /for-each/0.3.5:
    for-each: private
  /foreground-child/3.3.1:
    foreground-child: private
  /form-data/4.0.3:
    form-data: private
  /fraction.js/4.3.7:
    fraction.js: private
  /fs-extra/10.1.0:
    fs-extra: private
  /fs.realpath/1.0.0:
    fs.realpath: private
  /fsevents/2.3.3:
    fsevents: private
  /function-bind/1.1.2:
    function-bind: private
  /function.prototype.name/1.1.8:
    function.prototype.name: private
  /functions-have-names/1.2.3:
    functions-have-names: private
  /gensync/1.0.0-beta.2:
    gensync: private
  /get-caller-file/2.0.5:
    get-caller-file: private
  /get-east-asian-width/1.3.0:
    get-east-asian-width: private
  /get-intrinsic/1.3.0:
    get-intrinsic: private
  /get-nonce/1.0.1:
    get-nonce: private
  /get-package-type/0.1.0:
    get-package-type: private
  /get-proto/1.0.1:
    get-proto: private
  /get-stream/6.0.1:
    get-stream: private
  /get-symbol-description/1.1.0:
    get-symbol-description: private
  /get-tsconfig/4.10.1:
    get-tsconfig: private
  /get-uri/6.0.4:
    get-uri: private
  /git-raw-commits/2.0.11:
    git-raw-commits: private
  /git-semver-tags/7.0.1:
    git-semver-tags: private
  /glob-parent/6.0.2:
    glob-parent: private
  /glob-to-regexp/0.4.1:
    glob-to-regexp: private
  /glob/10.3.10:
    glob: private
  /global-dirs/0.1.1:
    global-dirs: private
  /globals/13.24.0:
    globals: private
  /globalthis/1.0.4:
    globalthis: private
  /globby/11.1.0:
    globby: private
  /gopd/1.2.0:
    gopd: private
  /graceful-fs/4.2.11:
    graceful-fs: private
  /gradient-string/2.0.2:
    gradient-string: private
  /graphemer/1.4.0:
    graphemer: private
  /gzip-size/6.0.0:
    gzip-size: private
  /handlebars/4.7.8:
    handlebars: private
  /hard-rejection/2.1.0:
    hard-rejection: private
  /has-bigints/1.1.0:
    has-bigints: private
  /has-flag/3.0.0:
    has-flag: private
  /has-property-descriptors/1.0.2:
    has-property-descriptors: private
  /has-proto/1.2.0:
    has-proto: private
  /has-symbols/1.1.0:
    has-symbols: private
  /has-tostringtag/1.0.2:
    has-tostringtag: private
  /hasown/2.0.2:
    hasown: private
  /header-case/1.0.1:
    header-case: private
  /hosted-git-info/7.0.2:
    hosted-git-info: private
  /html-encoding-sniffer/3.0.0:
    html-encoding-sniffer: private
  /html-escaper/2.0.2:
    html-escaper: private
  /http-proxy-agent/5.0.0:
    http-proxy-agent: private
  /https-proxy-agent/5.0.1:
    https-proxy-agent: private
  /human-signals/2.1.0:
    human-signals: private
  /iconv-lite/0.4.24:
    iconv-lite: private
  /ieee754/1.2.1:
    ieee754: private
  /ignore/5.3.2:
    ignore: private
  /import-fresh/3.3.1:
    import-fresh: private
  /import-local/3.2.0:
    import-local: private
  /imurmurhash/0.1.4:
    imurmurhash: private
  /indent-string/4.0.0:
    indent-string: private
  /inflight/1.0.6:
    inflight: private
  /inherits/2.0.4:
    inherits: private
  /ini/1.3.8:
    ini: private
  /inquirer/8.2.6:
    inquirer: private
  /internal-slot/1.1.0:
    internal-slot: private
  /internmap/2.0.3:
    internmap: private
  /ip-address/9.0.5:
    ip-address: private
  /is-arguments/1.2.0:
    is-arguments: private
  /is-array-buffer/3.0.5:
    is-array-buffer: private
  /is-arrayish/0.2.1:
    is-arrayish: private
  /is-async-function/2.1.1:
    is-async-function: private
  /is-bigint/1.1.0:
    is-bigint: private
  /is-binary-path/2.1.0:
    is-binary-path: private
  /is-boolean-object/1.2.2:
    is-boolean-object: private
  /is-bun-module/2.0.0:
    is-bun-module: private
  /is-callable/1.2.7:
    is-callable: private
  /is-core-module/2.16.1:
    is-core-module: private
  /is-data-view/1.0.2:
    is-data-view: private
  /is-date-object/1.1.0:
    is-date-object: private
  /is-extglob/2.1.1:
    is-extglob: private
  /is-finalizationregistry/1.1.1:
    is-finalizationregistry: private
  /is-fullwidth-code-point/3.0.0:
    is-fullwidth-code-point: private
  /is-generator-fn/2.1.0:
    is-generator-fn: private
  /is-generator-function/1.1.0:
    is-generator-function: private
  /is-glob/4.0.3:
    is-glob: private
  /is-interactive/1.0.0:
    is-interactive: private
  /is-lower-case/1.1.3:
    is-lower-case: private
  /is-map/2.0.3:
    is-map: private
  /is-negative-zero/2.0.3:
    is-negative-zero: private
  /is-number-object/1.1.1:
    is-number-object: private
  /is-number/7.0.0:
    is-number: private
  /is-obj/2.0.0:
    is-obj: private
  /is-path-cwd/2.2.0:
    is-path-cwd: private
  /is-path-inside/3.0.3:
    is-path-inside: private
  /is-plain-obj/1.1.0:
    is-plain-obj: private
  /is-plain-object/5.0.0:
    is-plain-object: private
  /is-potential-custom-element-name/1.0.1:
    is-potential-custom-element-name: private
  /is-regex/1.2.1:
    is-regex: private
  /is-set/2.0.3:
    is-set: private
  /is-shared-array-buffer/1.0.4:
    is-shared-array-buffer: private
  /is-stream/2.0.1:
    is-stream: private
  /is-string/1.1.1:
    is-string: private
  /is-symbol/1.1.1:
    is-symbol: private
  /is-text-path/2.0.0:
    is-text-path: private
  /is-typed-array/1.1.15:
    is-typed-array: private
  /is-unicode-supported/0.1.0:
    is-unicode-supported: private
  /is-upper-case/1.1.2:
    is-upper-case: private
  /is-weakmap/2.0.2:
    is-weakmap: private
  /is-weakref/1.1.1:
    is-weakref: private
  /is-weakset/2.0.4:
    is-weakset: private
  /isarray/2.0.5:
    isarray: private
  /isbinaryfile/4.0.10:
    isbinaryfile: private
  /isexe/2.0.0:
    isexe: private
  /isows/1.0.7(ws@8.18.3):
    isows: private
  /istanbul-lib-coverage/3.2.2:
    istanbul-lib-coverage: private
  /istanbul-lib-instrument/6.0.3:
    istanbul-lib-instrument: private
  /istanbul-lib-report/3.0.1:
    istanbul-lib-report: private
  /istanbul-lib-source-maps/4.0.1:
    istanbul-lib-source-maps: private
  /istanbul-reports/3.1.7:
    istanbul-reports: private
  /iterator.prototype/1.1.5:
    iterator.prototype: private
  /jackspeak/2.3.6:
    jackspeak: private
  /jake/10.9.2:
    jake: private
  /jest-changed-files/29.7.0:
    jest-changed-files: private
  /jest-circus/29.7.0:
    jest-circus: private
  /jest-cli/29.7.0(@types/node@20.19.4):
    jest-cli: private
  /jest-config/29.7.0(@types/node@20.19.4):
    jest-config: private
  /jest-diff/29.7.0:
    jest-diff: private
  /jest-docblock/29.7.0:
    jest-docblock: private
  /jest-each/29.7.0:
    jest-each: private
  /jest-environment-jsdom/29.7.0:
    jest-environment-jsdom: private
  /jest-environment-node/29.7.0:
    jest-environment-node: private
  /jest-get-type/29.6.3:
    jest-get-type: private
  /jest-haste-map/29.7.0:
    jest-haste-map: private
  /jest-leak-detector/29.7.0:
    jest-leak-detector: private
  /jest-matcher-utils/29.7.0:
    jest-matcher-utils: private
  /jest-message-util/29.7.0:
    jest-message-util: private
  /jest-mock/29.7.0:
    jest-mock: private
  /jest-pnp-resolver/1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  /jest-regex-util/29.6.3:
    jest-regex-util: private
  /jest-resolve-dependencies/29.7.0:
    jest-resolve-dependencies: private
  /jest-resolve/29.7.0:
    jest-resolve: private
  /jest-runner/29.7.0:
    jest-runner: private
  /jest-runtime/29.7.0:
    jest-runtime: private
  /jest-snapshot/29.7.0:
    jest-snapshot: private
  /jest-util/29.7.0:
    jest-util: private
  /jest-validate/29.7.0:
    jest-validate: private
  /jest-watcher/29.7.0:
    jest-watcher: private
  /jest-worker/29.7.0:
    jest-worker: private
  /jest/29.7.0(@types/node@20.19.4):
    jest: private
  /jiti/1.21.7:
    jiti: private
  /js-tokens/4.0.0:
    js-tokens: private
  /js-yaml/4.1.0:
    js-yaml: private
  /jsbn/1.1.0:
    jsbn: private
  /jsdom/20.0.3:
    jsdom: private
  /jsesc/3.1.0:
    jsesc: private
  /json-buffer/3.0.1:
    json-buffer: private
  /json-parse-even-better-errors/2.3.1:
    json-parse-even-better-errors: private
  /json-schema-traverse/0.4.1:
    json-schema-traverse: private
  /json-stable-stringify-without-jsonify/1.0.1:
    json-stable-stringify-without-jsonify: private
  /json-stringify-safe/5.0.1:
    json-stringify-safe: private
  /json5/2.2.3:
    json5: private
  /jsonfile/6.1.0:
    jsonfile: private
  /jsonparse/1.3.1:
    jsonparse: private
  /jsx-ast-utils/3.3.5:
    jsx-ast-utils: private
  /keyv/4.5.4:
    keyv: private
  /kind-of/6.0.3:
    kind-of: private
  /kleur/3.0.3:
    kleur: private
  /language-subtag-registry/0.3.23:
    language-subtag-registry: private
  /language-tags/1.0.9:
    language-tags: private
  /leven/3.1.0:
    leven: private
  /levn/0.4.1:
    levn: private
  /lilconfig/3.1.3:
    lilconfig: private
  /lines-and-columns/1.2.4:
    lines-and-columns: private
  /listr2/8.3.3:
    listr2: private
  /locate-path/6.0.0:
    locate-path: private
  /lodash.camelcase/4.3.0:
    lodash.camelcase: private
  /lodash.get/4.4.2:
    lodash.get: private
  /lodash.isfunction/3.0.9:
    lodash.isfunction: private
  /lodash.isplainobject/4.0.6:
    lodash.isplainobject: private
  /lodash.kebabcase/4.1.1:
    lodash.kebabcase: private
  /lodash.memoize/4.1.2:
    lodash.memoize: private
  /lodash.merge/4.6.2:
    lodash.merge: private
  /lodash.mergewith/4.6.2:
    lodash.mergewith: private
  /lodash.snakecase/4.1.1:
    lodash.snakecase: private
  /lodash.startcase/4.4.0:
    lodash.startcase: private
  /lodash.uniq/4.5.0:
    lodash.uniq: private
  /lodash.upperfirst/4.3.1:
    lodash.upperfirst: private
  /lodash/4.17.21:
    lodash: private
  /log-symbols/3.0.0:
    log-symbols: private
  /log-update/6.1.0:
    log-update: private
  /loose-envify/1.4.0:
    loose-envify: private
  /lower-case-first/1.0.2:
    lower-case-first: private
  /lower-case/1.1.4:
    lower-case: private
  /lru-cache/7.18.3:
    lru-cache: private
  /lucide-react/0.303.0(react@18.3.1):
    lucide-react: private
  /lz-string/1.5.0:
    lz-string: private
  /make-dir/4.0.0:
    make-dir: private
  /make-error/1.3.6:
    make-error: private
  /makeerror/1.0.12:
    makeerror: private
  /map-obj/4.3.0:
    map-obj: private
  /math-intrinsics/1.1.0:
    math-intrinsics: private
  /meow/12.1.1:
    meow: private
  /merge-stream/2.0.0:
    merge-stream: private
  /merge2/1.4.1:
    merge2: private
  /micromatch/4.0.8:
    micromatch: private
  /mime-db/1.52.0:
    mime-db: private
  /mime-types/2.1.35:
    mime-types: private
  /mimic-fn/2.1.0:
    mimic-fn: private
  /mimic-function/5.0.1:
    mimic-function: private
  /min-indent/1.0.1:
    min-indent: private
  /minimatch/9.0.5:
    minimatch: private
  /minimist-options/4.1.0:
    minimist-options: private
  /minimist/1.2.8:
    minimist: private
  /minipass/7.1.2:
    minipass: private
  /mkdirp/0.5.6:
    mkdirp: private
  /mrmime/2.0.1:
    mrmime: private
  /ms/2.1.3:
    ms: private
  /mute-stream/0.0.8:
    mute-stream: private
  /mz/2.7.0:
    mz: private
  /nanoid/3.3.11:
    nanoid: private
  /napi-postinstall/0.3.0:
    napi-postinstall: private
  /natural-compare/1.4.0:
    natural-compare: private
  /neo-async/2.6.2:
    neo-async: private
  /netmask/2.0.2:
    netmask: private
  /next/14.0.4(@babel/core@7.28.0)(react-dom@18.3.1)(react@18.3.1):
    next: private
  /no-case/2.3.2:
    no-case: private
  /node-int64/0.4.0:
    node-int64: private
  /node-plop/0.26.3:
    node-plop: private
  /node-releases/2.0.19:
    node-releases: private
  /normalize-package-data/6.0.2:
    normalize-package-data: private
  /normalize-path/3.0.0:
    normalize-path: private
  /normalize-range/0.1.2:
    normalize-range: private
  /npm-run-path/4.0.1:
    npm-run-path: private
  /nwsapi/2.2.20:
    nwsapi: private
  /object-assign/4.1.1:
    object-assign: private
  /object-hash/3.0.0:
    object-hash: private
  /object-inspect/1.13.4:
    object-inspect: private
  /object-is/1.1.6:
    object-is: private
  /object-keys/1.1.1:
    object-keys: private
  /object.assign/4.1.7:
    object.assign: private
  /object.entries/1.1.9:
    object.entries: private
  /object.fromentries/2.0.8:
    object.fromentries: private
  /object.groupby/1.0.3:
    object.groupby: private
  /object.values/1.2.1:
    object.values: private
  /once/1.4.0:
    once: private
  /onetime/5.1.2:
    onetime: private
  /opener/1.5.2:
    opener: private
  /optionator/0.9.4:
    optionator: private
  /ora/4.1.1:
    ora: private
  /os-tmpdir/1.0.2:
    os-tmpdir: private
  /own-keys/1.0.1:
    own-keys: private
  /p-limit/3.1.0:
    p-limit: private
  /p-locate/5.0.0:
    p-locate: private
  /p-map/3.0.0:
    p-map: private
  /p-try/2.2.0:
    p-try: private
  /pac-proxy-agent/7.2.0:
    pac-proxy-agent: private
  /pac-resolver/7.0.1:
    pac-resolver: private
  /package-json-from-dist/1.0.1:
    package-json-from-dist: private
  /param-case/2.1.1:
    param-case: private
  /parent-module/1.0.1:
    parent-module: private
  /parse-json/5.2.0:
    parse-json: private
  /parse5/7.3.0:
    parse5: private
  /pascal-case/2.0.1:
    pascal-case: private
  /path-case/2.1.1:
    path-case: private
  /path-exists/4.0.0:
    path-exists: private
  /path-is-absolute/1.0.1:
    path-is-absolute: private
  /path-key/3.1.1:
    path-key: private
  /path-parse/1.0.7:
    path-parse: private
  /path-scurry/1.11.1:
    path-scurry: private
  /path-type/4.0.0:
    path-type: private
  /picocolors/1.1.1:
    picocolors: private
  /picomatch/2.3.1:
    picomatch: private
  /pidtree/0.6.0:
    pidtree: private
  /pify/2.3.0:
    pify: private
  /pirates/4.0.7:
    pirates: private
  /pkg-dir/4.2.0:
    pkg-dir: private
  /possible-typed-array-names/1.1.0:
    possible-typed-array-names: private
  /postcss-import/15.1.0(postcss@8.5.6):
    postcss-import: private
  /postcss-js/4.0.1(postcss@8.5.6):
    postcss-js: private
  /postcss-load-config/4.0.2(postcss@8.5.6):
    postcss-load-config: private
  /postcss-nested/6.2.0(postcss@8.5.6):
    postcss-nested: private
  /postcss-selector-parser/6.1.2:
    postcss-selector-parser: private
  /postcss-value-parser/4.2.0:
    postcss-value-parser: private
  /postcss/8.5.6:
    postcss: private
  /prelude-ls/1.2.1:
    prelude-ls: private
  /pretty-format/29.7.0:
    pretty-format: private
  /prompts/2.4.2:
    prompts: private
  /prop-types/15.8.1:
    prop-types: private
  /proxy-agent/6.5.0:
    proxy-agent: private
  /proxy-from-env/1.1.0:
    proxy-from-env: private
  /psl/1.15.0:
    psl: private
  /punycode/2.3.1:
    punycode: private
  /pure-rand/6.1.0:
    pure-rand: private
  /querystringify/2.2.0:
    querystringify: private
  /queue-microtask/1.2.3:
    queue-microtask: private
  /quick-lru/4.0.1:
    quick-lru: private
  /ramda/0.29.1:
    ramda: private
  /rc/1.2.8:
    rc: private
  /react-day-picker/8.10.1(date-fns@3.6.0)(react@18.3.1):
    react-day-picker: private
  /react-dom/18.3.1(react@18.3.1):
    react-dom: private
  /react-hook-form/7.60.0(react@18.3.1):
    react-hook-form: private
  /react-is/18.3.1:
    react-is: private
  /react-remove-scroll-bar/2.3.8(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll-bar: private
  /react-remove-scroll/2.7.1(@types/react@18.3.23)(react@18.3.1):
    react-remove-scroll: private
  /react-smooth/4.0.4(react-dom@18.3.1)(react@18.3.1):
    react-smooth: private
  /react-style-singleton/2.2.3(@types/react@18.3.23)(react@18.3.1):
    react-style-singleton: private
  /react-transition-group/4.4.5(react-dom@18.3.1)(react@18.3.1):
    react-transition-group: private
  /react-transition-state/2.3.1(react-dom@18.3.1)(react@18.3.1):
    react-transition-state: private
  /react/18.3.1:
    react: private
  /read-cache/1.0.0:
    read-cache: private
  /read-pkg-up/10.1.0:
    read-pkg-up: private
  /read-pkg/8.1.0:
    read-pkg: private
  /readable-stream/3.6.2:
    readable-stream: private
  /readdirp/3.6.0:
    readdirp: private
  /recharts-scale/0.4.5:
    recharts-scale: private
  /recharts/2.15.4(react-dom@18.3.1)(react@18.3.1):
    recharts: private
  /redent/3.0.0:
    redent: private
  /reflect.getprototypeof/1.0.10:
    reflect.getprototypeof: private
  /regexp.prototype.flags/1.5.4:
    regexp.prototype.flags: private
  /registry-auth-token/3.3.2:
    registry-auth-token: private
  /registry-url/3.1.0:
    registry-url: private
  /require-directory/2.1.1:
    require-directory: private
  /require-from-string/2.0.2:
    require-from-string: private
  /requires-port/1.0.0:
    requires-port: private
  /resolve-cwd/3.0.0:
    resolve-cwd: private
  /resolve-from/5.0.0:
    resolve-from: private
  /resolve-global/1.0.0:
    resolve-global: private
  /resolve-pkg-maps/1.0.0:
    resolve-pkg-maps: private
  /resolve.exports/2.0.3:
    resolve.exports: private
  /resolve/2.0.0-next.5:
    resolve: private
  /restore-cursor/3.1.0:
    restore-cursor: private
  /reusify/1.1.0:
    reusify: private
  /rfdc/1.4.1:
    rfdc: private
  /rimraf/3.0.2:
    rimraf: private
  /run-async/2.4.1:
    run-async: private
  /run-parallel/1.2.0:
    run-parallel: private
  /rxjs/7.8.2:
    rxjs: private
  /safe-array-concat/1.1.3:
    safe-array-concat: private
  /safe-buffer/5.2.1:
    safe-buffer: private
  /safe-push-apply/1.0.0:
    safe-push-apply: private
  /safe-regex-test/1.1.0:
    safe-regex-test: private
  /safer-buffer/2.1.2:
    safer-buffer: private
  /saxes/6.0.0:
    saxes: private
  /scheduler/0.23.2:
    scheduler: private
  /semver/7.7.2:
    semver: private
  /sentence-case/2.1.1:
    sentence-case: private
  /set-function-length/1.2.2:
    set-function-length: private
  /set-function-name/2.0.2:
    set-function-name: private
  /set-proto/1.0.0:
    set-proto: private
  /shebang-command/2.0.0:
    shebang-command: private
  /shebang-regex/3.0.0:
    shebang-regex: private
  /side-channel-list/1.0.0:
    side-channel-list: private
  /side-channel-map/1.0.1:
    side-channel-map: private
  /side-channel-weakmap/1.0.2:
    side-channel-weakmap: private
  /side-channel/1.1.0:
    side-channel: private
  /signal-exit/3.0.7:
    signal-exit: private
  /sirv/2.0.4:
    sirv: private
  /sisteransi/1.0.5:
    sisteransi: private
  /slash/3.0.0:
    slash: private
  /slice-ansi/5.0.0:
    slice-ansi: private
  /smart-buffer/4.2.0:
    smart-buffer: private
  /snake-case/2.1.0:
    snake-case: private
  /socks-proxy-agent/8.0.5:
    socks-proxy-agent: private
  /socks/2.8.5:
    socks: private
  /source-map-js/1.2.1:
    source-map-js: private
  /source-map-support/0.5.13:
    source-map-support: private
  /source-map/0.6.1:
    source-map: private
  /spdx-correct/3.2.0:
    spdx-correct: private
  /spdx-exceptions/2.5.0:
    spdx-exceptions: private
  /spdx-expression-parse/3.0.1:
    spdx-expression-parse: private
  /spdx-license-ids/3.0.21:
    spdx-license-ids: private
  /split2/3.2.2:
    split2: private
  /sprintf-js/1.1.3:
    sprintf-js: private
  /stable-hash/0.0.5:
    stable-hash: private
  /stack-utils/2.0.6:
    stack-utils: private
  /stop-iteration-iterator/1.1.0:
    stop-iteration-iterator: private
  /streamsearch/1.1.0:
    streamsearch: private
  /string-argv/0.3.2:
    string-argv: private
  /string-length/4.0.2:
    string-length: private
  /string-width/4.2.3:
    string-width: private
    string-width-cjs: private
  /string.prototype.includes/2.0.1:
    string.prototype.includes: private
  /string.prototype.matchall/4.0.12:
    string.prototype.matchall: private
  /string.prototype.repeat/1.0.0:
    string.prototype.repeat: private
  /string.prototype.trim/1.2.10:
    string.prototype.trim: private
  /string.prototype.trimend/1.0.9:
    string.prototype.trimend: private
  /string.prototype.trimstart/1.0.8:
    string.prototype.trimstart: private
  /string_decoder/1.3.0:
    string_decoder: private
  /strip-ansi/6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  /strip-bom/3.0.0:
    strip-bom: private
  /strip-final-newline/2.0.0:
    strip-final-newline: private
  /strip-indent/3.0.0:
    strip-indent: private
  /strip-json-comments/3.1.1:
    strip-json-comments: private
  /styled-jsx/5.1.1(@babel/core@7.28.0)(react@18.3.1):
    styled-jsx: private
  /sucrase/3.35.0:
    sucrase: private
  /supports-color/5.5.0:
    supports-color: private
  /supports-preserve-symlinks-flag/1.0.0:
    supports-preserve-symlinks-flag: private
  /swap-case/1.1.2:
    swap-case: private
  /symbol-tree/3.2.4:
    symbol-tree: private
  /tabbable/6.2.0:
    tabbable: private
  /tailwind-merge/2.6.0:
    tailwind-merge: private
  /tailwindcss-animate/1.0.7(tailwindcss@3.4.17):
    tailwindcss-animate: private
  /tailwindcss/3.4.17:
    tailwindcss: private
  /temp-dir/3.0.0:
    temp-dir: private
  /tempfile/5.0.0:
    tempfile: private
  /test-exclude/6.0.0:
    test-exclude: private
  /text-extensions/2.4.0:
    text-extensions: private
  /text-table/0.2.0:
    text-table: private
  /thenify-all/1.6.0:
    thenify-all: private
  /thenify/3.3.1:
    thenify: private
  /through/2.3.8:
    through: private
  /through2/4.0.2:
    through2: private
  /tiny-invariant/1.3.3:
    tiny-invariant: private
  /tinycolor2/1.6.0:
    tinycolor2: private
  /tinyglobby/0.2.14:
    tinyglobby: private
  /tinygradient/1.1.5:
    tinygradient: private
  /title-case/2.1.1:
    title-case: private
  /tmp/0.0.33:
    tmp: private
  /tmpl/1.0.5:
    tmpl: private
  /to-regex-range/5.0.1:
    to-regex-range: private
  /totalist/3.0.1:
    totalist: private
  /tough-cookie/4.1.4:
    tough-cookie: private
  /tr46/3.0.0:
    tr46: private
  /trim-newlines/3.0.1:
    trim-newlines: private
  /ts-api-utils/1.4.3(typescript@5.8.3):
    ts-api-utils: private
  /ts-interface-checker/0.1.13:
    ts-interface-checker: private
  /ts-jest/29.4.0(@babel/core@7.28.0)(jest@29.7.0)(typescript@5.8.3):
    ts-jest: private
  /ts-node/10.9.2(@types/node@20.19.4)(typescript@5.8.3):
    ts-node: private
  /tsconfig-paths/3.15.0:
    tsconfig-paths: private
  /tslib/2.8.1:
    tslib: private
  /tsx/4.20.3:
    tsx: private
  /turbo-darwin-64/1.13.4:
    turbo-darwin-64: private
  /turbo-darwin-arm64/1.13.4:
    turbo-darwin-arm64: private
  /turbo-linux-64/1.13.4:
    turbo-linux-64: private
  /turbo-linux-arm64/1.13.4:
    turbo-linux-arm64: private
  /turbo-windows-64/1.13.4:
    turbo-windows-64: private
  /turbo-windows-arm64/1.13.4:
    turbo-windows-arm64: private
  /type-check/0.4.0:
    type-check: private
  /type-detect/4.0.8:
    type-detect: private
  /type-fest/4.41.0:
    type-fest: private
  /typed-array-buffer/1.0.3:
    typed-array-buffer: private
  /typed-array-byte-length/1.0.3:
    typed-array-byte-length: private
  /typed-array-byte-offset/1.0.4:
    typed-array-byte-offset: private
  /typed-array-length/1.0.7:
    typed-array-length: private
  /uglify-js/3.19.3:
    uglify-js: private
  /unbox-primitive/1.1.0:
    unbox-primitive: private
  /undici-types/6.21.0:
    undici-types: private
  /universalify/2.0.1:
    universalify: private
  /unrs-resolver/1.11.0:
    unrs-resolver: private
  /update-browserslist-db/1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  /update-check/1.5.4:
    update-check: private
  /upper-case-first/1.1.2:
    upper-case-first: private
  /upper-case/1.1.3:
    upper-case: private
  /uri-js/4.4.1:
    uri-js: private
  /url-parse/1.5.10:
    url-parse: private
  /use-callback-ref/1.3.3(@types/react@18.3.23)(react@18.3.1):
    use-callback-ref: private
  /use-sidecar/1.1.3(@types/react@18.3.23)(react@18.3.1):
    use-sidecar: private
  /use-sync-external-store/1.5.0(react@18.3.1):
    use-sync-external-store: private
  /util-deprecate/1.0.2:
    util-deprecate: private
  /v8-compile-cache-lib/3.0.1:
    v8-compile-cache-lib: private
  /v8-to-istanbul/9.3.0:
    v8-to-istanbul: private
  /validate-npm-package-license/3.0.4:
    validate-npm-package-license: private
  /validate-npm-package-name/5.0.1:
    validate-npm-package-name: private
  /victory-vendor/36.9.2:
    victory-vendor: private
  /w3c-xmlserializer/4.0.0:
    w3c-xmlserializer: private
  /walker/1.0.8:
    walker: private
  /watchpack/2.4.0:
    watchpack: private
  /wcwidth/1.0.1:
    wcwidth: private
  /webidl-conversions/7.0.0:
    webidl-conversions: private
  /webpack-bundle-analyzer/4.10.1:
    webpack-bundle-analyzer: private
  /whatwg-encoding/2.0.0:
    whatwg-encoding: private
  /whatwg-mimetype/3.0.0:
    whatwg-mimetype: private
  /whatwg-url/5.0.0:
    whatwg-url: private
  /which-boxed-primitive/1.1.1:
    which-boxed-primitive: private
  /which-builtin-type/1.2.1:
    which-builtin-type: private
  /which-collection/1.0.2:
    which-collection: private
  /which-typed-array/1.1.19:
    which-typed-array: private
  /which/2.0.2:
    which: private
  /word-wrap/1.2.5:
    word-wrap: private
  /wordwrap/1.0.0:
    wordwrap: private
  /wrap-ansi/6.2.0:
    wrap-ansi: private
  /wrap-ansi/7.0.0:
    wrap-ansi-cjs: private
  /wrappy/1.0.2:
    wrappy: private
  /write-file-atomic/4.0.2:
    write-file-atomic: private
  /ws/8.18.3:
    ws: private
  /xml-name-validator/4.0.0:
    xml-name-validator: private
  /xmlchars/2.2.0:
    xmlchars: private
  /y18n/5.0.8:
    y18n: private
  /yallist/3.1.1:
    yallist: private
  /yaml/2.8.0:
    yaml: private
  /yargs-parser/21.1.1:
    yargs-parser: private
  /yargs/17.7.2:
    yargs: private
  /yn/3.1.1:
    yn: private
  /yocto-queue/0.1.0:
    yocto-queue: private
  /zod/3.25.75:
    zod: private
  /zustand/4.5.7(@types/react@18.3.23)(react@18.3.1):
    zustand: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@8.15.0
pendingBuilds: []
prunedAt: Mon, 07 Jul 2025 16:13:06 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - /@emnapi/core/1.4.4
  - /@emnapi/runtime/1.4.4
  - /@emnapi/wasi-threads/1.0.3
  - /@esbuild/aix-ppc64/0.25.5
  - /@esbuild/android-arm/0.25.5
  - /@esbuild/android-arm64/0.25.5
  - /@esbuild/android-x64/0.25.5
  - /@esbuild/darwin-x64/0.25.5
  - /@esbuild/freebsd-arm64/0.25.5
  - /@esbuild/freebsd-x64/0.25.5
  - /@esbuild/linux-arm/0.25.5
  - /@esbuild/linux-arm64/0.25.5
  - /@esbuild/linux-ia32/0.25.5
  - /@esbuild/linux-loong64/0.25.5
  - /@esbuild/linux-mips64el/0.25.5
  - /@esbuild/linux-ppc64/0.25.5
  - /@esbuild/linux-riscv64/0.25.5
  - /@esbuild/linux-s390x/0.25.5
  - /@esbuild/linux-x64/0.25.5
  - /@esbuild/netbsd-arm64/0.25.5
  - /@esbuild/netbsd-x64/0.25.5
  - /@esbuild/openbsd-arm64/0.25.5
  - /@esbuild/openbsd-x64/0.25.5
  - /@esbuild/sunos-x64/0.25.5
  - /@esbuild/win32-arm64/0.25.5
  - /@esbuild/win32-ia32/0.25.5
  - /@esbuild/win32-x64/0.25.5
  - /@napi-rs/wasm-runtime/0.2.11
  - /@next/swc-darwin-x64/14.0.4
  - /@next/swc-linux-arm64-gnu/14.0.4
  - /@next/swc-linux-arm64-musl/14.0.4
  - /@next/swc-linux-x64-gnu/14.0.4
  - /@next/swc-linux-x64-musl/14.0.4
  - /@next/swc-win32-arm64-msvc/14.0.4
  - /@next/swc-win32-ia32-msvc/14.0.4
  - /@next/swc-win32-x64-msvc/14.0.4
  - /@tybys/wasm-util/0.9.0
  - /@unrs/resolver-binding-android-arm-eabi/1.11.0
  - /@unrs/resolver-binding-android-arm64/1.11.0
  - /@unrs/resolver-binding-darwin-x64/1.11.0
  - /@unrs/resolver-binding-freebsd-x64/1.11.0
  - /@unrs/resolver-binding-linux-arm-gnueabihf/1.11.0
  - /@unrs/resolver-binding-linux-arm-musleabihf/1.11.0
  - /@unrs/resolver-binding-linux-arm64-gnu/1.11.0
  - /@unrs/resolver-binding-linux-arm64-musl/1.11.0
  - /@unrs/resolver-binding-linux-ppc64-gnu/1.11.0
  - /@unrs/resolver-binding-linux-riscv64-gnu/1.11.0
  - /@unrs/resolver-binding-linux-riscv64-musl/1.11.0
  - /@unrs/resolver-binding-linux-s390x-gnu/1.11.0
  - /@unrs/resolver-binding-linux-x64-gnu/1.11.0
  - /@unrs/resolver-binding-linux-x64-musl/1.11.0
  - /@unrs/resolver-binding-wasm32-wasi/1.11.0
  - /@unrs/resolver-binding-win32-arm64-msvc/1.11.0
  - /@unrs/resolver-binding-win32-ia32-msvc/1.11.0
  - /@unrs/resolver-binding-win32-x64-msvc/1.11.0
  - /turbo-darwin-64/1.13.4
  - /turbo-linux-64/1.13.4
  - /turbo-linux-arm64/1.13.4
  - /turbo-windows-64/1.13.4
  - /turbo-windows-arm64/1.13.4
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
