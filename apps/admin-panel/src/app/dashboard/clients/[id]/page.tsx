'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { CustomerQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'

interface CustomerDetail {
  id: string
  customer_type: 'retail' | 'wholesale'
  first_name: string | null
  last_name: string | null
  company_name: string | null
  email: string | null
  phone: string | null
  address_line1: string | null
  address_line2: string | null
  city: string | null
  postal_code: string | null
  country: string
  tax_id: string | null
  business_license: string | null
  loyalty_points: number
  store_credit: number
  membership_tier: string
  membership_expires_at: string | null
  assigned_coach_id: string | null
  is_active: boolean
  created_at: string
  updated_at: string
  coaches?: {
    id: string
    first_name: string
    last_name: string
    email: string
  } | null
}

export default function CustomerDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [customer, setCustomer] = useState<CustomerDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (params.id) {
      loadCustomer(params.id as string)
    }
  }, [params.id])

  const loadCustomer = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      const customerQueries = new CustomerQueries()
      const data = await customerQueries.getById(id)
      setCustomer(data)
    } catch (err) {
      console.error('Failed to load customer:', err)
      setError(err instanceof Error ? err.message : 'Failed to load customer')
    } finally {
      setLoading(false)
    }
  }

  const getCustomerName = (customer: CustomerDetail) => {
    if (customer.customer_type === 'wholesale' && customer.company_name) {
      return customer.company_name
    }
    return `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || 'Unnamed Customer'
  }

  const getMembershipBadgeColor = (tier: string) => {
    switch (tier) {
      case 'premium': return 'bg-purple-100 text-purple-800'
      case 'regular': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getCustomerTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'wholesale': return 'bg-green-100 text-green-800'
      case 'retail': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Loading customer...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-700">{error || 'Customer not found'}</p>
              <Button 
                variant="outline" 
                onClick={() => router.push('/customers')}
                className="mt-4"
              >
                Back to Customers
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <Button 
                variant="outline" 
                onClick={() => router.push('/customers')}
                className="mb-4"
              >
                ← Back to Customers
              </Button>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                {getCustomerName(customer)}
              </h1>
              <div className="flex items-center gap-2 mt-2">
                <span className={`text-sm px-3 py-1 rounded-full ${getCustomerTypeBadgeColor(customer.customer_type)}`}>
                  {customer.customer_type} customer
                </span>
                <span className={`text-sm px-3 py-1 rounded ${getMembershipBadgeColor(customer.membership_tier)}`}>
                  {customer.membership_tier} member
                </span>
                {!customer.is_active && (
                  <span className="text-sm px-3 py-1 rounded bg-red-100 text-red-800">
                    Inactive
                  </span>
                )}
              </div>
            </div>
            <div className="mt-4 sm:mt-0 flex gap-2">
              <Button variant="outline">
                Edit Customer
              </Button>
              <Button>
                New Order
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Customer Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {customer.customer_type === 'retail' ? (
                    <>
                      <div>
                        <label className="text-sm font-medium text-gray-700">First Name</label>
                        <p className="text-gray-900">{customer.first_name || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-700">Last Name</label>
                        <p className="text-gray-900">{customer.last_name || 'N/A'}</p>
                      </div>
                    </>
                  ) : (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-700">Company Name</label>
                      <p className="text-gray-900">{customer.company_name || 'N/A'}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-700">Email</label>
                    <p className="text-gray-900">{customer.email || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Phone</label>
                    <p className="text-gray-900">{customer.phone || 'N/A'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Address Information */}
            <Card>
              <CardHeader>
                <CardTitle>Address Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-gray-700">Address Line 1</label>
                    <p className="text-gray-900">{customer.address_line1 || 'N/A'}</p>
                  </div>
                  {customer.address_line2 && (
                    <div className="md:col-span-2">
                      <label className="text-sm font-medium text-gray-700">Address Line 2</label>
                      <p className="text-gray-900">{customer.address_line2}</p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-gray-700">City</label>
                    <p className="text-gray-900">{customer.city || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Postal Code</label>
                    <p className="text-gray-900">{customer.postal_code || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Country</label>
                    <p className="text-gray-900">{customer.country}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Business Information (for wholesale customers) */}
            {customer.customer_type === 'wholesale' && (
              <Card>
                <CardHeader>
                  <CardTitle>Business Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Tax ID</label>
                      <p className="text-gray-900">{customer.tax_id || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Business License</label>
                      <p className="text-gray-900">{customer.business_license || 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Order History Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Order history and purchase patterns</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <p className="text-sm">Order history will be displayed here</p>
                  <p className="text-xs mt-1">This feature will be implemented in the next phase</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Loyalty & Credits */}
            <Card>
              <CardHeader>
                <CardTitle>Loyalty & Credits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">{customer.loyalty_points}</div>
                  <p className="text-sm text-gray-600">Loyalty Points</p>
                </div>
                
                {customer.store_credit > 0 && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      AWG {customer.store_credit.toFixed(2)}
                    </div>
                    <p className="text-sm text-gray-600">Store Credit</p>
                  </div>
                )}

                <div className="pt-4 border-t">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Membership</span>
                    <span className={`text-sm px-2 py-1 rounded ${getMembershipBadgeColor(customer.membership_tier)}`}>
                      {customer.membership_tier}
                    </span>
                  </div>
                  
                  {customer.membership_expires_at && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Expires</span>
                      <span className="text-sm text-gray-900">
                        {new Date(customer.membership_expires_at).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Coach Assignment */}
            {customer.coaches && (
              <Card>
                <CardHeader>
                  <CardTitle>Assigned Coach</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <span className="text-blue-600 font-semibold text-lg">
                        {customer.coaches.first_name[0]}{customer.coaches.last_name[0]}
                      </span>
                    </div>
                    <h3 className="font-medium text-gray-900">
                      {customer.coaches.first_name} {customer.coaches.last_name}
                    </h3>
                    <p className="text-sm text-gray-600">{customer.coaches.email}</p>
                    <Button variant="outline" size="sm" className="mt-3">
                      Contact Coach
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Customer Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Customer Since</span>
                  <span className="text-sm text-gray-900">
                    {new Date(customer.created_at).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Last Updated</span>
                  <span className="text-sm text-gray-900">
                    {new Date(customer.updated_at).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <span className={`text-sm ${customer.is_active ? 'text-green-600' : 'text-red-600'}`}>
                    {customer.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Customer ID</span>
                  <span className="text-xs text-gray-500 font-mono">{customer.id.slice(0, 8)}...</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  📧 Send Email
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  🎁 Add Loyalty Points
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  💳 Add Store Credit
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  📊 View Analytics
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
