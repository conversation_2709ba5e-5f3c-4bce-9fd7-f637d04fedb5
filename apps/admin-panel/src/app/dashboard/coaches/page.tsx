'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'

interface Coach {
  id: string
  user_id: string | null
  first_name: string
  last_name: string
  email: string
  phone: string | null
  monthly_credit_amount: number
  current_credit_balance: number
  referral_percentage: number
  total_referrals: number
  total_sales: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export default function CoachesPage() {
  const router = useRouter()
  const [coaches, setCoaches] = useState<Coach[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadCoaches()
  }, [])

  const loadCoaches = async () => {
    try {
      setLoading(true)
      setError(null)
      const supabase = createClient()
      
      const { data, error: queryError } = await supabase
        .from('coaches')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (queryError) throw queryError
      setCoaches(data || [])
    } catch (err) {
      console.error('Failed to load coaches:', err)
      setError(err instanceof Error ? err.message : 'Failed to load coaches')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadCoaches()
      return
    }

    try {
      setLoading(true)
      setError(null)
      const supabase = createClient()
      
      const { data, error: queryError } = await supabase
        .from('coaches')
        .select('*')
        .or(`first_name.ilike.%${searchQuery}%,last_name.ilike.%${searchQuery}%,email.ilike.%${searchQuery}%`)
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (queryError) throw queryError
      setCoaches(data || [])
    } catch (err) {
      console.error('Search failed:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  const getPerformanceLevel = (totalSales: number) => {
    if (totalSales >= 10000) return { level: 'Excellent', color: 'text-green-600', bgColor: 'bg-green-100' }
    if (totalSales >= 5000) return { level: 'Good', color: 'text-blue-600', bgColor: 'bg-blue-100' }
    if (totalSales >= 1000) return { level: 'Average', color: 'text-yellow-600', bgColor: 'bg-yellow-100' }
    return { level: 'New', color: 'text-gray-600', bgColor: 'bg-gray-100' }
  }

  if (loading && coaches.length === 0) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-4">Loading coaches...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Coach Program</h1>
              <p className="text-gray-600 mt-1">Manage coaches, credits, and referral performance</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Button className="w-full sm:w-auto">
                Add Coach
              </Button>
            </div>
          </div>
        </div>

        {/* Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search Coaches</CardTitle>
            <CardDescription>Find coaches by name or email</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search coaches..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? 'Searching...' : 'Search'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('')
                    loadCoaches()
                  }}
                >
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-700">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Coaches Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {coaches.map((coach) => {
            const performance = getPerformanceLevel(coach.total_sales)
            return (
              <Card key={coach.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg">
                        {coach.first_name} {coach.last_name}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {coach.email}
                      </CardDescription>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded ${performance.bgColor} ${performance.color}`}>
                      {performance.level}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Contact Info */}
                    {coach.phone && (
                      <div className="text-sm text-gray-600">
                        📞 {coach.phone}
                      </div>
                    )}

                    {/* Credits & Performance */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Monthly Credit</p>
                        <p className="font-semibold">AWG {coach.monthly_credit_amount.toFixed(2)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Current Balance</p>
                        <p className="font-semibold">AWG {coach.current_credit_balance.toFixed(2)}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Referral Rate</p>
                        <p className="font-semibold">{coach.referral_percentage}%</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Total Referrals</p>
                        <p className="font-semibold">{coach.total_referrals}</p>
                      </div>
                    </div>

                    {/* Total Sales */}
                    <div className="pt-3 border-t">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Total Sales</span>
                        <span className="font-semibold text-lg">AWG {coach.total_sales.toFixed(2)}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        Edit
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1"
                        onClick={() => router.push(`/coaches/${coach.id}`)}
                      >
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Empty State */}
        {!loading && coaches.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <div className="text-gray-500">
                <p className="text-lg font-medium mb-2">No coaches found</p>
                <p className="text-sm">
                  {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first coach'}
                </p>
              </div>
              <Button className="mt-4">
                Add Coach
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Summary Stats */}
        {!loading && coaches.length > 0 && (
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Program Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <p className="font-medium text-gray-900">{coaches.length}</p>
                  <p className="text-gray-600">Active Coaches</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    {coaches.reduce((sum, coach) => sum + coach.total_referrals, 0)}
                  </p>
                  <p className="text-gray-600">Total Referrals</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    AWG {coaches.reduce((sum, coach) => sum + coach.total_sales, 0).toFixed(2)}
                  </p>
                  <p className="text-gray-600">Total Sales</p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-900">
                    AWG {coaches.reduce((sum, coach) => sum + coach.current_credit_balance, 0).toFixed(2)}
                  </p>
                  <p className="text-gray-600">Total Credits</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
