'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'

export default function SalesByItemPage() {
  const sampleData = [
    { name: 'Gold Standard 100% Whey Protein 5lbs', sku: 'ON-WHEY-5LB', quantity: 12, revenue: 'AWG 2,279.88', percentage: 35.2 },
    { name: 'Nitro-Tech Whey Protein 2lbs', sku: 'MT-NITRO-2LB', quantity: 8, revenue: 'AWG 719.92', percentage: 22.1 },
    { name: 'Amino X BCAA Formula', sku: 'BSN-AMINO-30', quantity: 15, revenue: 'AWG 989.85', percentage: 18.7 },
    { name: 'Vitamin D3 2000 IU', sku: 'NOW-VIT-D3', quantity: 25, revenue: 'AWG 649.75', percentage: 12.3 },
    { name: 'Pre-Workout Extreme', sku: 'BSN-PRE-EXT', quantity: 6, revenue: 'AWG 359.94', percentage: 11.7 },
  ]

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Sales by Item</h1>
          <p className="text-gray-600 mt-1">Product performance and sales breakdown</p>
        </div>

        {/* Date Range Selector */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Date Range</CardTitle>
            <CardDescription>Select the period for your item sales report</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">From</label>
                <input 
                  type="date" 
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  defaultValue="2025-07-01"
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
                <input 
                  type="date" 
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  defaultValue="2025-07-08"
                />
              </div>
              <div className="flex items-end">
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                  Update Report
                </button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Items Sold</p>
                  <p className="text-2xl font-bold text-gray-900">66</p>
                </div>
                <div className="text-2xl">📦</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Products</p>
                  <p className="text-2xl font-bold text-gray-900">5</p>
                </div>
                <div className="text-2xl">🏷️</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average per Item</p>
                  <p className="text-2xl font-bold text-gray-900">AWG 97.99</p>
                </div>
                <div className="text-2xl">💰</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sales by Item Table */}
        <Card>
          <CardHeader>
            <CardTitle>Item Performance</CardTitle>
            <CardDescription>Detailed breakdown of sales by individual products</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-700">Product</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-700">SKU</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Qty Sold</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">Revenue</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">% of Total</th>
                  </tr>
                </thead>
                <tbody>
                  {sampleData.map((item, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <p className="font-medium text-gray-900">{item.name}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600 font-mono text-sm">{item.sku}</td>
                      <td className="py-3 px-4 text-right font-medium">{item.quantity}</td>
                      <td className="py-3 px-4 text-right font-medium">{item.revenue}</td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex items-center justify-end">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full" 
                              style={{ width: `${item.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium">{item.percentage}%</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Coming Soon Notice */}
        <div className="mt-8">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <span className="text-2xl mr-3">📊</span>
                <div>
                  <h3 className="font-semibold text-blue-900">Enhanced Item Analytics Coming Soon</h3>
                  <p className="text-blue-700 text-sm mt-1">
                    Advanced filtering, sorting, and export capabilities will be available in the next update.
                    Current data shown is sample data for demonstration.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
