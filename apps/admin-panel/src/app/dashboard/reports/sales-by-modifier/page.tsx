'use client'

import { Card, CardContent } from '@nutripro/ui'

export default function SalesByModifierPage() {
  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Sales by Modifier</h1>
          <p className="text-gray-600 mt-1">Product modifier and variant analysis</p>
        </div>
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <span className="text-2xl mr-3">🚧</span>
              <div>
                <h3 className="font-semibold text-blue-900">Report Coming Soon</h3>
                <p className="text-blue-700 text-sm mt-1">Analysis of product modifiers and variants.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
