'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { Button } from '@nutripro/ui'
import { useState } from 'react'
import { ChevronDown, ChevronRight } from 'lucide-react'

const navigation = [
  {
    name: 'Reports',
    href: '/dashboard',
    icon: '📊',
    hasSubmenu: true,
    submenu: [
      { name: 'Sales summary', href: '/dashboard/reports/sales-summary', icon: '📈' },
      { name: 'Sales by item', href: '/dashboard/reports/sales-by-item', icon: '📦' },
      { name: 'Sales by category', href: '/dashboard/reports/sales-by-category', icon: '📂' },
      { name: 'Sales by employee', href: '/dashboard/reports/sales-by-employee', icon: '👤' },
      { name: 'Sales by payment type', href: '/dashboard/reports/sales-by-payment', icon: '💳' },
      { name: 'Receipts', href: '/dashboard/reports/receipts', icon: '🧾' },
      { name: 'Sales by modifier', href: '/dashboard/reports/sales-by-modifier', icon: '🔧' },
      { name: 'Discounts', href: '/dashboard/reports/discounts', icon: '🏷️' },
      { name: 'Taxes', href: '/dashboard/reports/taxes', icon: '💰' },
    ]
  },
  { name: 'Inventory', href: '/dashboard/inventory', icon: '📦' },
  { name: 'Clients', href: '/dashboard/clients', icon: '👥' },
  { name: 'Retail', href: '/dashboard/retail', icon: '🛒' },
  { name: 'Wholesale', href: '/dashboard/wholesale', icon: '🏢' },
  { name: 'Purchase Orders', href: '/dashboard/purchase-orders', icon: '🚚' },
  { name: 'Coaches', href: '/dashboard/coaches', icon: '🏃‍♂️' },
  { name: 'Settings', href: '/dashboard/settings', icon: '⚙️' },
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { user, signOut } = useAuth()
  const [expandedMenus, setExpandedMenus] = useState<string[]>(['Reports']) // Reports expanded by default

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  const toggleSubmenu = (menuName: string) => {
    setExpandedMenus(prev =>
      prev.includes(menuName)
        ? prev.filter(name => name !== menuName)
        : [...prev, menuName]
    )
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="hidden md:flex md:flex-col md:w-64 bg-white shadow-lg">
        {/* Logo */}
        <div className="flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-700">
          <h1 className="text-xl font-bold text-white">NutriPro Admin</h1>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            const isExpanded = expandedMenus.includes(item.name)
            const hasActiveSubmenu = item.submenu?.some(subItem => pathname === subItem.href)

            return (
              <div key={item.name}>
                {/* Main menu item */}
                {item.hasSubmenu ? (
                  <button
                    onClick={() => toggleSubmenu(item.name)}
                    className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                      isActive || hasActiveSubmenu
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="mr-3 text-lg">{item.icon}</span>
                      {item.name}
                    </div>
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </button>
                ) : (
                  <Link
                    href={item.href}
                    className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <span className="mr-3 text-lg">{item.icon}</span>
                    {item.name}
                  </Link>
                )}

                {/* Submenu items */}
                {item.hasSubmenu && isExpanded && item.submenu && (
                  <div className="ml-4 mt-1 space-y-1">
                    {item.submenu.map((subItem) => {
                      const isSubActive = pathname === subItem.href
                      return (
                        <Link
                          key={subItem.name}
                          href={subItem.href}
                          className={`flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${
                            isSubActive
                              ? 'bg-blue-100 text-blue-700 font-medium'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }`}
                        >
                          <span className="mr-3 text-base">{subItem.icon}</span>
                          {subItem.name}
                        </Link>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </nav>

        {/* User Info & Sign Out */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center">
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.email || 'Demo User'}
              </p>
              <p className="text-xs text-gray-500">Administrator</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSignOut}
              className="ml-3 flex-shrink-0"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        <div className="md:hidden bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-4 py-3">
            <h1 className="text-lg font-semibold text-gray-900">NutriPro Admin</h1>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSignOut}
            >
              Sign Out
            </Button>
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto bg-gray-50">
          {children}
        </main>
      </div>
    </div>
  )
}
