'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { TransactionQueries, ProductQueries, CustomerQueries, type CreateTransactionData } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'
import { ArrowLeft, Plus, Trash2, Search } from 'lucide-react'

interface OrderItem {
  id: string
  product_id: string
  product_name: string
  product_sku: string
  quantity: number
  unit_price: number
  discount_amount: number
  line_total: number
}

interface Customer {
  id: string
  first_name: string | null
  last_name: string | null
  company_name: string | null
  customer_type: string
}

interface Product {
  id: string
  name: string
  sku: string
  retail_price: number
  wholesale_price: number | null
  stock_quantity: number
}

export default function NewOrderPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [paymentMethod, setPaymentMethod] = useState<string>('cash')
  const [notes, setNotes] = useState('')

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      const customerQueries = new CustomerQueries()
      const productQueries = new ProductQueries()
      
      const [customersData, productsData] = await Promise.all([
        customerQueries.getAll(),
        productQueries.getAll()
      ])
      
      setCustomers(customersData || [])
      setProducts(productsData || [])
    } catch (error) {
      console.error('Failed to load initial data:', error)
    }
  }

  const addProductToOrder = (product: Product) => {
    const existingItem = orderItems.find(item => item.product_id === product.id)
    
    if (existingItem) {
      // Increase quantity if product already exists
      updateItemQuantity(existingItem.id, existingItem.quantity + 1)
    } else {
      // Add new item
      const price = selectedCustomer?.customer_type === 'wholesale' && product.wholesale_price 
        ? product.wholesale_price 
        : product.retail_price
      
      const newItem: OrderItem = {
        id: `temp-${Date.now()}`,
        product_id: product.id,
        product_name: product.name,
        product_sku: product.sku,
        quantity: 1,
        unit_price: price,
        discount_amount: 0,
        line_total: price
      }
      
      setOrderItems([...orderItems, newItem])
    }
  }

  const updateItemQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId)
      return
    }
    
    setOrderItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, quantity, line_total: (item.unit_price * quantity) - item.discount_amount }
          : item
      )
    )
  }

  const updateItemDiscount = (itemId: string, discount: number) => {
    setOrderItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, discount_amount: discount, line_total: (item.unit_price * item.quantity) - discount }
          : item
      )
    )
  }

  const removeItem = (itemId: string) => {
    setOrderItems(items => items.filter(item => item.id !== itemId))
  }

  const calculateTotals = () => {
    const subtotal = orderItems.reduce((sum, item) => sum + item.line_total, 0)
    const taxRate = 0.0625 // 6.25% tax rate
    const taxAmount = subtotal * taxRate
    const total = subtotal + taxAmount
    
    return { subtotal, taxAmount, total, taxRate }
  }

  const createOrder = async () => {
    if (orderItems.length === 0) {
      alert('Please add at least one item to the order')
      return
    }

    try {
      setLoading(true)
      const { subtotal, taxAmount, total, taxRate } = calculateTotals()
      
      const transactionData: CreateTransactionData = {
        transaction: {
          transaction_number: '', // Will be auto-generated
          customer_id: selectedCustomer?.id || null,
          transaction_type: selectedCustomer?.customer_type === 'wholesale' ? 'wholesale_order' : 'sale',
          status: 'pending',
          subtotal,
          tax_rate: taxRate,
          tax_amount: taxAmount,
          discount_amount: orderItems.reduce((sum, item) => sum + item.discount_amount, 0),
          total_amount: total,
          payment_method: paymentMethod as any,
          notes: notes || null
        },
        items: orderItems.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_amount: item.discount_amount,
          line_total: item.line_total,
          product_name: item.product_name,
          product_sku: item.product_sku
        }))
      }

      const transactionQueries = new TransactionQueries()
      const newOrder = await transactionQueries.create(transactionData)
      
      // Redirect to the new order detail page
      router.push(`/dashboard/retail/${newOrder.id}`)
    } catch (error) {
      console.error('Failed to create order:', error)
      alert('Failed to create order. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const { subtotal, taxAmount, total } = calculateTotals()

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/retail')}
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Retail
            </Button>
          </div>
          
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Create New Order</h1>
          <p className="text-gray-600 mt-1">Add products and process a new sales transaction</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Selection */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
                <CardDescription>Select a customer or leave blank for walk-in sale</CardDescription>
              </CardHeader>
              <CardContent>
                <select
                  value={selectedCustomer?.id || ''}
                  onChange={(e) => {
                    const customer = customers.find(c => c.id === e.target.value)
                    setSelectedCustomer(customer || null)
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Walk-in Customer</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.company_name || `${customer.first_name} ${customer.last_name}`} 
                      ({customer.customer_type})
                    </option>
                  ))}
                </select>
              </CardContent>
            </Card>

            {/* Product Search */}
            <Card>
              <CardHeader>
                <CardTitle>Add Products</CardTitle>
                <CardDescription>Search and add products to the order</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search products by name or SKU..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {filteredProducts.map(product => (
                    <div key={product.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                        <p className="text-sm text-gray-500">Stock: {product.stock_quantity}</p>
                      </div>
                      <div className="text-right mr-4">
                        <p className="font-medium">
                          AWG {selectedCustomer?.customer_type === 'wholesale' && product.wholesale_price 
                            ? product.wholesale_price.toFixed(2)
                            : product.retail_price.toFixed(2)
                          }
                        </p>
                        {selectedCustomer?.customer_type === 'wholesale' && product.wholesale_price && (
                          <p className="text-xs text-blue-600">Wholesale Price</p>
                        )}
                      </div>
                      <Button
                        size="sm"
                        onClick={() => addProductToOrder(product)}
                        disabled={product.stock_quantity <= 0}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Order Items</CardTitle>
                <CardDescription>{orderItems.length} items in cart</CardDescription>
              </CardHeader>
              <CardContent>
                {orderItems.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-4">🛒</div>
                    <p>No items added yet</p>
                    <p className="text-sm">Search and add products above</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {orderItems.map(item => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.product_name}</h4>
                          <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 1)}
                            className="w-20"
                          />
                          <span className="text-sm">×</span>
                          <span className="font-medium">AWG {item.unit_price.toFixed(2)}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">AWG {item.line_total.toFixed(2)}</p>
                          {item.discount_amount > 0 && (
                            <p className="text-sm text-red-600">-AWG {item.discount_amount.toFixed(2)}</p>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>AWG {subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax (6.25%):</span>
                  <span>AWG {taxAmount.toFixed(2)}</span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total:</span>
                    <span>AWG {total.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment & Notes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Payment Method</label>
                  <select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="cash">Cash</option>
                    <option value="card">Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="store_credit">Store Credit</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Notes</label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Order notes..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </CardContent>
            </Card>

            <Button
              onClick={createOrder}
              disabled={loading || orderItems.length === 0}
              className="w-full"
              size="lg"
            >
              {loading ? 'Creating Order...' : 'Create Order'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
