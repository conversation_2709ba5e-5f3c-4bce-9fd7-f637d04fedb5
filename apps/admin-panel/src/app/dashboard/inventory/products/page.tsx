'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import {
  Package,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  AlertTriangle,
  DollarSign,
  Warehouse
} from 'lucide-react'
import { DeleteProductDialog } from '@/components/products/DeleteProductDialog'

interface Product {
  id: string
  sku: string
  name: string
  description?: string
  retail_price: number
  wholesale_price?: number
  stock_quantity: number
  min_stock_level: number
  wholesale_available: boolean
  is_active: boolean
  categories?: { id: string; name: string }
  brands?: { id: string; name: string }
  vendors?: { id: string; name: string }
}

export default function ProductsPage() {
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [productToDelete, setProductToDelete] = useState<Product | null>(null)

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    try {
      setLoading(true)
      setError(null)
      const productQueries = new ProductQueries()
      const data = await productQueries.getAll()
      setProducts(data || [])
    } catch (err) {
      console.error('Failed to load products:', err)
      setError(err instanceof Error ? err.message : 'Failed to load products')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadProducts()
      return
    }

    try {
      setLoading(true)
      setError(null)
      const productQueries = new ProductQueries()
      const data = await productQueries.search(searchQuery)
      setProducts(data || [])
    } catch (err) {
      console.error('Search failed:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const handleDeleteProduct = (product: Product) => {
    setProductToDelete(product)
    setDeleteDialogOpen(true)
  }

  const handleDeleteSuccess = () => {
    loadProducts() // Reload the products list
  }

  const getStockStatus = (product: Product) => {
    if (product.stock_quantity === 0) {
      return { label: 'Out of Stock', color: 'bg-red-100 text-red-800' }
    } else if (product.stock_quantity <= product.min_stock_level) {
      return { label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' }
    } else {
      return { label: 'In Stock', color: 'bg-green-100 text-green-800' }
    }
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Products</h1>
                <p className="text-gray-600 mt-1">Manage your product catalog</p>
              </div>
            </div>
            <Button 
              onClick={() => router.push('/dashboard/inventory/new')}
              className="flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Add Product</span>
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Search Products</CardTitle>
            <CardDescription>Find products by name, SKU, or description</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? 'Searching...' : 'Search'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('')
                    loadProducts()
                  }}
                >
                  Clear
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <div className="text-red-600 font-medium">Error</div>
                <div className="text-red-700">{error}</div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Products Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="text-gray-500">Loading products...</div>
          </div>
        ) : filteredProducts.length === 0 ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-gray-500 mb-2">
                  {searchQuery ? 'No products found matching your search' : 'No products found'}
                </div>
                <div className="text-sm text-gray-400 mb-4">
                  {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first product'}
                </div>
                {!searchQuery && (
                  <Button onClick={() => router.push('/dashboard/inventory/new')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Product
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => {
              const stockStatus = getStockStatus(product)
              
              return (
                <Card key={product.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg truncate">{product.name}</CardTitle>
                        <CardDescription className="text-sm text-gray-500">
                          SKU: {product.sku}
                        </CardDescription>
                      </div>
                      <Badge className={stockStatus.color}>
                        {stockStatus.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Product Info */}
                    <div className="space-y-2">
                      {product.categories && (
                        <div className="text-sm text-gray-600">
                          Category: {product.categories.name}
                        </div>
                      )}
                      {product.brands && (
                        <div className="text-sm text-gray-600">
                          Brand: {product.brands.name}
                        </div>
                      )}
                    </div>

                    {/* Pricing */}
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-medium">AWG {product.retail_price.toFixed(2)}</span>
                      </div>
                      {product.wholesale_available && product.wholesale_price && (
                        <div className="text-sm text-gray-600">
                          Wholesale: AWG {product.wholesale_price.toFixed(2)}
                        </div>
                      )}
                    </div>

                    {/* Stock */}
                    <div className="flex items-center space-x-1">
                      <Warehouse className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">
                        {product.stock_quantity} in stock
                        {product.min_stock_level > 0 && (
                          <span className="text-gray-500"> (min: {product.min_stock_level})</span>
                        )}
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/dashboard/products/${product.id}`)}
                        className="flex items-center space-x-1"
                      >
                        <Eye className="h-4 w-4" />
                        <span>View</span>
                      </Button>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/products/${product.id}/edit`)}
                          className="flex items-center space-x-1"
                        >
                          <Edit className="h-4 w-4" />
                          <span>Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteProduct(product)}
                          className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span>Delete</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}

        {/* Delete Product Dialog */}
        {productToDelete && (
          <DeleteProductDialog
            product={productToDelete}
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
            onSuccess={handleDeleteSuccess}
          />
        )}
      </div>
    </div>
  )
}
