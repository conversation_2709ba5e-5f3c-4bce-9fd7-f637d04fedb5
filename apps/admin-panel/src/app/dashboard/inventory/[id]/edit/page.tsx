'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { ArrowLeft, Package, Loader2 } from 'lucide-react'
import { ProductForm } from '@/components/products/ProductForm'
import type { Database } from '@nutripro/database/src/types/supabase'

type Product = Database['public']['Tables']['products']['Row']
type ProductUpdate = Database['public']['Tables']['products']['Update']

export default function EditProductPage() {
  const router = useRouter()
  const params = useParams()
  const productId = params.id as string

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (productId) {
      loadProduct()
    }
  }, [productId])

  const loadProduct = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const productQueries = new ProductQueries()
      const data = await productQueries.getById(productId)
      setProduct(data)
    } catch (err) {
      console.error('Failed to load product:', err)
      setError(err instanceof Error ? err.message : 'Failed to load product')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (formData: any) => {
    try {
      setIsSubmitting(true)
      setError(null)

      // Transform form data to match database schema
      const updateData: ProductUpdate = {
        name: formData.name,
        sku: formData.sku,
        barcode: formData.barcode || null,
        description: formData.description || null,
        category_id: formData.category_id || null,
        brand_id: formData.brand_id || null,
        vendor_id: formData.vendor_id || null,
        
        // Pricing
        retail_price: formData.retail_price,
        wholesale_price: formData.wholesale_price || null,
        purchase_price: formData.purchase_price || null,
        landing_cost: formData.landing_cost || null,
        purchase_currency: formData.purchase_currency || 'AWG',
        wholesale_available: formData.wholesale_available || false,
        
        // Inventory
        stock_quantity: formData.stock_quantity || 0,
        min_stock_level: formData.min_stock_level || 0,
        max_stock_level: formData.max_stock_level || null,
        
        // Product Details
        weight: formData.weight || null,
        notes: formData.notes || null,
        
        // Supplement Specific
        serving_size: formData.serving_size || null,
        servings_per_container: formData.servings_per_container || null,
        ingredients: formData.ingredients || [],
        allergens: formData.allergens || [],
        expiry_tracking: formData.expiry_tracking || false,
        
        // Variants
        has_variants: formData.has_variants || false,
        variant_type: formData.variant_type || null,
        
        // Wholesale
        min_order_quantity: formData.min_order_quantity || 1,
        
        // Update timestamp
        updated_at: new Date().toISOString()
      }

      const productQueries = new ProductQueries()
      await productQueries.update(productId, updateData)

      // Redirect to the product detail page
      router.push(`/dashboard/inventory/products/${productId}`)
    } catch (err) {
      console.error('Failed to update product:', err)
      setError(err instanceof Error ? err.message : 'Failed to update product')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    router.back()
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading product...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error && !product) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-red-600 font-medium mb-2">Error Loading Product</div>
                <div className="text-red-700 mb-4">{error}</div>
                <Button onClick={() => router.back()} variant="outline">
                  Go Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-gray-600 mb-4">Product not found</div>
                <Button onClick={() => router.back()} variant="outline">
                  Go Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
          </div>
          
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Package className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                Edit Product
              </h1>
              <p className="text-gray-600 mt-1">
                Update product information for {product.name}
              </p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2">
                <div className="text-red-600 font-medium">Error</div>
                <div className="text-red-700">{error}</div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Product Form */}
        <ProductForm
          mode="edit"
          initialData={product}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={isSubmitting}
        />
      </div>
    </div>
  )
}
