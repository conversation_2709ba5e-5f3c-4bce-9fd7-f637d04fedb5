'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { InventoryQueries, type InventoryAlert, type ProductBatchWithProduct } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { AlertTriangle, Package, Clock, TrendingDown, Plus, Search, Filter } from 'lucide-react'
import { Input } from '@nutripro/ui'
import { BatchManagement } from '@/components/inventory/BatchManagement'

export default function InventoryPage() {
  const router = useRouter()
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [batches, setBatches] = useState<ProductBatchWithProduct[]>([])
  const [stats, setStats] = useState({
    totalProducts: 0,
    lowStockProducts: 0,
    outOfStockProducts: 0,
    expiringBatches: 0
  })
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'alerts' | 'batches' | 'adjustments' | 'batch-management'>('alerts')

  useEffect(() => {
    loadInventoryData()
  }, [])

  const loadInventoryData = async () => {
    try {
      setLoading(true)
      const inventoryQueries = new InventoryQueries()
      
      const [alertsData, batchesData, statsData] = await Promise.all([
        inventoryQueries.getInventoryAlerts(),
        inventoryQueries.getExpiringBatches(30),
        inventoryQueries.getInventoryStats()
      ])
      
      setAlerts(alertsData)
      setBatches(batchesData)
      setStats(statsData)
    } catch (error) {
      console.error('Failed to load inventory data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'out_of_stock': return <Package className="h-4 w-4" />
      case 'low_stock': return <TrendingDown className="h-4 w-4" />
      case 'expiry_warning': return <Clock className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getAlertMessage = (alert: InventoryAlert) => {
    switch (alert.alert_type) {
      case 'out_of_stock':
        return 'Out of stock'
      case 'low_stock':
        return `Low stock: ${alert.current_stock} remaining (min: ${alert.min_stock})`
      case 'expiry_warning':
        return `Expires in ${alert.days_until_expiry} days`
      default:
        return 'Unknown alert'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Inventory Management</h1>
            <p className="text-gray-600 mt-1">Monitor stock levels, batches, and inventory alerts</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Inventory Management</h1>
              <p className="text-gray-600 mt-1">Monitor stock levels, batches, and inventory alerts</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => router.push('/dashboard/inventory/products')}
                variant="outline"
              >
                <Package className="h-4 w-4 mr-1" />
                Manage Products
              </Button>
              <Button
                onClick={() => router.push('/dashboard/inventory/new')}
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Product
              </Button>
              <Button
                onClick={() => router.push('/inventory/adjustments/new')}
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-1" />
                Stock Adjustment
              </Button>
              <Button
                onClick={() => router.push('/purchase-orders/new')}
              >
                <Plus className="h-4 w-4 mr-1" />
                Purchase Order
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Products</p>
                  <p className="text-2xl font-bold">{stats.totalProducts}</p>
                </div>
                <Package className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Low Stock</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.lowStockProducts}</p>
                </div>
                <TrendingDown className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Out of Stock</p>
                  <p className="text-2xl font-bold text-red-600">{stats.outOfStockProducts}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Expiring Soon</p>
                  <p className="text-2xl font-bold text-orange-600">{stats.expiringBatches}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('alerts')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'alerts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Inventory Alerts ({alerts.length})
              </button>
              <button
                onClick={() => setActiveTab('batches')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'batches'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Expiring Batches ({batches.length})
              </button>
              <button
                onClick={() => setActiveTab('adjustments')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'adjustments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Recent Adjustments
              </button>
              <button
                onClick={() => setActiveTab('batch-management')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'batch-management'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Batch Management
              </button>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'alerts' && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                <span>Inventory Alerts</span>
              </CardTitle>
              <CardDescription>
                {alerts.length} active alerts requiring attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {alerts.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No inventory alerts</p>
                    <p className="text-sm">All products are well stocked</p>
                  </div>
                ) : (
                  alerts.map((alert) => (
                    <div key={alert.id} className="flex items-start space-x-3 p-4 border rounded-lg">
                      <div className={`p-1 rounded ${getSeverityColor(alert.severity)}`}>
                        {getAlertIcon(alert.alert_type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{alert.product_name}</p>
                        <p className="text-xs text-gray-500">SKU: {alert.sku}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          {getAlertMessage(alert)}
                        </p>
                        {alert.batch_number && (
                          <p className="text-xs text-gray-500">Batch: {alert.batch_number}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                          {alert.severity}
                        </Badge>
                        <Button size="sm" variant="outline">
                          Action
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'batches' && (
          <Card>
            <CardHeader>
              <CardTitle>Expiring Batches</CardTitle>
              <CardDescription>
                Product batches expiring in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {batches.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p>No expiring batches</p>
                    <p className="text-sm">All batches have sufficient shelf life</p>
                  </div>
                ) : (
                  batches.map((batch) => {
                    const daysUntilExpiry = Math.ceil(
                      (new Date(batch.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
                    )
                    return (
                      <div key={batch.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{batch.products?.name}</h4>
                          <p className="text-sm text-gray-600">SKU: {batch.products?.sku}</p>
                          <p className="text-sm text-gray-600">Batch: {batch.batch_number}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{batch.quantity_available} units</p>
                          <p className="text-sm text-gray-600">Expires: {formatDate(batch.expiry_date)}</p>
                          <Badge 
                            variant="outline" 
                            className={
                              daysUntilExpiry <= 7 ? 'text-red-600 border-red-200' :
                              daysUntilExpiry <= 14 ? 'text-yellow-600 border-yellow-200' :
                              'text-blue-600 border-blue-200'
                            }
                          >
                            {daysUntilExpiry} days
                          </Badge>
                        </div>
                      </div>
                    )
                  })
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'adjustments' && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Adjustments</CardTitle>
              <CardDescription>
                Latest inventory adjustments and stock movements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>No recent adjustments</p>
                <p className="text-sm">Inventory adjustment history will appear here</p>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === 'batch-management' && (
          <BatchManagement />
        )}
      </div>
    </div>
  )
}
