'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { Separator } from '@nutripro/ui'
import { 
  ArrowLeft, 
  Package, 
  Edit, 
  Trash2, 
  DollarSign, 
  Warehouse, 
  Info,
  Pill,
  Settings,
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { DeleteProductDialog } from '@/components/products/DeleteProductDialog'

interface ProductDetail {
  id: string
  sku: string
  barcode?: string
  name: string
  description?: string
  retail_price: number
  wholesale_price?: number
  purchase_price?: number
  landing_cost?: number
  purchase_currency: string
  wholesale_available: boolean
  stock_quantity: number
  min_stock_level: number
  max_stock_level?: number
  weight?: number
  notes?: string
  serving_size?: string
  servings_per_container?: number
  ingredients?: string[]
  allergens?: string[]
  expiry_tracking: boolean
  has_variants: boolean
  variant_type?: string
  min_order_quantity: number
  is_active: boolean
  created_at: string
  updated_at: string
  categories?: { id: string; name: string }
  brands?: { id: string; name: string }
  vendors?: { id: string; name: string }
}

export default function ProductDetailPage() {
  const router = useRouter()
  const params = useParams()
  const productId = params.id as string

  const [product, setProduct] = useState<ProductDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  useEffect(() => {
    if (productId) {
      loadProduct()
    }
  }, [productId])

  const loadProduct = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const productQueries = new ProductQueries()
      const data = await productQueries.getById(productId)
      setProduct(data)
    } catch (err) {
      console.error('Failed to load product:', err)
      setError(err instanceof Error ? err.message : 'Failed to load product')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSuccess = () => {
    // Redirect to products list after successful deletion
    router.push('/dashboard/inventory/products')
  }

  const getStockStatus = (product: ProductDetail) => {
    if (product.stock_quantity === 0) {
      return { label: 'Out of Stock', color: 'bg-red-100 text-red-800' }
    } else if (product.stock_quantity <= product.min_stock_level) {
      return { label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' }
    } else {
      return { label: 'In Stock', color: 'bg-green-100 text-green-800' }
    }
  }

  if (loading) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading product...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <div className="text-red-600 font-medium mb-2">
                  {error || 'Product not found'}
                </div>
                <Button onClick={() => router.back()} variant="outline">
                  Go Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const stockStatus = getStockStatus(product)

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back</span>
            </Button>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
                  {product.name}
                </h1>
                <p className="text-gray-600 mt-1">
                  SKU: {product.sku}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge className={stockStatus.color}>
                {stockStatus.label}
              </Badge>
              <Button
                onClick={() => router.push(`/dashboard/products/${product.id}/edit`)}
                className="flex items-center space-x-2"
              >
                <Edit className="h-4 w-4" />
                <span>Edit</span>
              </Button>
              <Button
                onClick={() => setDeleteDialogOpen(true)}
                variant="outline"
                className="flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4" />
                <span>Delete</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Product Name</label>
                    <div className="mt-1">{product.name}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">SKU</label>
                    <div className="mt-1 font-mono">{product.sku}</div>
                  </div>
                </div>

                {product.barcode && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Barcode</label>
                    <div className="mt-1 font-mono">{product.barcode}</div>
                  </div>
                )}

                {product.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <div className="mt-1">{product.description}</div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {product.categories && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Category</label>
                      <div className="mt-1">{product.categories.name}</div>
                    </div>
                  )}
                  {product.brands && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Brand</label>
                      <div className="mt-1">{product.brands.name}</div>
                    </div>
                  )}
                  {product.vendors && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Vendor</label>
                      <div className="mt-1">{product.vendors.name}</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5" />
                  <span>Pricing</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Retail Price</label>
                    <div className="mt-1 text-lg font-semibold">AWG {product.retail_price.toFixed(2)}</div>
                  </div>
                  {product.wholesale_available && product.wholesale_price && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Wholesale Price</label>
                      <div className="mt-1 text-lg font-semibold">AWG {product.wholesale_price.toFixed(2)}</div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {product.purchase_price && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Purchase Price</label>
                      <div className="mt-1">{product.purchase_currency} {product.purchase_price.toFixed(2)}</div>
                    </div>
                  )}
                  {product.landing_cost && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Landing Cost</label>
                      <div className="mt-1">AWG {product.landing_cost.toFixed(2)}</div>
                    </div>
                  )}
                </div>

                {product.wholesale_available && (
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-blue-100 text-blue-800">Wholesale Available</Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Inventory */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Warehouse className="h-5 w-5" />
                  <span>Inventory</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Current Stock</label>
                  <div className="mt-1 text-2xl font-bold">{product.stock_quantity}</div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Min Level:</span>
                    <span>{product.min_stock_level}</span>
                  </div>
                  {product.max_stock_level && (
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Max Level:</span>
                      <span>{product.max_stock_level}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Min Order Qty:</span>
                    <span>{product.min_order_quantity}</span>
                  </div>
                </div>

                {product.expiry_tracking && (
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-orange-100 text-orange-800">Expiry Tracking</Badge>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Product Details */}
            {(product.weight || product.notes) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Info className="h-5 w-5" />
                    <span>Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {product.weight && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Weight</label>
                      <div className="mt-1">{product.weight}g</div>
                    </div>
                  )}
                  {product.notes && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Notes</label>
                      <div className="mt-1 text-sm">{product.notes}</div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Delete Product Dialog */}
        {product && (
          <DeleteProductDialog
            product={{
              id: product.id,
              name: product.name,
              sku: product.sku,
              stock_quantity: product.stock_quantity
            }}
            open={deleteDialogOpen}
            onOpenChange={setDeleteDialogOpen}
            onSuccess={handleDeleteSuccess}
          />
        )}
      </div>
    </div>
  )
}
