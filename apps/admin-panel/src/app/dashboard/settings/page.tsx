'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'

export default function SettingsPage() {
  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">Manage system configuration and preferences</p>
        </div>

        {/* Settings Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* General Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">⚙️</span>
                General
              </CardTitle>
              <CardDescription>
                Basic system configuration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Configure general system settings, business information, and default preferences.
              </p>
            </CardContent>
          </Card>

          {/* Invoice Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">📄</span>
                Invoice Settings
              </CardTitle>
              <CardDescription>
                Invoice numbering and formatting
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Manage invoice number formats, tax rates, and invoice templates.
              </p>
            </CardContent>
          </Card>

          {/* Payment Methods */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">💳</span>
                Payment Methods
              </CardTitle>
              <CardDescription>
                Configure accepted payment types
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Set up and manage payment methods, processing fees, and payment terms.
              </p>
            </CardContent>
          </Card>

          {/* User Management */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">👤</span>
                User Management
              </CardTitle>
              <CardDescription>
                Manage staff accounts and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Add, edit, and manage staff accounts, roles, and access permissions.
              </p>
            </CardContent>
          </Card>

          {/* Backup & Sync */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">☁️</span>
                Backup & Sync
              </CardTitle>
              <CardDescription>
                Data backup and synchronization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Configure automatic backups, data sync, and recovery options.
              </p>
            </CardContent>
          </Card>

          {/* System Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <span className="mr-2">ℹ️</span>
                System Information
              </CardTitle>
              <CardDescription>
                Version and system details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                View system version, database status, and technical information.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Coming Soon Notice */}
        <div className="mt-8">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <span className="text-2xl mr-3">🚧</span>
                <div>
                  <h3 className="font-semibold text-blue-900">Settings Coming Soon</h3>
                  <p className="text-blue-700 text-sm mt-1">
                    Detailed settings configuration will be available in the next update. 
                    For now, system defaults are being used.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
