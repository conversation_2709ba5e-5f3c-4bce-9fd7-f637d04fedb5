'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getAuthClient } from '@nutripro/auth'
import type { AuthContextType, User, LoginCredentials, SignUpCredentials } from '@nutripro/auth'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Check if we have valid Supabase configuration
  const hasValidConfig = process.env.NEXT_PUBLIC_SUPABASE_URL &&
                         process.env.NEXT_PUBLIC_SUPABASE_URL !== 'https://placeholder.supabase.co'

  const supabase = hasValidConfig ? getAuthClient() : null

  useEffect(() => {
    if (!hasValidConfig) {
      // If no valid Supabase config, just set loading to false
      setLoading(false)
      setError('Supabase not configured - using demo mode')
      return
    }

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase!.auth.getSession()
        if (error) {
          console.error('Error getting session:', error)
          setError(error.message)
        } else {
          setUser(session?.user as User || null)
        }
      } catch (err) {
        console.error('Error in getInitialSession:', err)
        setError('Failed to get session')
      } finally {
        setLoading(false)
      }
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase!.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        setUser(session?.user as User || null)
        setLoading(false)
        setError(null)
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase, hasValidConfig])

  const signIn = async (credentials: LoginCredentials) => {
    try {
      setLoading(true)
      setError(null)

      if (!hasValidConfig) {
        // Demo mode - simulate successful login
        setTimeout(() => {
          setUser({
            id: 'demo-user',
            email: credentials.email,
            role: 'admin'
          } as User)
          setLoading(false)
        }, 1000)
        return
      }

      const { error } = await supabase!.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      })

      if (error) {
        setError(error.message)
        throw error
      }
    } catch (err) {
      console.error('Sign in error:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (credentials: SignUpCredentials) => {
    try {
      setLoading(true)
      setError(null)
      
      const { error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          data: {
            role: credentials.role || 'staff',
          },
        },
      })

      if (error) {
        setError(error.message)
        throw error
      }
    } catch (err) {
      console.error('Sign up error:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!hasValidConfig) {
        // Demo mode - simulate sign out
        setUser(null)
        setLoading(false)
        return
      }

      const { error } = await supabase!.auth.signOut()

      if (error) {
        setError(error.message)
        throw error
      }
    } catch (err) {
      console.error('Sign out error:', err)
      throw err
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      setError(null)
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        setError(error.message)
        throw error
      }
    } catch (err) {
      console.error('Reset password error:', err)
      throw err
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
