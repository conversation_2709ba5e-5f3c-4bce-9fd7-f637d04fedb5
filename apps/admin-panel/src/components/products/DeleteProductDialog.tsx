'use client'

import { useState } from 'react'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import * as Dialog from '@radix-ui/react-dialog'
import { AlertTriangle, X, Trash2, Loader2 } from 'lucide-react'

interface Product {
  id: string
  name: string
  sku: string
  stock_quantity: number
}

interface DeleteProductDialogProps {
  product: Product
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function DeleteProductDialog({ product, open, onOpenChange, onSuccess }: DeleteProductDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleDelete = async () => {
    try {
      setIsDeleting(true)
      setError(null)

      const productQueries = new ProductQueries()
      await productQueries.delete(product.id)

      onSuccess()
      onOpenChange(false)
    } catch (err) {
      console.error('Failed to delete product:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete product')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeactivate = async () => {
    try {
      setIsDeleting(true)
      setError(null)

      const productQueries = new ProductQueries()
      await productQueries.deactivate(product.id)

      onSuccess()
      onOpenChange(false)
    } catch (err) {
      console.error('Failed to deactivate product:', err)
      setError(err instanceof Error ? err.message : 'Failed to deactivate product')
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="p-2 bg-red-100 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">Delete Product</CardTitle>
                    <CardDescription>This action cannot be undone</CardDescription>
                  </div>
                </div>
                <Dialog.Close asChild>
                  <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                    <X className="h-4 w-4" />
                  </Button>
                </Dialog.Close>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="font-medium">{product.name}</div>
                <div className="text-sm text-gray-600">SKU: {product.sku}</div>
                {product.stock_quantity > 0 && (
                  <div className="text-sm text-orange-600 mt-1">
                    ⚠️ This product has {product.stock_quantity} units in stock
                  </div>
                )}
              </div>

              <div className="text-sm text-gray-700">
                <p className="mb-2">
                  Are you sure you want to delete this product? This will:
                </p>
                <ul className="list-disc list-inside space-y-1 text-gray-600">
                  <li>Remove the product from your catalog</li>
                  <li>Hide it from all product listings</li>
                  <li>Prevent new sales of this product</li>
                  <li>Preserve historical sales data</li>
                </ul>
              </div>

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-red-700">{error}</div>
                  </div>
                  
                  {error.includes('sales history') && (
                    <div className="mt-3 pt-3 border-t border-red-200">
                      <p className="text-sm text-red-600 mb-2">
                        This product has sales history. You can deactivate it instead:
                      </p>
                      <Button
                        onClick={handleDeactivate}
                        disabled={isDeleting}
                        variant="outline"
                        size="sm"
                        className="border-orange-300 text-orange-700 hover:bg-orange-50"
                      >
                        {isDeleting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Deactivating...
                          </>
                        ) : (
                          'Deactivate Product'
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Dialog.Close asChild>
                  <Button variant="outline" disabled={isDeleting}>
                    Cancel
                  </Button>
                </Dialog.Close>
                <Button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Product
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  )
}
