'use client'

import { useEffect, useState } from 'react'
import { CategoryQueries } from '@nutripro/database'
import * as Select from '@radix-ui/react-select'
import * as Label from '@radix-ui/react-label'
import { ChevronDown, Check, Search } from 'lucide-react'
import { Input } from '@nutripro/ui'

interface Category {
  id: string
  name: string
  description?: string
  parent_id?: string
  parent?: {
    id: string
    name: string
  }
}

interface CategorySelectProps {
  value?: string
  onValueChange: (value: string) => void
  placeholder?: string
  label?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

export function CategorySelect({
  value,
  onValueChange,
  placeholder = "Select category",
  label,
  required = false,
  disabled = false,
  className
}: CategorySelectProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = async () => {
    try {
      setLoading(true)
      const categoryQueries = new CategoryQueries()
      const data = await categoryQueries.getAll()
      setCategories(data || [])
    } catch (error) {
      console.error('Failed to load categories:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  const selectedCategory = categories.find(cat => cat.id === value)

  const formatCategoryName = (category: Category) => {
    if (category.parent) {
      return `${category.parent.name} > ${category.name}`
    }
    return category.name
  }

  return (
    <div className={className}>
      {label && (
        <Label.Root className="text-sm font-medium mb-2 block">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label.Root>
      )}
      
      <Select.Root
        value={value || undefined}
        onValueChange={onValueChange}
        disabled={disabled || loading}
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <Select.Trigger className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
          <Select.Value placeholder={loading ? "Loading..." : placeholder}>
            {selectedCategory ? formatCategoryName(selectedCategory) : placeholder}
          </Select.Value>
          <Select.Icon>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Select.Icon>
        </Select.Trigger>

        <Select.Portal>
          <Select.Content className="relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md">
            <div className="p-2 border-b">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search categories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 h-8"
                />
              </div>
            </div>
            
            <Select.Viewport className="p-1 max-h-60 overflow-y-auto">
              <Select.Item 
                value="" 
                className="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground"
              >
                <Select.ItemText>No category</Select.ItemText>
              </Select.Item>
              
              {filteredCategories.length === 0 && searchQuery ? (
                <div className="py-2 px-8 text-sm text-muted-foreground">
                  No categories found
                </div>
              ) : (
                filteredCategories.map((category) => (
                  <Select.Item
                    key={category.id}
                    value={category.id}
                    className="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground"
                  >
                    <Select.ItemIndicator className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                      <Check className="h-4 w-4" />
                    </Select.ItemIndicator>
                    <Select.ItemText>
                      <div>
                        <div className="font-medium">{formatCategoryName(category)}</div>
                        {category.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {category.description}
                          </div>
                        )}
                      </div>
                    </Select.ItemText>
                  </Select.Item>
                ))
              )}
            </Select.Viewport>
          </Select.Content>
        </Select.Portal>
      </Select.Root>
    </div>
  )
}
