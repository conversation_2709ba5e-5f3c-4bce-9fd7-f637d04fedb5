'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { ProductQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Input } from '@nutripro/ui'
import { Textarea } from '@nutripro/ui'
import { Separator } from '@nutripro/ui'
import * as Switch from '@radix-ui/react-switch'
import * as Select from '@radix-ui/react-select'
import * as Label from '@radix-ui/react-label'
import { ChevronDown, Check, Package, DollarSign, Warehouse, Info, Pill, Settings } from 'lucide-react'
import { CategorySelect } from './CategorySelect'
import { BrandSelect } from './BrandSelect'
import { VendorSelect } from './VendorSelect'

// Form validation schema
const productFormSchema = z.object({
  // Basic Information
  name: z.string().min(1, 'Product name is required').max(200, 'Name too long'),
  sku: z.string().min(1, 'SKU is required').max(50, 'SKU too long'),
  barcode: z.string().optional(),
  description: z.string().optional(),
  category_id: z.string().optional(),
  brand_id: z.string().optional(),
  vendor_id: z.string().optional(),
  
  // Pricing
  retail_price: z.number().min(0.01, 'Retail price must be greater than 0'),
  wholesale_price: z.number().optional(),
  purchase_price: z.number().optional(),
  landing_cost: z.number().optional(),
  purchase_currency: z.string().default('AWG'),
  wholesale_available: z.boolean().default(false),
  
  // Inventory
  stock_quantity: z.number().int().min(0, 'Stock cannot be negative').default(0),
  min_stock_level: z.number().int().min(0, 'Minimum stock cannot be negative').default(0),
  max_stock_level: z.number().int().optional(),
  
  // Product Details
  weight: z.number().optional(),
  notes: z.string().optional(),
  
  // Supplement Specific
  serving_size: z.string().optional(),
  servings_per_container: z.number().int().optional(),
  ingredients: z.array(z.string()).default([]),
  allergens: z.array(z.string()).default([]),
  expiry_tracking: z.boolean().default(false),
  
  // Variants
  has_variants: z.boolean().default(false),
  variant_type: z.string().optional(),
  
  // Wholesale
  min_order_quantity: z.number().int().min(1, 'Minimum order quantity must be at least 1').default(1),
})

type ProductFormData = z.infer<typeof productFormSchema>

interface ProductFormProps {
  initialData?: Partial<ProductFormData>
  onSubmit: (data: ProductFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  mode: 'create' | 'edit'
}



export function ProductForm({ initialData, onSubmit, onCancel, isLoading = false, mode }: ProductFormProps) {
  const [ingredientInput, setIngredientInput] = useState('')
  const [allergenInput, setAllergenInput] = useState('')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      purchase_currency: 'AWG',
      wholesale_available: false,
      stock_quantity: 0,
      min_stock_level: 0,
      expiry_tracking: false,
      has_variants: false,
      min_order_quantity: 1,
      ingredients: [],
      allergens: [],
      ...initialData
    }
  })

  const watchedFields = watch(['wholesale_available', 'has_variants', 'expiry_tracking', 'ingredients', 'allergens'])

  useEffect(() => {
    if (initialData) {
      reset({
        purchase_currency: 'AWG',
        wholesale_available: false,
        stock_quantity: 0,
        min_stock_level: 0,
        expiry_tracking: false,
        has_variants: false,
        min_order_quantity: 1,
        ingredients: [],
        allergens: [],
        ...initialData
      })
    }
  }, [initialData, reset])

  const addIngredient = () => {
    if (ingredientInput.trim()) {
      const currentIngredients = watch('ingredients') || []
      setValue('ingredients', [...currentIngredients, ingredientInput.trim()])
      setIngredientInput('')
    }
  }

  const removeIngredient = (index: number) => {
    const currentIngredients = watch('ingredients') || []
    setValue('ingredients', currentIngredients.filter((_, i) => i !== index))
  }

  const addAllergen = () => {
    if (allergenInput.trim()) {
      const currentAllergens = watch('allergens') || []
      setValue('allergens', [...currentAllergens, allergenInput.trim()])
      setAllergenInput('')
    }
  }

  const removeAllergen = (index: number) => {
    const currentAllergens = watch('allergens') || []
    setValue('allergens', currentAllergens.filter((_, i) => i !== index))
  }

  const generateSKU = () => {
    const name = watch('name')
    if (name) {
      const sku = name
        .toUpperCase()
        .replace(/[^A-Z0-9]/g, '')
        .substring(0, 10) + '-' + Date.now().toString().slice(-4)
      setValue('sku', sku)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Basic Information</span>
          </CardTitle>
          <CardDescription>
            Essential product details and identification
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="name" className="text-sm font-medium">
                Product Name *
              </Label.Root>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter product name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label.Root htmlFor="sku" className="text-sm font-medium">
                  SKU *
                </Label.Root>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateSKU}
                  disabled={!watch('name')}
                >
                  Generate
                </Button>
              </div>
              <Input
                id="sku"
                {...register('sku')}
                placeholder="Enter SKU"
                className={errors.sku ? 'border-red-500' : ''}
              />
              {errors.sku && (
                <p className="text-sm text-red-600">{errors.sku.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="barcode" className="text-sm font-medium">
                Barcode
              </Label.Root>
              <Input
                id="barcode"
                {...register('barcode')}
                placeholder="Enter barcode"
              />
            </div>

            <CategorySelect
              value={watch('category_id')}
              onValueChange={(value) => setValue('category_id', value)}
              label="Category"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <BrandSelect
              value={watch('brand_id')}
              onValueChange={(value) => setValue('brand_id', value)}
              label="Brand"
            />

            <VendorSelect
              value={watch('vendor_id')}
              onValueChange={(value) => setValue('vendor_id', value)}
              label="Vendor"
            />
          </div>

          <div className="space-y-2">
            <Label.Root htmlFor="description" className="text-sm font-medium">
              Description
            </Label.Root>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter product description"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Pricing */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Pricing</span>
          </CardTitle>
          <CardDescription>
            Set retail, wholesale, and cost prices
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="retail_price" className="text-sm font-medium">
                Retail Price (AWG) *
              </Label.Root>
              <Input
                id="retail_price"
                type="number"
                step="0.01"
                min="0"
                {...register('retail_price', { valueAsNumber: true })}
                placeholder="0.00"
                className={errors.retail_price ? 'border-red-500' : ''}
              />
              {errors.retail_price && (
                <p className="text-sm text-red-600">{errors.retail_price.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="purchase_price" className="text-sm font-medium">
                Purchase Price
              </Label.Root>
              <div className="flex space-x-2">
                <Input
                  id="purchase_price"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('purchase_price', { valueAsNumber: true })}
                  placeholder="0.00"
                  className="flex-1"
                />
                <Select.Root
                  value={watch('purchase_currency') || 'AWG'}
                  onValueChange={(value) => setValue('purchase_currency', value)}
                >
                  <Select.Trigger className="w-20 flex h-10 items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm">
                    <Select.Value />
                    <Select.Icon>
                      <ChevronDown className="h-4 w-4 opacity-50" />
                    </Select.Icon>
                  </Select.Trigger>
                  <Select.Portal>
                    <Select.Content className="relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md">
                      <Select.Viewport className="p-1">
                        {['AWG', 'USD', 'EUR'].map((currency) => (
                          <Select.Item
                            key={currency}
                            value={currency}
                            className="relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground"
                          >
                            <Select.ItemIndicator className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
                              <Check className="h-4 w-4" />
                            </Select.ItemIndicator>
                            <Select.ItemText>{currency}</Select.ItemText>
                          </Select.Item>
                        ))}
                      </Select.Viewport>
                    </Select.Content>
                  </Select.Portal>
                </Select.Root>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="landing_cost" className="text-sm font-medium">
                Landing Cost (AWG)
              </Label.Root>
              <Input
                id="landing_cost"
                type="number"
                step="0.01"
                min="0"
                {...register('landing_cost', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="wholesale_price" className="text-sm font-medium">
                Wholesale Price (AWG)
              </Label.Root>
              <Input
                id="wholesale_price"
                type="number"
                step="0.01"
                min="0"
                {...register('wholesale_price', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={!watch('wholesale_available')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch.Root
              checked={watch('wholesale_available')}
              onCheckedChange={(checked) => setValue('wholesale_available', checked)}
              className="relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600"
            >
              <Switch.Thumb className="block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]" />
            </Switch.Root>
            <Label.Root className="text-sm font-medium cursor-pointer">
              Available for wholesale
            </Label.Root>
          </div>
        </CardContent>
      </Card>

      {/* Inventory */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Warehouse className="h-5 w-5" />
            <span>Inventory</span>
          </CardTitle>
          <CardDescription>
            Stock levels and inventory management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="stock_quantity" className="text-sm font-medium">
                Current Stock
              </Label.Root>
              <Input
                id="stock_quantity"
                type="number"
                min="0"
                {...register('stock_quantity', { valueAsNumber: true })}
                placeholder="0"
                className={errors.stock_quantity ? 'border-red-500' : ''}
              />
              {errors.stock_quantity && (
                <p className="text-sm text-red-600">{errors.stock_quantity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="min_stock_level" className="text-sm font-medium">
                Minimum Stock Level
              </Label.Root>
              <Input
                id="min_stock_level"
                type="number"
                min="0"
                {...register('min_stock_level', { valueAsNumber: true })}
                placeholder="0"
                className={errors.min_stock_level ? 'border-red-500' : ''}
              />
              {errors.min_stock_level && (
                <p className="text-sm text-red-600">{errors.min_stock_level.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="max_stock_level" className="text-sm font-medium">
                Maximum Stock Level
              </Label.Root>
              <Input
                id="max_stock_level"
                type="number"
                min="0"
                {...register('max_stock_level', { valueAsNumber: true })}
                placeholder="Optional"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="min_order_quantity" className="text-sm font-medium">
                Minimum Order Quantity
              </Label.Root>
              <Input
                id="min_order_quantity"
                type="number"
                min="1"
                {...register('min_order_quantity', { valueAsNumber: true })}
                placeholder="1"
                className={errors.min_order_quantity ? 'border-red-500' : ''}
              />
              {errors.min_order_quantity && (
                <p className="text-sm text-red-600">{errors.min_order_quantity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="weight" className="text-sm font-medium">
                Weight (grams)
              </Label.Root>
              <Input
                id="weight"
                type="number"
                step="0.1"
                min="0"
                {...register('weight', { valueAsNumber: true })}
                placeholder="0.0"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch.Root
              checked={watch('expiry_tracking')}
              onCheckedChange={(checked) => setValue('expiry_tracking', checked)}
              className="relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600"
            >
              <Switch.Thumb className="block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]" />
            </Switch.Root>
            <Label.Root className="text-sm font-medium cursor-pointer">
              Enable expiry tracking
            </Label.Root>
          </div>
        </CardContent>
      </Card>

      {/* Supplement Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Pill className="h-5 w-5" />
            <span>Supplement Information</span>
          </CardTitle>
          <CardDescription>
            Nutrition facts and supplement-specific details
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label.Root htmlFor="serving_size" className="text-sm font-medium">
                Serving Size
              </Label.Root>
              <Input
                id="serving_size"
                {...register('serving_size')}
                placeholder="e.g., 1 scoop (30g)"
              />
            </div>

            <div className="space-y-2">
              <Label.Root htmlFor="servings_per_container" className="text-sm font-medium">
                Servings per Container
              </Label.Root>
              <Input
                id="servings_per_container"
                type="number"
                min="1"
                {...register('servings_per_container', { valueAsNumber: true })}
                placeholder="30"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label.Root className="text-sm font-medium">
              Ingredients
            </Label.Root>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  value={ingredientInput}
                  onChange={(e) => setIngredientInput(e.target.value)}
                  placeholder="Add ingredient"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addIngredient())}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addIngredient}
                  disabled={!ingredientInput.trim()}
                >
                  Add
                </Button>
              </div>
              {watch('ingredients')?.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watch('ingredients').map((ingredient, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-1 bg-gray-100 rounded-md px-2 py-1 text-sm"
                    >
                      <span>{ingredient}</span>
                      <button
                        type="button"
                        onClick={() => removeIngredient(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label.Root className="text-sm font-medium">
              Allergens
            </Label.Root>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  value={allergenInput}
                  onChange={(e) => setAllergenInput(e.target.value)}
                  placeholder="Add allergen"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAllergen())}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addAllergen}
                  disabled={!allergenInput.trim()}
                >
                  Add
                </Button>
              </div>
              {watch('allergens')?.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {watch('allergens').map((allergen, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-1 bg-red-100 rounded-md px-2 py-1 text-sm"
                    >
                      <span>{allergen}</span>
                      <button
                        type="button"
                        onClick={() => removeAllergen(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product Variants */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Product Variants</span>
          </CardTitle>
          <CardDescription>
            Configure product variants and options
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch.Root
              checked={watch('has_variants')}
              onCheckedChange={(checked) => setValue('has_variants', checked)}
              className="relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600"
            >
              <Switch.Thumb className="block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]" />
            </Switch.Root>
            <Label.Root className="text-sm font-medium cursor-pointer">
              This product has variants
            </Label.Root>
          </div>

          {watch('has_variants') && (
            <div className="space-y-2">
              <Label.Root htmlFor="variant_type" className="text-sm font-medium">
                Variant Type
              </Label.Root>
              <Input
                id="variant_type"
                {...register('variant_type')}
                placeholder="e.g., Flavor, Size, Color"
              />
              <p className="text-xs text-gray-500">
                Specify what varies between product options (e.g., Flavor for different flavors)
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="h-5 w-5" />
            <span>Additional Details</span>
          </CardTitle>
          <CardDescription>
            Notes and additional product information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label.Root htmlFor="notes" className="text-sm font-medium">
              Notes
            </Label.Root>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Internal notes about this product"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : mode === 'create' ? 'Create Product' : 'Update Product'}
        </Button>
      </div>
    </form>
  )
}
