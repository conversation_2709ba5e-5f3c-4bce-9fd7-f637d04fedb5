'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { AlertTriangle, Package, Clock, TrendingDown } from 'lucide-react'
import { ProductQueries } from '@nutripro/database'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'

interface InventoryAlert {
  id: string
  productName: string
  sku: string
  currentStock: number
  minStock: number
  alertType: 'low_stock' | 'out_of_stock' | 'expiry_warning'
  severity: 'high' | 'medium' | 'low'
  daysUntilExpiry?: number
}

interface StockLevelData {
  category: string
  inStock: number
  lowStock: number
  outOfStock: number
}

// Mock data generators
const generateInventoryAlerts = (products: any[]): InventoryAlert[] => {
  const alerts: InventoryAlert[] = []
  
  products?.forEach((product) => {
    if (product.stock_quantity <= product.min_stock_level) {
      alerts.push({
        id: product.id,
        productName: product.name,
        sku: product.sku,
        currentStock: product.stock_quantity,
        minStock: product.min_stock_level,
        alertType: product.stock_quantity === 0 ? 'out_of_stock' : 'low_stock',
        severity: product.stock_quantity === 0 ? 'high' : product.stock_quantity <= product.min_stock_level / 2 ? 'medium' : 'low'
      })
    }
    
    // Add some mock expiry warnings
    if (Math.random() > 0.8) {
      const daysUntilExpiry = Math.floor(Math.random() * 30) + 1
      alerts.push({
        id: `${product.id}-expiry`,
        productName: product.name,
        sku: product.sku,
        currentStock: product.stock_quantity,
        minStock: product.min_stock_level,
        alertType: 'expiry_warning',
        severity: daysUntilExpiry <= 7 ? 'high' : daysUntilExpiry <= 14 ? 'medium' : 'low',
        daysUntilExpiry
      })
    }
  })
  
  return alerts
}

const generateStockLevelData = (): StockLevelData[] => {
  return [
    { category: 'Protein Powders', inStock: 45, lowStock: 8, outOfStock: 2 },
    { category: 'Pre-Workout', inStock: 32, lowStock: 5, outOfStock: 1 },
    { category: 'Vitamins', inStock: 28, lowStock: 3, outOfStock: 0 },
    { category: 'BCAA/Amino', inStock: 22, lowStock: 4, outOfStock: 1 },
    { category: 'Creatine', inStock: 18, lowStock: 2, outOfStock: 0 },
    { category: 'Fat Burners', inStock: 15, lowStock: 3, outOfStock: 1 }
  ]
}

export function InventoryAlertsSection() {
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [stockLevelData, setStockLevelData] = useState<StockLevelData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadInventoryData = async () => {
      try {
        const productQueries = new ProductQueries()
        const products = await productQueries.getAll()
        
        setAlerts(generateInventoryAlerts(products))
        setStockLevelData(generateStockLevelData())
      } catch (error) {
        console.error('Failed to load inventory data:', error)
        // Use mock data on error
        setStockLevelData(generateStockLevelData())
      } finally {
        setLoading(false)
      }
    }

    loadInventoryData()
  }, [])

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAlertIcon = (alertType: string) => {
    switch (alertType) {
      case 'out_of_stock': return <Package className="h-4 w-4" />
      case 'low_stock': return <TrendingDown className="h-4 w-4" />
      case 'expiry_warning': return <Clock className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getAlertMessage = (alert: InventoryAlert) => {
    switch (alert.alertType) {
      case 'out_of_stock':
        return 'Out of stock'
      case 'low_stock':
        return `Low stock: ${alert.currentStock} remaining (min: ${alert.minStock})`
      case 'expiry_warning':
        return `Expires in ${alert.daysUntilExpiry} days`
      default:
        return 'Unknown alert'
    }
  }

  const stockStatusColors = ['#10B981', '#F59E0B', '#EF4444'] // Green, Yellow, Red

  if (loading) {
    return (
      <div className="mb-6 md:mb-8">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Inventory Management</h2>
          <p className="text-gray-600">Stock levels, alerts, and inventory insights</p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          {[1, 2].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-6 md:mb-8">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Inventory Management</h2>
        <p className="text-gray-600">Stock levels, alerts, and inventory insights</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Inventory Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>Inventory Alerts</span>
            </CardTitle>
            <CardDescription>
              {alerts.length} active alerts requiring attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {alerts.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No inventory alerts</p>
                  <p className="text-sm">All products are well stocked</p>
                </div>
              ) : (
                alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <div className={`p-1 rounded ${getSeverityColor(alert.severity)}`}>
                      {getAlertIcon(alert.alertType)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{alert.productName}</p>
                      <p className="text-xs text-gray-500">SKU: {alert.sku}</p>
                      <p className="text-xs text-gray-600 mt-1">
                        {getAlertMessage(alert)}
                      </p>
                    </div>
                    <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                      {alert.severity}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Stock Levels by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Levels by Category</CardTitle>
            <CardDescription>Inventory status across product categories</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={stockLevelData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="category" type="category" width={100} />
                <Tooltip />
                <Bar dataKey="inStock" stackId="a" fill="#10B981" name="In Stock" />
                <Bar dataKey="lowStock" stackId="a" fill="#F59E0B" name="Low Stock" />
                <Bar dataKey="outOfStock" stackId="a" fill="#EF4444" name="Out of Stock" />
              </BarChart>
            </ResponsiveContainer>
            <div className="mt-4 flex justify-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span>In Stock</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                <span>Low Stock</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded"></div>
                <span>Out of Stock</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
