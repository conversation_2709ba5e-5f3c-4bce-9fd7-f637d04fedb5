'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { TrendingUp, TrendingDown, DollarSign, ShoppingCart, Users, Package } from 'lucide-react'

interface SalesMetrics {
  todaySales: number
  yesterdaySales: number
  weekSales: number
  monthSales: number
  todayOrders: number
  weekOrders: number
  monthOrders: number
  averageOrderValue: number
  loading: boolean
}

// Mock data generator for demonstration
const generateMockSalesData = (): SalesMetrics => {
  const baseDaily = 850
  const variation = () => Math.random() * 0.3 + 0.85 // 85-115% variation
  
  const todaySales = Math.round(baseDaily * variation())
  const yesterdaySales = Math.round(baseDaily * variation())
  const weekSales = Math.round(baseDaily * 7 * variation())
  const monthSales = Math.round(baseDaily * 30 * variation())
  
  const todayOrders = Math.round(todaySales / 95) // ~95 AWG average order
  const weekOrders = Math.round(weekSales / 95)
  const monthOrders = Math.round(monthSales / 95)
  
  return {
    todaySales,
    yesterdaySales,
    weekSales,
    monthSales,
    todayOrders,
    weekOrders,
    monthOrders,
    averageOrderValue: Math.round(monthSales / monthOrders),
    loading: false
  }
}

export function SalesMetricsSection() {
  const [metrics, setMetrics] = useState<SalesMetrics>({
    todaySales: 0,
    yesterdaySales: 0,
    weekSales: 0,
    monthSales: 0,
    todayOrders: 0,
    weekOrders: 0,
    monthOrders: 0,
    averageOrderValue: 0,
    loading: true
  })

  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      setMetrics(generateMockSalesData())
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return 0
    return ((current - previous) / previous) * 100
  }

  const todayGrowth = calculateGrowth(metrics.todaySales, metrics.yesterdaySales)

  const MetricCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    growth, 
    prefix = '',
    suffix = '' 
  }: {
    title: string
    value: number | string
    subtitle: string
    icon: any
    growth?: number
    prefix?: string
    suffix?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {metrics.loading ? '...' : `${prefix}${value}${suffix}`}
        </div>
        <div className="flex items-center space-x-2 text-xs">
          <p className="text-muted-foreground">{subtitle}</p>
          {growth !== undefined && !metrics.loading && (
            <div className={`flex items-center ${growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {growth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              <span>{Math.abs(growth).toFixed(1)}%</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className="mb-6 md:mb-8">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Sales Overview</h2>
        <p className="text-gray-600">Real-time sales performance and metrics</p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <MetricCard
          title="Today's Sales"
          value={metrics.todaySales}
          subtitle="vs yesterday"
          icon={DollarSign}
          growth={todayGrowth}
          prefix="AWG "
        />
        
        <MetricCard
          title="This Week"
          value={metrics.weekSales}
          subtitle={`${metrics.weekOrders} orders`}
          icon={TrendingUp}
          prefix="AWG "
        />
        
        <MetricCard
          title="This Month"
          value={metrics.monthSales}
          subtitle={`${metrics.monthOrders} orders`}
          icon={ShoppingCart}
          prefix="AWG "
        />
        
        <MetricCard
          title="Avg Order Value"
          value={metrics.averageOrderValue}
          subtitle="this month"
          icon={Package}
          prefix="AWG "
        />
      </div>
    </div>
  )
}
