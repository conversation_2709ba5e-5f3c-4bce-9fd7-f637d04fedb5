'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { format, subDays, startOfDay } from 'date-fns'

interface DailySalesData {
  date: string
  sales: number
  orders: number
  customers: number
}

interface CategorySalesData {
  name: string
  value: number
  color: string
}

// Generate mock daily sales data for the last 30 days
const generateDailySalesData = (): DailySalesData[] => {
  const data: DailySalesData[] = []
  const baseDaily = 850
  
  for (let i = 29; i >= 0; i--) {
    const date = subDays(new Date(), i)
    const variation = Math.random() * 0.4 + 0.8 // 80-120% variation
    const weekendMultiplier = date.getDay() === 0 || date.getDay() === 6 ? 1.3 : 1 // Weekend boost
    
    const sales = Math.round(baseDaily * variation * weekendMultiplier)
    const orders = Math.round(sales / (85 + Math.random() * 20)) // 85-105 AWG average
    const customers = Math.round(orders * (0.8 + Math.random() * 0.3)) // Some repeat customers
    
    data.push({
      date: format(date, 'MMM dd'),
      sales,
      orders,
      customers
    })
  }
  
  return data
}

// Generate mock category sales data
const generateCategorySalesData = (): CategorySalesData[] => {
  return [
    { name: 'Protein Powders', value: 45, color: '#3B82F6' },
    { name: 'Pre-Workout', value: 25, color: '#10B981' },
    { name: 'Vitamins', value: 15, color: '#F59E0B' },
    { name: 'BCAA/Amino', value: 10, color: '#EF4444' },
    { name: 'Other', value: 5, color: '#8B5CF6' }
  ]
}

export function RevenueChartsSection() {
  const [dailySalesData, setDailySalesData] = useState<DailySalesData[]>([])
  const [categorySalesData, setCategorySalesData] = useState<CategorySalesData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      setDailySalesData(generateDailySalesData())
      setCategorySalesData(generateCategorySalesData())
      setLoading(false)
    }, 1200)

    return () => clearTimeout(timer)
  }, [])

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'sales' && 'Sales: AWG '}
              {entry.dataKey === 'orders' && 'Orders: '}
              {entry.dataKey === 'customers' && 'Customers: '}
              {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <div className="mb-6 md:mb-8">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Revenue Analytics</h2>
          <p className="text-gray-600">Sales trends and performance insights</p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-6 md:mb-8">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Revenue Analytics</h2>
        <p className="text-gray-600">Sales trends and performance insights</p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Daily Sales Trend */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Daily Sales Trend</CardTitle>
            <CardDescription>Sales performance over the last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={dailySalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Area 
                  type="monotone" 
                  dataKey="sales" 
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Orders vs Customers */}
        <Card>
          <CardHeader>
            <CardTitle>Orders & Customers</CardTitle>
            <CardDescription>Daily order and customer trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={dailySalesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey="orders" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  dot={{ r: 3 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="customers" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                  dot={{ r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Category Sales Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
            <CardDescription>Revenue distribution across product categories</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={categorySalesData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {categorySalesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: any) => [`${value}%`, 'Share']}
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {categorySalesData.map((category, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: category.color }}
                    ></div>
                    <span>{category.name}</span>
                  </div>
                  <span className="font-medium">{category.value}%</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
