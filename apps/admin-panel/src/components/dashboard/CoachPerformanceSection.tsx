'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { Trophy, Users, DollarSign, TrendingUp, Star, Award } from 'lucide-react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

interface CoachPerformance {
  id: string
  name: string
  totalReferrals: number
  monthlyReferrals: number
  totalSales: number
  monthlySales: number
  commissionEarned: number
  monthlyCommission: number
  referralRate: number
  rank: number
  trend: 'up' | 'down' | 'stable'
}

interface MonthlyPerformanceData {
  month: string
  totalSales: number
  totalCommissions: number
  newReferrals: number
}

// Mock data generators
const generateCoachPerformanceData = (): CoachPerformance[] => {
  const coaches = [
    { name: '<PERSON>', baseReferrals: 45, baseSales: 12500 },
    { name: '<PERSON>', baseReferrals: 38, baseSales: 10800 },
    { name: '<PERSON>', baseReferrals: 32, baseSales: 9200 },
    { name: '<PERSON>', baseReferrals: 28, baseSales: 8100 }
  ]

  return coaches.map((coach, index) => {
    const monthlyVariation = 0.8 + Math.random() * 0.4 // 80-120%
    const monthlyReferrals = Math.round(coach.baseReferrals * 0.15 * monthlyVariation)
    const monthlySales = Math.round(coach.baseSales * 0.15 * monthlyVariation)
    const commissionRate = 0.05 + (index * 0.01) // 5-8% commission rate
    
    return {
      id: `coach-${index + 1}`,
      name: coach.name,
      totalReferrals: coach.baseReferrals,
      monthlyReferrals,
      totalSales: coach.baseSales,
      monthlySales,
      commissionEarned: Math.round(coach.baseSales * commissionRate),
      monthlyCommission: Math.round(monthlySales * commissionRate),
      referralRate: commissionRate * 100,
      rank: index + 1,
      trend: Math.random() > 0.3 ? 'up' : Math.random() > 0.5 ? 'stable' : 'down'
    }
  })
}

const generateMonthlyPerformanceData = (): MonthlyPerformanceData[] => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
  return months.map(month => ({
    month,
    totalSales: Math.floor(Math.random() * 15000) + 25000,
    totalCommissions: Math.floor(Math.random() * 800) + 1200,
    newReferrals: Math.floor(Math.random() * 20) + 15
  }))
}

export function CoachPerformanceSection() {
  const [coachData, setCoachData] = useState<CoachPerformance[]>([])
  const [monthlyData, setMonthlyData] = useState<MonthlyPerformanceData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      setCoachData(generateCoachPerformanceData())
      setMonthlyData(generateMonthlyPerformanceData())
      setLoading(false)
    }, 1500)

    return () => clearTimeout(timer)
  }, [])

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '↗️'
      case 'down': return '↘️'
      default: return '➡️'
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600 bg-green-50 border-green-200'
      case 'down': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800 border-yellow-300'
      case 2: return 'bg-gray-100 text-gray-800 border-gray-300'
      case 3: return 'bg-orange-100 text-orange-800 border-orange-300'
      default: return 'bg-blue-100 text-blue-800 border-blue-300'
    }
  }

  if (loading) {
    return (
      <div className="mb-6 md:mb-8">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Coach Performance</h2>
          <p className="text-gray-600">Referral tracking, commission analytics, and performance metrics</p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const totalMonthlySales = coachData.reduce((sum, coach) => sum + coach.monthlySales, 0)
  const totalMonthlyCommissions = coachData.reduce((sum, coach) => sum + coach.monthlyCommission, 0)
  const totalMonthlyReferrals = coachData.reduce((sum, coach) => sum + coach.monthlyReferrals, 0)

  return (
    <div className="mb-6 md:mb-8">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Coach Performance</h2>
        <p className="text-gray-600">Referral tracking, commission analytics, and performance metrics</p>
      </div>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Monthly Coach Sales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">AWG {totalMonthlySales.toLocaleString()}</div>
            <p className="text-xs text-green-600">+12% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <Award className="h-4 w-4 mr-2" />
              Total Commissions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">AWG {totalMonthlyCommissions.toLocaleString()}</div>
            <p className="text-xs text-blue-600">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              New Referrals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMonthlyReferrals}</div>
            <p className="text-xs text-green-600">This month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Coach Leaderboard */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              <span>Coach Leaderboard</span>
            </CardTitle>
            <CardDescription>Top performing coaches this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {coachData.map((coach) => (
                <div key={coach.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className={getRankBadgeColor(coach.rank)}>
                      #{coach.rank}
                    </Badge>
                    <div>
                      <p className="font-medium text-sm">{coach.name}</p>
                      <p className="text-xs text-gray-500">
                        {coach.monthlyReferrals} referrals • {coach.referralRate}% rate
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">AWG {coach.monthlySales.toLocaleString()}</p>
                    <div className="flex items-center space-x-1">
                      <Badge variant="outline" className={getTrendColor(coach.trend)}>
                        {getTrendIcon(coach.trend)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        AWG {coach.monthlyCommission}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Performance Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance Trends</CardTitle>
            <CardDescription>Coach program sales and commission trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <AreaChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip 
                  formatter={(value: any, name: any) => [
                    name === 'totalSales' ? `AWG ${value.toLocaleString()}` : 
                    name === 'totalCommissions' ? `AWG ${value}` : value,
                    name === 'totalSales' ? 'Sales' :
                    name === 'totalCommissions' ? 'Commissions' : 'Referrals'
                  ]}
                />
                <Area 
                  type="monotone" 
                  dataKey="totalSales" 
                  stackId="1"
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="totalCommissions" 
                  stackId="2"
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
