'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'
import { Badge } from '@nutripro/ui'
import { Users, TrendingUp, Award, CreditCard, UserCheck, UserX } from 'lucide-react'
import { CustomerQueries } from '@nutripro/database'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

interface CustomerMetrics {
  totalCustomers: number
  newCustomersThisMonth: number
  activeCustomers: number
  loyaltyMembers: number
  premiumMembers: number
  averageLoyaltyPoints: number
  totalLoyaltyPoints: number
  customerRetentionRate: number
}

interface CustomerSegmentData {
  segment: string
  count: number
  percentage: number
  color: string
}

interface LoyaltyTrendData {
  month: string
  newMembers: number
  totalPoints: number
  redemptions: number
}

// Mock data generators
const generateCustomerMetrics = (customers: any[]): CustomerMetrics => {
  const totalCustomers = customers?.length || 0
  const loyaltyMembers = customers?.filter(c => c.loyalty_points > 0).length || 0
  const premiumMembers = customers?.filter(c => c.membership_tier === 'premium').length || 0
  const totalLoyaltyPoints = customers?.reduce((sum, c) => sum + (c.loyalty_points || 0), 0) || 0
  
  return {
    totalCustomers,
    newCustomersThisMonth: Math.floor(totalCustomers * 0.15), // 15% new this month
    activeCustomers: Math.floor(totalCustomers * 0.75), // 75% active
    loyaltyMembers,
    premiumMembers,
    averageLoyaltyPoints: loyaltyMembers > 0 ? Math.round(totalLoyaltyPoints / loyaltyMembers) : 0,
    totalLoyaltyPoints,
    customerRetentionRate: 85.5 // Mock retention rate
  }
}

const generateCustomerSegmentData = (): CustomerSegmentData[] => {
  return [
    { segment: 'Regular Retail', count: 45, percentage: 60, color: '#3B82F6' },
    { segment: 'Premium Members', count: 15, percentage: 20, color: '#10B981' },
    { segment: 'Wholesale', count: 10, percentage: 13, color: '#F59E0B' },
    { segment: 'Inactive', count: 5, percentage: 7, color: '#EF4444' }
  ]
}

const generateLoyaltyTrendData = (): LoyaltyTrendData[] => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
  return months.map(month => ({
    month,
    newMembers: Math.floor(Math.random() * 15) + 5,
    totalPoints: Math.floor(Math.random() * 5000) + 2000,
    redemptions: Math.floor(Math.random() * 800) + 200
  }))
}

export function CustomerAnalyticsSection() {
  const [metrics, setMetrics] = useState<CustomerMetrics>({
    totalCustomers: 0,
    newCustomersThisMonth: 0,
    activeCustomers: 0,
    loyaltyMembers: 0,
    premiumMembers: 0,
    averageLoyaltyPoints: 0,
    totalLoyaltyPoints: 0,
    customerRetentionRate: 0
  })
  const [segmentData, setSegmentData] = useState<CustomerSegmentData[]>([])
  const [loyaltyTrendData, setLoyaltyTrendData] = useState<LoyaltyTrendData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadCustomerData = async () => {
      try {
        const customerQueries = new CustomerQueries()
        const customers = await customerQueries.getAll()
        
        setMetrics(generateCustomerMetrics(customers))
        setSegmentData(generateCustomerSegmentData())
        setLoyaltyTrendData(generateLoyaltyTrendData())
      } catch (error) {
        console.error('Failed to load customer data:', error)
        // Use mock data on error
        setSegmentData(generateCustomerSegmentData())
        setLoyaltyTrendData(generateLoyaltyTrendData())
      } finally {
        setLoading(false)
      }
    }

    loadCustomerData()
  }, [])

  const MetricCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    trend,
    prefix = '',
    suffix = '' 
  }: {
    title: string
    value: number | string
    subtitle: string
    icon: any
    trend?: 'up' | 'down' | 'neutral'
    prefix?: string
    suffix?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? '...' : `${prefix}${value}${suffix}`}
        </div>
        <div className="flex items-center space-x-2 text-xs">
          <p className="text-muted-foreground">{subtitle}</p>
          {trend && !loading && (
            <Badge variant="outline" className={
              trend === 'up' ? 'text-green-600 border-green-200' :
              trend === 'down' ? 'text-red-600 border-red-200' :
              'text-gray-600 border-gray-200'
            }>
              {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="mb-6 md:mb-8">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Customer Analytics</h2>
          <p className="text-gray-600">Customer behavior, loyalty metrics, and segmentation insights</p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="mb-6 md:mb-8">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">Customer Analytics</h2>
        <p className="text-gray-600">Customer behavior, loyalty metrics, and segmentation insights</p>
      </div>
      
      {/* Customer Metrics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
        <MetricCard
          title="Total Customers"
          value={metrics.totalCustomers}
          subtitle={`${metrics.newCustomersThisMonth} new this month`}
          icon={Users}
          trend="up"
        />
        
        <MetricCard
          title="Active Customers"
          value={metrics.activeCustomers}
          subtitle={`${Math.round((metrics.activeCustomers / metrics.totalCustomers) * 100)}% of total`}
          icon={UserCheck}
          trend="up"
        />
        
        <MetricCard
          title="Loyalty Members"
          value={metrics.loyaltyMembers}
          subtitle={`${metrics.premiumMembers} premium members`}
          icon={Award}
          trend="up"
        />
        
        <MetricCard
          title="Retention Rate"
          value={metrics.customerRetentionRate}
          subtitle="last 12 months"
          icon={TrendingUp}
          suffix="%"
          trend="up"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* Customer Segmentation */}
        <Card>
          <CardHeader>
            <CardTitle>Customer Segmentation</CardTitle>
            <CardDescription>Distribution of customers by type and status</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={segmentData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="count"
                >
                  {segmentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: any, name: any) => [`${value} customers`, 'Count']}
                />
              </PieChart>
            </ResponsiveContainer>
            <div className="mt-4 space-y-2">
              {segmentData.map((segment, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: segment.color }}
                    ></div>
                    <span>{segment.segment}</span>
                  </div>
                  <div className="text-right">
                    <span className="font-medium">{segment.count}</span>
                    <span className="text-gray-500 ml-1">({segment.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Loyalty Program Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Loyalty Program Trends</CardTitle>
            <CardDescription>Monthly loyalty member growth and point activity</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <AreaChart data={loyaltyTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="newMembers" 
                  stackId="1"
                  stroke="#3B82F6" 
                  fill="#3B82F6" 
                  fillOpacity={0.6}
                  name="New Members"
                />
                <Area 
                  type="monotone" 
                  dataKey="redemptions" 
                  stackId="2"
                  stroke="#10B981" 
                  fill="#10B981" 
                  fillOpacity={0.6}
                  name="Point Redemptions"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
