"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/test-db/page",{

/***/ "(app-pages-browser)/../../packages/database/src/queries/transactions.ts":
/*!***********************************************************!*\
  !*** ../../packages/database/src/queries/transactions.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionQueries: function() { return /* binding */ TransactionQueries; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n\nclass TransactionQueries {\n    // Generate unique transaction number with new format\n    async generateTransactionNumber(transactionType, customerType) {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = (now.getMonth() + 1).toString().padStart(2, \"0\");\n        const yearMonth = \"\".concat(year).concat(month);\n        // Determine prefix based on transaction type and customer type\n        let prefix;\n        if (transactionType === \"sale\" || transactionType === \"return\") {\n            // For orders, use OR prefix\n            prefix = \"OR\";\n        } else if (transactionType === \"wholesale_order\") {\n            prefix = \"OR\" // Orders use OR regardless of type\n            ;\n        } else {\n            prefix = \"TXN\" // Keep existing format for adjustments\n            ;\n        }\n        // Get the next sequence number for this prefix and month\n        const sequenceNumber = await this.getNextSequenceNumber(prefix, yearMonth);\n        if (prefix === \"TXN\") {\n            // Keep old format for adjustments\n            const timestamp = Date.now().toString().slice(-6);\n            const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n            return \"\".concat(prefix).concat(timestamp).concat(random);\n        }\n        return \"\".concat(prefix, \"-\").concat(yearMonth, \"-\").concat(sequenceNumber.toString().padStart(5, \"0\"));\n    }\n    // Get next sequence number for a given prefix and month\n    async getNextSequenceNumber(prefix, yearMonth) {\n        // Query for the highest sequence number for this prefix and month\n        const pattern = \"\".concat(prefix, \"-\").concat(yearMonth, \"-%\");\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"transaction_number\").like(\"transaction_number\", pattern).order(\"transaction_number\", {\n            ascending: false\n        }).limit(1);\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return 1 // First transaction of the month\n            ;\n        }\n        // Extract sequence number from the last transaction\n        const lastNumber = data[0].transaction_number;\n        const parts = lastNumber.split(\"-\");\n        if (parts.length === 3) {\n            const lastSequence = parseInt(parts[2], 10);\n            return lastSequence + 1;\n        }\n        return 1;\n    }\n    async getAll() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name,\\n          customer_type\\n        ),\\n        transaction_items (*)\\n      \").order(\"created_at\", {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (error) throw error;\n        return data;\n    }\n    async getById(id) {\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name,\\n          customer_type,\\n          email,\\n          phone\\n        ),\\n        transaction_items (\\n          *,\\n          products (\\n            id,\\n            name,\\n            sku,\\n            retail_price\\n          )\\n        )\\n      \").eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    }\n    async getByCustomer(customerId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        transaction_items (*)\\n      \").eq(\"customer_id\", customerId).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByStatus(status) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").eq(\"status\", status).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByDateRange(startDate, endDate) {\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n    async create(transactionData) {\n        const { transaction, items } = transactionData;\n        // Generate transaction number if not provided\n        const transactionNumber = transaction.transaction_number || await this.generateTransactionNumber();\n        // Start a transaction\n        const { data: newTransaction, error: transactionError } = await this.supabase.from(\"transactions\").insert({\n            ...transaction,\n            transaction_number: transactionNumber\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Insert transaction items\n        const itemsWithTransactionId = items.map((item)=>({\n                ...item,\n                transaction_id: newTransaction.id\n            }));\n        const { data: newItems, error: itemsError } = await this.supabase.from(\"transaction_items\").insert(itemsWithTransactionId).select();\n        if (itemsError) {\n            // Rollback transaction if items insertion fails\n            await this.supabase.from(\"transactions\").delete().eq(\"id\", newTransaction.id);\n            throw itemsError;\n        }\n        // Update inventory for completed sales\n        if (newTransaction.status === \"completed\" && newTransaction.transaction_type === \"sale\") {\n            await this.updateInventoryForTransaction(newTransaction.id, \"decrease\");\n        }\n        // Return the complete transaction with items\n        return {\n            ...newTransaction,\n            transaction_items: newItems\n        };\n    }\n    // Update inventory based on transaction\n    async updateInventoryForTransaction(transactionId, operation) {\n        const { data: items, error } = await this.supabase.from(\"transaction_items\").select(\"product_id, quantity\").eq(\"transaction_id\", transactionId);\n        if (error) throw error;\n        for (const item of items){\n            if (item.product_id) {\n                const { data: product, error: productError } = await this.supabase.from(\"products\").select(\"stock_quantity\").eq(\"id\", item.product_id).single();\n                if (productError) continue; // Skip if product not found\n                const newQuantity = operation === \"decrease\" ? product.stock_quantity - item.quantity : product.stock_quantity + item.quantity;\n                await this.supabase.from(\"products\").update({\n                    stock_quantity: Math.max(0, newQuantity)\n                }).eq(\"id\", item.product_id);\n            }\n        }\n    }\n    async update(id, updates) {\n        const { data, error } = await this.supabase.from(\"transactions\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateStatus(id, status, notes) {\n        // Get current transaction to check previous status\n        const { data: currentTransaction, error: fetchError } = await this.supabase.from(\"transactions\").select(\"status, transaction_type\").eq(\"id\", id).single();\n        if (fetchError) throw fetchError;\n        const updates = {\n            status\n        };\n        if (notes) {\n            updates.internal_notes = notes;\n        }\n        const { data: updatedTransaction, error: updateError } = await this.supabase.from(\"transactions\").update(updates).eq(\"id\", id).select().single();\n        if (updateError) throw updateError;\n        // Handle inventory updates based on status changes\n        const previousStatus = currentTransaction.status;\n        const transactionType = currentTransaction.transaction_type;\n        // Update inventory when completing a sale\n        if (status === \"completed\" && previousStatus !== \"completed\" && transactionType === \"sale\") {\n            await this.updateInventoryForTransaction(id, \"decrease\");\n        }\n        // Restore inventory when cancelling a completed sale\n        if (status === \"cancelled\" && previousStatus === \"completed\" && transactionType === \"sale\") {\n            await this.updateInventoryForTransaction(id, \"increase\");\n        }\n        return updatedTransaction;\n    }\n    async addItem(transactionId, item) {\n        const { data, error } = await this.supabase.from(\"transaction_items\").insert({\n            ...item,\n            transaction_id: transactionId\n        }).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateItem(itemId, updates) {\n        const { data, error } = await this.supabase.from(\"transaction_items\").update(updates).eq(\"id\", itemId).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async removeItem(itemId) {\n        const { error } = await this.supabase.from(\"transaction_items\").delete().eq(\"id\", itemId);\n        if (error) throw error;\n        return true;\n    }\n    async getStats() {\n        // Get total transactions count\n        const { data: totalTransactions, error: totalError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        });\n        // Get completed transactions count\n        const { data: completedTransactions, error: completedError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"status\", \"completed\");\n        // Get pending transactions count\n        const { data: pendingTransactions, error: pendingError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"status\", \"pending\");\n        // Get today's sales\n        const today = new Date().toISOString().split(\"T\")[0];\n        const { data: todaySales, error: todayError } = await this.supabase.from(\"transactions\").select(\"total_amount\").eq(\"status\", \"completed\").gte(\"created_at\", \"\".concat(today, \"T00:00:00\")).lte(\"created_at\", \"\".concat(today, \"T23:59:59\"));\n        if (totalError || completedError || pendingError || todayError) {\n            throw totalError || completedError || pendingError || todayError;\n        }\n        const todayTotal = (todaySales === null || todaySales === void 0 ? void 0 : todaySales.reduce((sum, t)=>sum + (t.total_amount || 0), 0)) || 0;\n        return {\n            total: (totalTransactions === null || totalTransactions === void 0 ? void 0 : totalTransactions.length) || 0,\n            completed: (completedTransactions === null || completedTransactions === void 0 ? void 0 : completedTransactions.length) || 0,\n            pending: (pendingTransactions === null || pendingTransactions === void 0 ? void 0 : pendingTransactions.length) || 0,\n            todayTotal\n        };\n    }\n    async search(query) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").or(\"transaction_number.ilike.%\".concat(query, \"%,notes.ilike.%\").concat(query, \"%\")).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    constructor(){\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/queries/transactions.ts\n"));

/***/ })

});