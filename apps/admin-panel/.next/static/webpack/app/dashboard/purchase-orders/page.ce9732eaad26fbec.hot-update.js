/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/purchase-orders/page",{

/***/ "(app-pages-browser)/../../packages/database/src/index.ts":
/*!********************************************!*\
  !*** ../../packages/database/src/index.ts ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomerQueries: function() { return /* reexport safe */ _queries_customers__WEBPACK_IMPORTED_MODULE_2__.CustomerQueries; },\n/* harmony export */   InventoryQueries: function() { return /* reexport safe */ _queries_inventory__WEBPACK_IMPORTED_MODULE_5__.InventoryQueries; },\n/* harmony export */   ProductQueries: function() { return /* reexport safe */ _queries_products__WEBPACK_IMPORTED_MODULE_1__.ProductQueries; },\n/* harmony export */   PurchaseOrderQueries: function() { return /* reexport safe */ _queries_purchase_orders__WEBPACK_IMPORTED_MODULE_4__.PurchaseOrderQueries; },\n/* harmony export */   TransactionQueries: function() { return /* reexport safe */ _queries_transactions__WEBPACK_IMPORTED_MODULE_3__.TransactionQueries; },\n/* harmony export */   buildPaginationQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildPaginationQuery; },\n/* harmony export */   buildSearchQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildSearchQuery; },\n/* harmony export */   buildSortQuery: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.buildSortQuery; },\n/* harmony export */   createClient: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient; },\n/* harmony export */   formatCurrency: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency; },\n/* harmony export */   formatDateForDB: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.formatDateForDB; },\n/* harmony export */   handleSupabaseError: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.handleSupabaseError; },\n/* harmony export */   isValidUUID: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.isValidUUID; },\n/* harmony export */   parseDBDate: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.parseDBDate; },\n/* harmony export */   supabase: function() { return /* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase; },\n/* harmony export */   withTransaction: function() { return /* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_7__.withTransaction; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n/* harmony import */ var _queries_products__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./queries/products */ \"(app-pages-browser)/../../packages/database/src/queries/products.ts\");\n/* harmony import */ var _queries_customers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./queries/customers */ \"(app-pages-browser)/../../packages/database/src/queries/customers.ts\");\n/* harmony import */ var _queries_transactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./queries/transactions */ \"(app-pages-browser)/../../packages/database/src/queries/transactions.ts\");\n/* harmony import */ var _queries_purchase_orders__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./queries/purchase-orders */ \"(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts\");\n/* harmony import */ var _queries_inventory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./queries/inventory */ \"(app-pages-browser)/../../packages/database/src/queries/inventory.ts\");\n/* harmony import */ var _types_supabase__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./types/supabase */ \"(app-pages-browser)/../../packages/database/src/types/supabase.ts\");\n/* harmony import */ var _types_supabase__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_types_supabase__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _types_supabase__WEBPACK_IMPORTED_MODULE_6__) if([\"default\",\"supabase\",\"createClient\",\"ProductQueries\",\"CustomerQueries\",\"TransactionQueries\",\"PurchaseOrderQueries\",\"InventoryQueries\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _types_supabase__WEBPACK_IMPORTED_MODULE_6__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils */ \"(app-pages-browser)/../../packages/database/src/utils.ts\");\n// Export database client\n\n// Export query functions\n\n\n\n\n\n// Export types\n\n// Export utilities\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9wYWNrYWdlcy9kYXRhYmFzZS9zcmMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHlCQUF5QjtBQUN5QjtBQUVsRCx5QkFBeUI7QUFDMkI7QUFDRTtBQUNNO0FBQ0s7QUFDVjtBQU92RCxlQUFlO0FBQ2tCO0FBRWpDLG1CQUFtQjtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9wYWNrYWdlcy9kYXRhYmFzZS9zcmMvaW5kZXgudHM/Zjg4NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnQgZGF0YWJhc2UgY2xpZW50XG5leHBvcnQgeyBzdXBhYmFzZSwgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIi4vY2xpZW50XCI7XG5cbi8vIEV4cG9ydCBxdWVyeSBmdW5jdGlvbnNcbmV4cG9ydCB7IFByb2R1Y3RRdWVyaWVzIH0gZnJvbSBcIi4vcXVlcmllcy9wcm9kdWN0c1wiO1xuZXhwb3J0IHsgQ3VzdG9tZXJRdWVyaWVzIH0gZnJvbSBcIi4vcXVlcmllcy9jdXN0b21lcnNcIjtcbmV4cG9ydCB7IFRyYW5zYWN0aW9uUXVlcmllcyB9IGZyb20gXCIuL3F1ZXJpZXMvdHJhbnNhY3Rpb25zXCI7XG5leHBvcnQgeyBQdXJjaGFzZU9yZGVyUXVlcmllcyB9IGZyb20gXCIuL3F1ZXJpZXMvcHVyY2hhc2Utb3JkZXJzXCI7XG5leHBvcnQgeyBJbnZlbnRvcnlRdWVyaWVzIH0gZnJvbSBcIi4vcXVlcmllcy9pbnZlbnRvcnlcIjtcblxuLy8gRXhwb3J0IHR5cGVzXG5leHBvcnQgdHlwZSB7IFRyYW5zYWN0aW9uV2l0aEl0ZW1zLCBDcmVhdGVUcmFuc2FjdGlvbkRhdGEgfSBmcm9tIFwiLi9xdWVyaWVzL3RyYW5zYWN0aW9uc1wiO1xuZXhwb3J0IHR5cGUgeyBQdXJjaGFzZU9yZGVyV2l0aEl0ZW1zLCBDcmVhdGVQdXJjaGFzZU9yZGVyRGF0YSB9IGZyb20gXCIuL3F1ZXJpZXMvcHVyY2hhc2Utb3JkZXJzXCI7XG5leHBvcnQgdHlwZSB7IFByb2R1Y3RCYXRjaFdpdGhQcm9kdWN0LCBJbnZlbnRvcnlBbGVydCwgRklGT0JhdGNoIH0gZnJvbSBcIi4vcXVlcmllcy9pbnZlbnRvcnlcIjtcblxuLy8gRXhwb3J0IHR5cGVzXG5leHBvcnQgKiBmcm9tIFwiLi90eXBlcy9zdXBhYmFzZVwiO1xuXG4vLyBFeHBvcnQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tIFwiLi91dGlsc1wiO1xuIl0sIm5hbWVzIjpbInN1cGFiYXNlIiwiY3JlYXRlQ2xpZW50IiwiUHJvZHVjdFF1ZXJpZXMiLCJDdXN0b21lclF1ZXJpZXMiLCJUcmFuc2FjdGlvblF1ZXJpZXMiLCJQdXJjaGFzZU9yZGVyUXVlcmllcyIsIkludmVudG9yeVF1ZXJpZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/../../packages/database/src/types/supabase.ts":
/*!*****************************************************!*\
  !*** ../../packages/database/src/types/supabase.ts ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});