"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/purchase-orders/page",{

/***/ "(app-pages-browser)/../../packages/database/src/queries/transactions.ts":
/*!***********************************************************!*\
  !*** ../../packages/database/src/queries/transactions.ts ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionQueries: function() { return /* binding */ TransactionQueries; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n\nclass TransactionQueries {\n    // Generate unique transaction number with new format\n    async generateTransactionNumber(transactionType, customerType) {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = (now.getMonth() + 1).toString().padStart(2, \"0\");\n        const yearMonth = \"\".concat(year).concat(month);\n        // Determine prefix based on transaction type and customer type\n        let prefix;\n        if (transactionType === \"sale\" || transactionType === \"return\") {\n            // For orders, use OR prefix\n            prefix = \"OR\";\n        } else if (transactionType === \"wholesale_order\") {\n            prefix = \"OR\" // Orders use OR regardless of type\n            ;\n        } else {\n            prefix = \"TXN\" // Keep existing format for adjustments\n            ;\n        }\n        // Get the next sequence number for this prefix and month\n        const sequenceNumber = await this.getNextSequenceNumber(prefix, yearMonth);\n        if (prefix === \"TXN\") {\n            // Keep old format for adjustments\n            const timestamp = Date.now().toString().slice(-6);\n            const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n            return \"\".concat(prefix).concat(timestamp).concat(random);\n        }\n        return \"\".concat(prefix, \"-\").concat(yearMonth, \"-\").concat(sequenceNumber.toString().padStart(5, \"0\"));\n    }\n    // Get next sequence number for a given prefix and month\n    async getNextSequenceNumber(prefix, yearMonth) {\n        // Query for the highest sequence number for this prefix and month\n        const pattern = \"\".concat(prefix, \"-\").concat(yearMonth, \"-%\");\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"transaction_number\").like(\"transaction_number\", pattern).order(\"transaction_number\", {\n            ascending: false\n        }).limit(1);\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return 1 // First transaction of the month\n            ;\n        }\n        // Extract sequence number from the last transaction\n        const lastNumber = data[0].transaction_number;\n        const parts = lastNumber.split(\"-\");\n        if (parts.length === 3) {\n            const lastSequence = parseInt(parts[2], 10);\n            return lastSequence + 1;\n        }\n        return 1;\n    }\n    // Generate invoice number (separate from order number)\n    async generateInvoiceNumber(customerType) {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = (now.getMonth() + 1).toString().padStart(2, \"0\");\n        const yearMonth = \"\".concat(year).concat(month);\n        // Determine prefix based on customer type\n        const prefix = customerType === \"wholesale\" ? \"WH\" : \"RE\";\n        // Get the next sequence number for this prefix and month\n        const sequenceNumber = await this.getNextInvoiceSequenceNumber(prefix, yearMonth);\n        return \"\".concat(prefix, \"-\").concat(yearMonth, \"-\").concat(sequenceNumber.toString().padStart(5, \"0\"));\n    }\n    // Get next invoice sequence number for a given prefix and month\n    async getNextInvoiceSequenceNumber(prefix, yearMonth) {\n        // Query for the highest sequence number for this prefix and month\n        const pattern = \"\".concat(prefix, \"-\").concat(yearMonth, \"-%\");\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"invoice_number\").like(\"invoice_number\", pattern).not(\"invoice_number\", \"is\", null).order(\"invoice_number\", {\n            ascending: false\n        }).limit(1);\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return 1 // First invoice of the month\n            ;\n        }\n        // Extract sequence number from the last invoice\n        const lastNumber = data[0].invoice_number;\n        if (lastNumber) {\n            const parts = lastNumber.split(\"-\");\n            if (parts.length === 3) {\n                const lastSequence = parseInt(parts[2], 10);\n                return lastSequence + 1;\n            }\n        }\n        return 1;\n    }\n    // Generate and assign invoice number to a transaction\n    async generateAndAssignInvoiceNumber(transactionId, customerType) {\n        const invoiceNumber = await this.generateInvoiceNumber(customerType);\n        const { error } = await this.supabase.from(\"transactions\").update({\n            invoice_number: invoiceNumber\n        }).eq(\"id\", transactionId);\n        if (error) throw error;\n        return invoiceNumber;\n    }\n    // Complete a transaction and generate invoice number\n    async completeTransaction(transactionId) {\n        var _transaction_customers;\n        // Get transaction details to determine customer type\n        const { data: transaction, error: fetchError } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          customer_type\\n        )\\n      \").eq(\"id\", transactionId).single();\n        if (fetchError) throw fetchError;\n        if (!transaction) throw new Error(\"Transaction not found\");\n        // Determine customer type (default to retail if no customer)\n        const customerType = ((_transaction_customers = transaction.customers) === null || _transaction_customers === void 0 ? void 0 : _transaction_customers.customer_type) || \"retail\";\n        // Generate invoice number if not already assigned\n        let invoiceNumber = transaction.invoice_number;\n        if (!invoiceNumber) {\n            invoiceNumber = await this.generateInvoiceNumber(customerType);\n        }\n        // Update transaction status and invoice number\n        const { error: updateError } = await this.supabase.from(\"transactions\").update({\n            status: \"completed\",\n            invoice_number: invoiceNumber\n        }).eq(\"id\", transactionId);\n        if (updateError) throw updateError;\n    }\n    async getAll() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name,\\n          customer_type\\n        ),\\n        transaction_items (*)\\n      \").order(\"created_at\", {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (error) throw error;\n        return data;\n    }\n    async getById(id) {\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name,\\n          customer_type,\\n          email,\\n          phone\\n        ),\\n        transaction_items (\\n          *,\\n          products (\\n            id,\\n            name,\\n            sku,\\n            retail_price\\n          )\\n        )\\n      \").eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    }\n    async getByCustomer(customerId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        transaction_items (*)\\n      \").eq(\"customer_id\", customerId).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByStatus(status) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").eq(\"status\", status).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByDateRange(startDate, endDate) {\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) throw error;\n        return data;\n    }\n    async create(transactionData) {\n        const { transaction, items } = transactionData;\n        // Get customer type if customer_id is provided\n        let customerType;\n        if (transaction.customer_id) {\n            const { data: customer, error: customerError } = await this.supabase.from(\"customers\").select(\"customer_type\").eq(\"id\", transaction.customer_id).single();\n            if (!customerError && customer) {\n                customerType = customer.customer_type;\n            }\n        }\n        // Generate transaction number if not provided\n        const transactionNumber = transaction.transaction_number || await this.generateTransactionNumber(transaction.transaction_type, customerType);\n        // Start a transaction\n        const { data: newTransaction, error: transactionError } = await this.supabase.from(\"transactions\").insert({\n            ...transaction,\n            transaction_number: transactionNumber\n        }).select().single();\n        if (transactionError) throw transactionError;\n        // Insert transaction items\n        const itemsWithTransactionId = items.map((item)=>({\n                ...item,\n                transaction_id: newTransaction.id\n            }));\n        const { data: newItems, error: itemsError } = await this.supabase.from(\"transaction_items\").insert(itemsWithTransactionId).select();\n        if (itemsError) {\n            // Rollback transaction if items insertion fails\n            await this.supabase.from(\"transactions\").delete().eq(\"id\", newTransaction.id);\n            throw itemsError;\n        }\n        // Update inventory for completed sales\n        if (newTransaction.status === \"completed\" && newTransaction.transaction_type === \"sale\") {\n            await this.updateInventoryForTransaction(newTransaction.id, \"decrease\");\n        }\n        // Return the complete transaction with items\n        return {\n            ...newTransaction,\n            transaction_items: newItems\n        };\n    }\n    // Update inventory based on transaction\n    async updateInventoryForTransaction(transactionId, operation) {\n        const { data: items, error } = await this.supabase.from(\"transaction_items\").select(\"product_id, quantity\").eq(\"transaction_id\", transactionId);\n        if (error) throw error;\n        for (const item of items){\n            if (item.product_id) {\n                const { data: product, error: productError } = await this.supabase.from(\"products\").select(\"stock_quantity\").eq(\"id\", item.product_id).single();\n                if (productError) continue; // Skip if product not found\n                const newQuantity = operation === \"decrease\" ? product.stock_quantity - item.quantity : product.stock_quantity + item.quantity;\n                await this.supabase.from(\"products\").update({\n                    stock_quantity: Math.max(0, newQuantity)\n                }).eq(\"id\", item.product_id);\n            }\n        }\n    }\n    async update(id, updates) {\n        const { data, error } = await this.supabase.from(\"transactions\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateStatus(id, status, notes) {\n        // Get current transaction to check previous status\n        const { data: currentTransaction, error: fetchError } = await this.supabase.from(\"transactions\").select(\"status, transaction_type\").eq(\"id\", id).single();\n        if (fetchError) throw fetchError;\n        const updates = {\n            status\n        };\n        if (notes) {\n            updates.internal_notes = notes;\n        }\n        const { data: updatedTransaction, error: updateError } = await this.supabase.from(\"transactions\").update(updates).eq(\"id\", id).select().single();\n        if (updateError) throw updateError;\n        // Handle inventory updates based on status changes\n        const previousStatus = currentTransaction.status;\n        const transactionType = currentTransaction.transaction_type;\n        // Update inventory when completing a sale\n        if (status === \"completed\" && previousStatus !== \"completed\" && transactionType === \"sale\") {\n            await this.updateInventoryForTransaction(id, \"decrease\");\n        }\n        // Restore inventory when cancelling a completed sale\n        if (status === \"cancelled\" && previousStatus === \"completed\" && transactionType === \"sale\") {\n            await this.updateInventoryForTransaction(id, \"increase\");\n        }\n        return updatedTransaction;\n    }\n    async addItem(transactionId, item) {\n        const { data, error } = await this.supabase.from(\"transaction_items\").insert({\n            ...item,\n            transaction_id: transactionId\n        }).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateItem(itemId, updates) {\n        const { data, error } = await this.supabase.from(\"transaction_items\").update(updates).eq(\"id\", itemId).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async removeItem(itemId) {\n        const { error } = await this.supabase.from(\"transaction_items\").delete().eq(\"id\", itemId);\n        if (error) throw error;\n        return true;\n    }\n    async getStats() {\n        // Get total transactions count\n        const { data: totalTransactions, error: totalError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        });\n        // Get completed transactions count\n        const { data: completedTransactions, error: completedError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"status\", \"completed\");\n        // Get pending transactions count\n        const { data: pendingTransactions, error: pendingError } = await this.supabase.from(\"transactions\").select(\"id\", {\n            count: \"exact\"\n        }).eq(\"status\", \"pending\");\n        // Get today's sales\n        const today = new Date().toISOString().split(\"T\")[0];\n        const { data: todaySales, error: todayError } = await this.supabase.from(\"transactions\").select(\"total_amount\").eq(\"status\", \"completed\").gte(\"created_at\", \"\".concat(today, \"T00:00:00\")).lte(\"created_at\", \"\".concat(today, \"T23:59:59\"));\n        if (totalError || completedError || pendingError || todayError) {\n            throw totalError || completedError || pendingError || todayError;\n        }\n        const todayTotal = (todaySales === null || todaySales === void 0 ? void 0 : todaySales.reduce((sum, t)=>sum + (t.total_amount || 0), 0)) || 0;\n        return {\n            total: (totalTransactions === null || totalTransactions === void 0 ? void 0 : totalTransactions.length) || 0,\n            completed: (completedTransactions === null || completedTransactions === void 0 ? void 0 : completedTransactions.length) || 0,\n            pending: (pendingTransactions === null || pendingTransactions === void 0 ? void 0 : pendingTransactions.length) || 0,\n            todayTotal\n        };\n    }\n    async search(query) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"transactions\").select(\"\\n        *,\\n        customers (\\n          id,\\n          first_name,\\n          last_name,\\n          company_name\\n        ),\\n        transaction_items (*)\\n      \").or(\"transaction_number.ilike.%\".concat(query, \"%,notes.ilike.%\").concat(query, \"%\")).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    constructor(){\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/queries/transactions.ts\n"));

/***/ })

});