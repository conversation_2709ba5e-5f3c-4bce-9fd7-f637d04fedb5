"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/orders/page",{

/***/ "(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts":
/*!**************************************************************!*\
  !*** ../../packages/database/src/queries/purchase-orders.ts ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PurchaseOrderQueries: function() { return /* binding */ PurchaseOrderQueries; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client */ \"(app-pages-browser)/../../packages/database/src/client.ts\");\n\nclass PurchaseOrderQueries {\n    // Generate unique PO number with new format\n    async generatePONumber() {\n        const now = new Date();\n        const year = now.getFullYear();\n        const month = (now.getMonth() + 1).toString().padStart(2, \"0\");\n        const yearMonth = \"\".concat(year).concat(month);\n        const prefix = \"PO\";\n        // Get the next sequence number for this prefix and month\n        const sequenceNumber = await this.getNextPOSequenceNumber(prefix, yearMonth);\n        return \"\".concat(prefix, \"-\").concat(yearMonth, \"-\").concat(sequenceNumber.toString().padStart(5, \"0\"));\n    }\n    // Get next PO sequence number for a given prefix and month\n    async getNextPOSequenceNumber(prefix, yearMonth) {\n        // Query for the highest sequence number for this prefix and month\n        const pattern = \"\".concat(prefix, \"-\").concat(yearMonth, \"-%\");\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"po_number\").like(\"po_number\", pattern).order(\"po_number\", {\n            ascending: false\n        }).limit(1);\n        if (error) throw error;\n        if (!data || data.length === 0) {\n            return 1 // First PO of the month\n            ;\n        }\n        // Extract sequence number from the last PO\n        const lastNumber = data[0].po_number;\n        const parts = lastNumber.split(\"-\");\n        if (parts.length === 3) {\n            const lastSequence = parseInt(parts[2], 10);\n            return lastSequence + 1;\n        }\n        return 1;\n    }\n    async getAll() {\n        let limit = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 50, offset = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email,\\n          preferred_currency\\n        ),\\n        purchase_order_items (*)\\n      \").order(\"created_at\", {\n            ascending: false\n        }).range(offset, offset + limit - 1);\n        if (error) throw error;\n        return data;\n    }\n    async getById(id) {\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email,\\n          preferred_currency,\\n          address_line1,\\n          city,\\n          country\\n        ),\\n        purchase_order_items (\\n          *,\\n          products (\\n            id,\\n            name,\\n            sku,\\n            purchase_price\\n          )\\n        )\\n      \").eq(\"id\", id).single();\n        if (error) throw error;\n        return data;\n    }\n    async getByVendor(vendorId) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 20;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        purchase_order_items (*)\\n      \").eq(\"vendor_id\", vendorId).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async getByStatus(status) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email\\n        ),\\n        purchase_order_items (*)\\n      \").eq(\"status\", status).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    async create(purchaseOrderData) {\n        const { purchase_order, items } = purchaseOrderData;\n        // Generate PO number if not provided\n        const poNumber = purchase_order.po_number || await this.generatePONumber();\n        // Create the purchase order\n        const { data: newPO, error: poError } = await this.supabase.from(\"purchase_orders\").insert({\n            ...purchase_order,\n            po_number: poNumber\n        }).select().single();\n        if (poError) throw poError;\n        // Insert purchase order items\n        const itemsWithPOId = items.map((item)=>({\n                ...item,\n                purchase_order_id: newPO.id\n            }));\n        const { data: newItems, error: itemsError } = await this.supabase.from(\"purchase_order_items\").insert(itemsWithPOId).select();\n        if (itemsError) {\n            // Rollback PO if items insertion fails\n            await this.supabase.from(\"purchase_orders\").delete().eq(\"id\", newPO.id);\n            throw itemsError;\n        }\n        // Return the complete PO with items\n        return {\n            ...newPO,\n            purchase_order_items: newItems\n        };\n    }\n    async update(id, updates) {\n        const { data, error } = await this.supabase.from(\"purchase_orders\").update(updates).eq(\"id\", id).select().single();\n        if (error) throw error;\n        return data;\n    }\n    async updateStatus(id, status, notes) {\n        const updates = {\n            status\n        };\n        if (notes) {\n            updates.internal_notes = notes;\n        }\n        return this.update(id, updates);\n    }\n    async receiveItems(poId, receivedItems) {\n        // Update each item with received quantities\n        for (const item of receivedItems){\n            const { error } = await this.supabase.from(\"purchase_order_items\").update({\n                quantity_received: item.quantity_received,\n                batch_number: item.batch_number,\n                expiry_date: item.expiry_date,\n                received_date: new Date().toISOString()\n            }).eq(\"id\", item.item_id);\n            if (error) throw error;\n            // Update product stock levels\n            const { data: poItem, error: itemError } = await this.supabase.from(\"purchase_order_items\").select(\"product_id, quantity_received\").eq(\"id\", item.item_id).single();\n            if (itemError) continue;\n            if (poItem.product_id) {\n                // Get current stock\n                const { data: product, error: productError } = await this.supabase.from(\"products\").select(\"stock_quantity\").eq(\"id\", poItem.product_id).single();\n                if (productError) continue;\n                // Update stock\n                await this.supabase.from(\"products\").update({\n                    stock_quantity: product.stock_quantity + item.quantity_received\n                }).eq(\"id\", poItem.product_id);\n            }\n        }\n        // Check if PO is fully received\n        const { data: poItems, error: itemsError } = await this.supabase.from(\"purchase_order_items\").select(\"quantity_ordered, quantity_received\").eq(\"purchase_order_id\", poId);\n        if (itemsError) throw itemsError;\n        const isFullyReceived = poItems.every((item)=>item.quantity_received >= item.quantity_ordered);\n        const isPartiallyReceived = poItems.some((item)=>item.quantity_received > 0);\n        // Update PO status\n        let newStatus = \"sent\";\n        if (isFullyReceived) {\n            newStatus = \"received\";\n        } else if (isPartiallyReceived) {\n            newStatus = \"partially_received\";\n        }\n        await this.updateStatus(poId, newStatus);\n        return true;\n    }\n    async getStats() {\n        // Get total POs count\n        const { data: totalPOs, error: totalError } = await this.supabase.from(\"purchase_orders\").select(\"id\", {\n            count: \"exact\"\n        });\n        // Get pending POs count\n        const { data: pendingPOs, error: pendingError } = await this.supabase.from(\"purchase_orders\").select(\"id\", {\n            count: \"exact\"\n        }).in(\"status\", [\n            \"draft\",\n            \"pending_approval\",\n            \"approved\",\n            \"sent\"\n        ]);\n        // Get this month's PO value\n        const startOfMonth = new Date();\n        startOfMonth.setDate(1);\n        startOfMonth.setHours(0, 0, 0, 0);\n        const { data: monthlyPOs, error: monthlyError } = await this.supabase.from(\"purchase_orders\").select(\"total_amount_awg\").gte(\"created_at\", startOfMonth.toISOString());\n        if (totalError || pendingError || monthlyError) {\n            throw totalError || pendingError || monthlyError;\n        }\n        const monthlyTotal = (monthlyPOs === null || monthlyPOs === void 0 ? void 0 : monthlyPOs.reduce((sum, po)=>sum + (po.total_amount_awg || 0), 0)) || 0;\n        return {\n            total: (totalPOs === null || totalPOs === void 0 ? void 0 : totalPOs.length) || 0,\n            pending: (pendingPOs === null || pendingPOs === void 0 ? void 0 : pendingPOs.length) || 0,\n            monthlyTotal\n        };\n    }\n    async search(query) {\n        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;\n        const { data, error } = await this.supabase.from(\"purchase_orders\").select(\"\\n        *,\\n        vendors (\\n          id,\\n          name,\\n          contact_email\\n        ),\\n        purchase_order_items (*)\\n      \").or(\"po_number.ilike.%\".concat(query, \"%,notes.ilike.%\").concat(query, \"%\")).order(\"created_at\", {\n            ascending: false\n        }).limit(limit);\n        if (error) throw error;\n        return data;\n    }\n    constructor(){\n        this.supabase = (0,_client__WEBPACK_IMPORTED_MODULE_0__.createClient)();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../packages/database/src/queries/purchase-orders.ts\n"));

/***/ })

});