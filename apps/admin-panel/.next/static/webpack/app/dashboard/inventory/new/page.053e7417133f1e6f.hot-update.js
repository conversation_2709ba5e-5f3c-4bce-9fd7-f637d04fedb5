"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/inventory/new/page",{

/***/ "(app-pages-browser)/./src/components/products/ProductForm.tsx":
/*!*************************************************!*\
  !*** ./src/components/products/ProductForm.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductForm: function() { return /* binding */ ProductForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/../../node_modules/.pnpm/react-hook-form@7.60.0_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/../../node_modules/.pnpm/@hookform+resolvers@3.10.0_react-hook-form@7.60.0/node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/../../node_modules/.pnpm/zod@3.25.75/node_modules/zod/v3/types.js\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Input!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Input!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Textarea_nutripro_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Textarea!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Textarea!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-switch */ \"(app-pages-browser)/../../node_modules/.pnpm/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-switch/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-select */ \"(app-pages-browser)/../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-label */ \"(app-pages-browser)/../../node_modules/.pnpm/@radix-ui+react-label@2.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/warehouse.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pill.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,DollarSign,Info,Package,Pill,Settings,Warehouse!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _CategorySelect__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CategorySelect */ \"(app-pages-browser)/./src/components/products/CategorySelect.tsx\");\n/* harmony import */ var _BrandSelect__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./BrandSelect */ \"(app-pages-browser)/./src/components/products/BrandSelect.tsx\");\n/* harmony import */ var _VendorSelect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./VendorSelect */ \"(app-pages-browser)/./src/components/products/VendorSelect.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProductForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Form validation schema\nconst productFormSchema = zod__WEBPACK_IMPORTED_MODULE_10__.object({\n    // Basic Information\n    name: zod__WEBPACK_IMPORTED_MODULE_10__.string().min(1, \"Product name is required\").max(200, \"Name too long\"),\n    sku: zod__WEBPACK_IMPORTED_MODULE_10__.string().min(1, \"SKU is required\").max(50, \"SKU too long\"),\n    barcode: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    description: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    brand_id: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    vendor_id: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    // Pricing\n    retail_price: zod__WEBPACK_IMPORTED_MODULE_10__.number().min(0.01, \"Retail price must be greater than 0\"),\n    wholesale_price: zod__WEBPACK_IMPORTED_MODULE_10__.number().optional(),\n    purchase_price: zod__WEBPACK_IMPORTED_MODULE_10__.number().optional(),\n    landing_cost: zod__WEBPACK_IMPORTED_MODULE_10__.number().optional(),\n    purchase_currency: zod__WEBPACK_IMPORTED_MODULE_10__.string().default(\"AWG\"),\n    wholesale_available: zod__WEBPACK_IMPORTED_MODULE_10__.boolean().default(false),\n    // Inventory\n    stock_quantity: zod__WEBPACK_IMPORTED_MODULE_10__.number().int().min(0, \"Stock cannot be negative\").default(0),\n    min_stock_level: zod__WEBPACK_IMPORTED_MODULE_10__.number().int().min(0, \"Minimum stock cannot be negative\").default(0),\n    max_stock_level: zod__WEBPACK_IMPORTED_MODULE_10__.number().int().optional(),\n    // Product Details\n    weight: zod__WEBPACK_IMPORTED_MODULE_10__.number().optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    // Supplement Specific\n    serving_size: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    servings_per_container: zod__WEBPACK_IMPORTED_MODULE_10__.number().int().optional(),\n    ingredients: zod__WEBPACK_IMPORTED_MODULE_10__.array(zod__WEBPACK_IMPORTED_MODULE_10__.string()).default([]),\n    allergens: zod__WEBPACK_IMPORTED_MODULE_10__.array(zod__WEBPACK_IMPORTED_MODULE_10__.string()).default([]),\n    expiry_tracking: zod__WEBPACK_IMPORTED_MODULE_10__.boolean().default(false),\n    // Variants\n    has_variants: zod__WEBPACK_IMPORTED_MODULE_10__.boolean().default(false),\n    variant_type: zod__WEBPACK_IMPORTED_MODULE_10__.string().optional(),\n    // Wholesale\n    min_order_quantity: zod__WEBPACK_IMPORTED_MODULE_10__.number().int().min(1, \"Minimum order quantity must be at least 1\").default(1)\n});\nfunction ProductForm(param) {\n    let { initialData, onSubmit, onCancel, isLoading = false, mode } = param;\n    var _watch, _watch1;\n    _s();\n    const [ingredientInput, setIngredientInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [allergenInput, setAllergenInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { register, handleSubmit, watch, setValue, formState: { errors }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(productFormSchema),\n        defaultValues: {\n            purchase_currency: \"AWG\",\n            wholesale_available: false,\n            stock_quantity: 0,\n            min_stock_level: 0,\n            expiry_tracking: false,\n            has_variants: false,\n            min_order_quantity: 1,\n            ingredients: [],\n            allergens: [],\n            ...initialData\n        }\n    });\n    const watchedFields = watch([\n        \"wholesale_available\",\n        \"has_variants\",\n        \"expiry_tracking\",\n        \"ingredients\",\n        \"allergens\"\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (initialData) {\n            reset({\n                purchase_currency: \"AWG\",\n                wholesale_available: false,\n                stock_quantity: 0,\n                min_stock_level: 0,\n                expiry_tracking: false,\n                has_variants: false,\n                min_order_quantity: 1,\n                ingredients: [],\n                allergens: [],\n                ...initialData\n            });\n        }\n    }, [\n        initialData,\n        reset\n    ]);\n    const addIngredient = ()=>{\n        if (ingredientInput.trim()) {\n            const currentIngredients = watch(\"ingredients\") || [];\n            setValue(\"ingredients\", [\n                ...currentIngredients,\n                ingredientInput.trim()\n            ]);\n            setIngredientInput(\"\");\n        }\n    };\n    const removeIngredient = (index)=>{\n        const currentIngredients = watch(\"ingredients\") || [];\n        setValue(\"ingredients\", currentIngredients.filter((_, i)=>i !== index));\n    };\n    const addAllergen = ()=>{\n        if (allergenInput.trim()) {\n            const currentAllergens = watch(\"allergens\") || [];\n            setValue(\"allergens\", [\n                ...currentAllergens,\n                allergenInput.trim()\n            ]);\n            setAllergenInput(\"\");\n        }\n    };\n    const removeAllergen = (index)=>{\n        const currentAllergens = watch(\"allergens\") || [];\n        setValue(\"allergens\", currentAllergens.filter((_, i)=>i !== index));\n    };\n    const generateSKU = ()=>{\n        const name = watch(\"name\");\n        if (name) {\n            const sku = name.toUpperCase().replace(/[^A-Z0-9]/g, \"\").substring(0, 10) + \"-\" + Date.now().toString().slice(-4);\n            setValue(\"sku\", sku);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit(onSubmit),\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Basic Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Essential product details and identification\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"name\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Product Name *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"name\",\n                                                ...register(\"name\"),\n                                                placeholder: \"Enter product name\",\n                                                className: errors.name ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.name.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                        htmlFor: \"sku\",\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"SKU *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: generateSKU,\n                                                        disabled: !watch(\"name\"),\n                                                        children: \"Generate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"sku\",\n                                                ...register(\"sku\"),\n                                                placeholder: \"Enter SKU\",\n                                                className: errors.sku ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.sku && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.sku.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"barcode\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Barcode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"barcode\",\n                                                ...register(\"barcode\"),\n                                                placeholder: \"Enter barcode\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CategorySelect__WEBPACK_IMPORTED_MODULE_7__.CategorySelect, {\n                                        value: watch(\"category_id\"),\n                                        onValueChange: (value)=>setValue(\"category_id\", value),\n                                        label: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BrandSelect__WEBPACK_IMPORTED_MODULE_8__.BrandSelect, {\n                                        value: watch(\"brand_id\"),\n                                        onValueChange: (value)=>setValue(\"brand_id\", value),\n                                        label: \"Brand\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VendorSelect__WEBPACK_IMPORTED_MODULE_9__.VendorSelect, {\n                                        value: watch(\"vendor_id\"),\n                                        onValueChange: (value)=>setValue(\"vendor_id\", value),\n                                        label: \"Vendor\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        htmlFor: \"description\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Description\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Textarea_nutripro_ui__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        id: \"description\",\n                                        ...register(\"description\"),\n                                        placeholder: \"Enter product description\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Set retail, wholesale, and cost prices\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"retail_price\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Retail Price (AWG) *\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"retail_price\",\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                min: \"0\",\n                                                ...register(\"retail_price\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0.00\",\n                                                className: errors.retail_price ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.retail_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.retail_price.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"purchase_price\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Purchase Price\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"purchase_price\",\n                                                        type: \"number\",\n                                                        step: \"0.01\",\n                                                        min: \"0\",\n                                                        ...register(\"purchase_price\", {\n                                                            valueAsNumber: true\n                                                        }),\n                                                        placeholder: \"0.00\",\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Root, {\n                                                        value: watch(\"purchase_currency\") || undefined,\n                                                        onValueChange: (value)=>setValue(\"purchase_currency\", value),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Trigger, {\n                                                                className: \"w-20 flex h-10 items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Value, {}, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                        lineNumber: 313,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4 opacity-50\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Portal, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Content, {\n                                                                    className: \"relative z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Viewport, {\n                                                                        className: \"p-1\",\n                                                                        children: [\n                                                                            \"AWG\",\n                                                                            \"USD\",\n                                                                            \"EUR\"\n                                                                        ].map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.Item, {\n                                                                                value: currency,\n                                                                                className: \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.ItemIndicator, {\n                                                                                        className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                            className: \"h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                                            lineNumber: 328,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                                        lineNumber: 327,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_15__.ItemText, {\n                                                                                        children: currency\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                                        lineNumber: 330,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, currency, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                                lineNumber: 322,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"landing_cost\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Landing Cost (AWG)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"landing_cost\",\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                min: \"0\",\n                                                ...register(\"landing_cost\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0.00\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"wholesale_price\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Wholesale Price (AWG)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"wholesale_price\",\n                                                type: \"number\",\n                                                step: \"0.01\",\n                                                min: \"0\",\n                                                ...register(\"wholesale_price\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0.00\",\n                                                disabled: !watch(\"wholesale_available\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Root, {\n                                        checked: watch(\"wholesale_available\"),\n                                        onCheckedChange: (checked)=>setValue(\"wholesale_available\", checked),\n                                        className: \"relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Thumb, {\n                                            className: \"block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        className: \"text-sm font-medium cursor-pointer\",\n                                        children: \"Available for wholesale\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Inventory\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Stock levels and inventory management\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"stock_quantity\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Current Stock\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"stock_quantity\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                ...register(\"stock_quantity\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0\",\n                                                className: errors.stock_quantity ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.stock_quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.stock_quantity.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"min_stock_level\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Minimum Stock Level\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"min_stock_level\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                ...register(\"min_stock_level\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0\",\n                                                className: errors.min_stock_level ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.min_stock_level && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.min_stock_level.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"max_stock_level\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Maximum Stock Level\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"max_stock_level\",\n                                                type: \"number\",\n                                                min: \"0\",\n                                                ...register(\"max_stock_level\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"Optional\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"min_order_quantity\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Minimum Order Quantity\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"min_order_quantity\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                ...register(\"min_order_quantity\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"1\",\n                                                className: errors.min_order_quantity ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.min_order_quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600\",\n                                                children: errors.min_order_quantity.message\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"weight\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Weight (grams)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"weight\",\n                                                type: \"number\",\n                                                step: \"0.1\",\n                                                min: \"0\",\n                                                ...register(\"weight\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"0.0\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Root, {\n                                        checked: watch(\"expiry_tracking\"),\n                                        onCheckedChange: (checked)=>setValue(\"expiry_tracking\", checked),\n                                        className: \"relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Thumb, {\n                                            className: \"block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        className: \"text-sm font-medium cursor-pointer\",\n                                        children: \"Enable expiry tracking\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Supplement Information\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Nutrition facts and supplement-specific details\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"serving_size\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Serving Size\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"serving_size\",\n                                                ...register(\"serving_size\"),\n                                                placeholder: \"e.g., 1 scoop (30g)\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                                htmlFor: \"servings_per_container\",\n                                                className: \"text-sm font-medium\",\n                                                children: \"Servings per Container\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"servings_per_container\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                ...register(\"servings_per_container\", {\n                                                    valueAsNumber: true\n                                                }),\n                                                placeholder: \"30\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Ingredients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: ingredientInput,\n                                                        onChange: (e)=>setIngredientInput(e.target.value),\n                                                        placeholder: \"Add ingredient\",\n                                                        onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addIngredient())\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: addIngredient,\n                                                        disabled: !ingredientInput.trim(),\n                                                        children: \"Add\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this),\n                                            ((_watch = watch(\"ingredients\")) === null || _watch === void 0 ? void 0 : _watch.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: watch(\"ingredients\").map((ingredient, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 bg-gray-100 rounded-md px-2 py-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: ingredient\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeIngredient(index),\n                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Allergens\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        value: allergenInput,\n                                                        onChange: (e)=>setAllergenInput(e.target.value),\n                                                        placeholder: \"Add allergen\",\n                                                        onKeyPress: (e)=>e.key === \"Enter\" && (e.preventDefault(), addAllergen())\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: addAllergen,\n                                                        disabled: !allergenInput.trim(),\n                                                        children: \"Add\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 15\n                                            }, this),\n                                            ((_watch1 = watch(\"allergens\")) === null || _watch1 === void 0 ? void 0 : _watch1.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: watch(\"allergens\").map((allergen, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 bg-red-100 rounded-md px-2 py-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: allergen\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>removeAllergen(index),\n                                                                className: \"text-red-500 hover:text-red-700\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Product Variants\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 627,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Configure product variants and options\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Root, {\n                                        checked: watch(\"has_variants\"),\n                                        onCheckedChange: (checked)=>setValue(\"has_variants\", checked),\n                                        className: \"relative h-6 w-11 cursor-default rounded-full bg-gray-200 outline-none data-[state=checked]:bg-blue-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_18__.Thumb, {\n                                            className: \"block h-5 w-5 translate-x-0.5 rounded-full bg-white transition-transform duration-100 will-change-transform data-[state=checked]:translate-x-[22px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        className: \"text-sm font-medium cursor-pointer\",\n                                        children: \"This product has variants\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this),\n                            watch(\"has_variants\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                        htmlFor: \"variant_type\",\n                                        className: \"text-sm font-medium\",\n                                        children: \"Variant Type\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        id: \"variant_type\",\n                                        ...register(\"variant_type\"),\n                                        placeholder: \"e.g., Flavor, Size, Color\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500\",\n                                        children: \"Specify what varies between product options (e.g., Flavor for different flavors)\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_DollarSign_Info_Package_Pill_Settings_Warehouse_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Additional Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                children: \"Notes and additional product information\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                                    htmlFor: \"notes\",\n                                    className: \"text-sm font-medium\",\n                                    children: \"Notes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                    lineNumber: 678,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Textarea_nutripro_ui__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                    id: \"notes\",\n                                    ...register(\"notes\"),\n                                    placeholder: \"Internal notes about this product\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-end space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        disabled: isLoading,\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 693,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading,\n                        children: isLoading ? \"Saving...\" : mode === \"create\" ? \"Create Product\" : \"Update Product\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n                lineNumber: 692,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/components/products/ProductForm.tsx\",\n        lineNumber: 160,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductForm, \"h6++3jvGds6mIiDcQPqeISdKILo=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_11__.useForm\n    ];\n});\n_c = ProductForm;\nvar _c;\n$RefreshReg$(_c, \"ProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/products/ProductForm.tsx\n"));

/***/ })

});