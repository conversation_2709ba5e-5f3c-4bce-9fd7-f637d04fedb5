"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(app-pages-browser)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Reports\",\n        href: \"/dashboard\",\n        icon: \"\\uD83D\\uDCCA\",\n        hasSubmenu: true,\n        submenu: [\n            {\n                name: \"Sales summary\",\n                href: \"/dashboard/reports/sales-summary\",\n                icon: \"\\uD83D\\uDCC8\"\n            },\n            {\n                name: \"Sales by item\",\n                href: \"/dashboard/reports/sales-by-item\",\n                icon: \"\\uD83D\\uDCE6\"\n            },\n            {\n                name: \"Sales by category\",\n                href: \"/dashboard/reports/sales-by-category\",\n                icon: \"\\uD83D\\uDCC2\"\n            },\n            {\n                name: \"Sales by employee\",\n                href: \"/dashboard/reports/sales-by-employee\",\n                icon: \"\\uD83D\\uDC64\"\n            },\n            {\n                name: \"Sales by payment type\",\n                href: \"/dashboard/reports/sales-by-payment\",\n                icon: \"\\uD83D\\uDCB3\"\n            },\n            {\n                name: \"Receipts\",\n                href: \"/dashboard/reports/receipts\",\n                icon: \"\\uD83E\\uDDFE\"\n            },\n            {\n                name: \"Sales by modifier\",\n                href: \"/dashboard/reports/sales-by-modifier\",\n                icon: \"\\uD83D\\uDD27\"\n            },\n            {\n                name: \"Discounts\",\n                href: \"/dashboard/reports/discounts\",\n                icon: \"\\uD83C\\uDFF7️\"\n            },\n            {\n                name: \"Taxes\",\n                href: \"/dashboard/reports/taxes\",\n                icon: \"\\uD83D\\uDCB0\"\n            }\n        ]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/dashboard/inventory\",\n        icon: \"\\uD83D\\uDCE6\"\n    },\n    {\n        name: \"Clients\",\n        href: \"/dashboard/clients\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        name: \"Retail\",\n        href: \"/dashboard/retail\",\n        icon: \"\\uD83D\\uDED2\"\n    },\n    {\n        name: \"Wholesale\",\n        href: \"/dashboard/wholesale\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        name: \"Purchase Orders\",\n        href: \"/dashboard/purchase-orders\",\n        icon: \"\\uD83D\\uDE9A\"\n    },\n    {\n        name: \"Coaches\",\n        href: \"/dashboard/coaches\",\n        icon: \"\\uD83C\\uDFC3‍♂️\"\n    },\n    {\n        name: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: \"⚙️\"\n    }\n];\nfunction DashboardLayout(param) {\n    let { children } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, signOut } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        \"Reports\"\n    ]) // Reports expanded by default\n    ;\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n        } catch (error) {\n            console.error(\"Sign out failed:\", error);\n        }\n    };\n    const toggleSubmenu = (menuName)=>{\n        setExpandedMenus((prev)=>prev.includes(menuName) ? prev.filter((name)=>name !== menuName) : [\n                ...prev,\n                menuName\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-col md:w-64 bg-white shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"NutriPro Admin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-4 py-6 space-y-1\",\n                        children: navigation.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 \".concat(isActive ? \"bg-blue-50 text-blue-700 border-r-2 border-blue-700\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-3 text-lg\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, item.name, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: (user === null || user === void 0 ? void 0 : user.email) || \"Demo User\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleSignOut,\n                                    className: \"ml-3 flex-shrink-0\",\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-4 py-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"NutriPro Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleSignOut,\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"lqbAO4P+iCMDJpEXF01Kkw3yBOo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTRCO0FBQ2lCO0FBQ0Q7QUFDUDtBQUNMO0FBR2hDLE1BQU1LLGFBQWE7SUFDakI7UUFDRUMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsWUFBWTtRQUNaQyxTQUFTO1lBQ1A7Z0JBQUVKLE1BQU07Z0JBQWlCQyxNQUFNO2dCQUFvQ0MsTUFBTTtZQUFLO1lBQzlFO2dCQUFFRixNQUFNO2dCQUFpQkMsTUFBTTtnQkFBb0NDLE1BQU07WUFBSztZQUM5RTtnQkFBRUYsTUFBTTtnQkFBcUJDLE1BQU07Z0JBQXdDQyxNQUFNO1lBQUs7WUFDdEY7Z0JBQUVGLE1BQU07Z0JBQXFCQyxNQUFNO2dCQUF3Q0MsTUFBTTtZQUFLO1lBQ3RGO2dCQUFFRixNQUFNO2dCQUF5QkMsTUFBTTtnQkFBdUNDLE1BQU07WUFBSztZQUN6RjtnQkFBRUYsTUFBTTtnQkFBWUMsTUFBTTtnQkFBK0JDLE1BQU07WUFBSztZQUNwRTtnQkFBRUYsTUFBTTtnQkFBcUJDLE1BQU07Z0JBQXdDQyxNQUFNO1lBQUs7WUFDdEY7Z0JBQUVGLE1BQU07Z0JBQWFDLE1BQU07Z0JBQWdDQyxNQUFNO1lBQU07WUFDdkU7Z0JBQUVGLE1BQU07Z0JBQVNDLE1BQU07Z0JBQTRCQyxNQUFNO1lBQUs7U0FDL0Q7SUFDSDtJQUNBO1FBQUVGLE1BQU07UUFBYUMsTUFBTTtRQUF3QkMsTUFBTTtJQUFLO0lBQzlEO1FBQUVGLE1BQU07UUFBV0MsTUFBTTtRQUFzQkMsTUFBTTtJQUFLO0lBQzFEO1FBQUVGLE1BQU07UUFBVUMsTUFBTTtRQUFxQkMsTUFBTTtJQUFLO0lBQ3hEO1FBQUVGLE1BQU07UUFBYUMsTUFBTTtRQUF3QkMsTUFBTTtJQUFLO0lBQzlEO1FBQUVGLE1BQU07UUFBbUJDLE1BQU07UUFBOEJDLE1BQU07SUFBSztJQUMxRTtRQUFFRixNQUFNO1FBQVdDLE1BQU07UUFBc0JDLE1BQU07SUFBUTtJQUM3RDtRQUFFRixNQUFNO1FBQVlDLE1BQU07UUFBdUJDLE1BQU07SUFBSztDQUM3RDtBQUVjLFNBQVNHLGdCQUFnQixLQUl2QztRQUp1QyxFQUN0Q0MsUUFBUSxFQUdULEdBSnVDOztJQUt0QyxNQUFNQyxXQUFXWiw0REFBV0E7SUFDNUIsTUFBTSxFQUFFYSxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHYiwwREFBT0E7SUFDakMsTUFBTSxDQUFDYyxlQUFlQyxpQkFBaUIsR0FBR2IsK0NBQVFBLENBQVc7UUFBQztLQUFVLEVBQUUsOEJBQThCOztJQUV4RyxNQUFNYyxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLE1BQU1IO1FBQ1IsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxvQkFBb0JBO1FBQ3BDO0lBQ0Y7SUFFQSxNQUFNRSxnQkFBZ0IsQ0FBQ0M7UUFDckJMLGlCQUFpQk0sQ0FBQUEsT0FDZkEsS0FBS0MsUUFBUSxDQUFDRixZQUNWQyxLQUFLRSxNQUFNLENBQUNuQixDQUFBQSxPQUFRQSxTQUFTZ0IsWUFDN0I7bUJBQUlDO2dCQUFNRDthQUFTO0lBRTNCO0lBRUEscUJBQ0UsOERBQUNJO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBRWIsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBR0QsV0FBVTtzQ0FBK0I7Ozs7Ozs7Ozs7O2tDQUkvQyw4REFBQ0U7d0JBQUlGLFdBQVU7a0NBQ1p0QixXQUFXeUIsR0FBRyxDQUFDLENBQUNDOzRCQUNmLE1BQU1DLFdBQVduQixhQUFha0IsS0FBS3hCLElBQUk7NEJBQ3ZDLHFCQUNFLDhEQUFDUCxrREFBSUE7Z0NBRUhPLE1BQU13QixLQUFLeEIsSUFBSTtnQ0FDZm9CLFdBQVcsMEZBSVYsT0FIQ0ssV0FDSSx3REFDQTs7a0RBR04sOERBQUNDO3dDQUFLTixXQUFVO2tEQUFnQkksS0FBS3ZCLElBQUk7Ozs7OztvQ0FDeEN1QixLQUFLekIsSUFBSTs7K0JBVEx5QixLQUFLekIsSUFBSTs7Ozs7d0JBWXBCOzs7Ozs7a0NBSUYsOERBQUNvQjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUNWYixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1xQixLQUFLLEtBQUk7Ozs7OztzREFFbEIsOERBQUNEOzRDQUFFUCxXQUFVO3NEQUF3Qjs7Ozs7Ozs7Ozs7OzhDQUV2Qyw4REFBQ3hCLDZFQUFNQTtvQ0FDTGlDLFNBQVE7b0NBQ1JDLE1BQUs7b0NBQ0xDLFNBQVNwQjtvQ0FDVFMsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVAsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUdELFdBQVU7OENBQXNDOzs7Ozs7OENBQ3BELDhEQUFDeEIsNkVBQU1BO29DQUNMaUMsU0FBUTtvQ0FDUkMsTUFBSztvQ0FDTEMsU0FBU3BCOzhDQUNWOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPTCw4REFBQ3FCO3dCQUFLWixXQUFVO2tDQUNiZjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS1g7R0FuR3dCRDs7UUFLTFYsd0RBQVdBO1FBQ0ZDLHNEQUFPQTs7O0tBTlhTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL2xheW91dC50c3g/NzIzYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9saWIvYXV0aC1jb250ZXh0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQG51dHJpcHJvL3VpJ1xuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENoZXZyb25Eb3duLCBDaGV2cm9uUmlnaHQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IG5hdmlnYXRpb24gPSBbXG4gIHtcbiAgICBuYW1lOiAnUmVwb3J0cycsXG4gICAgaHJlZjogJy9kYXNoYm9hcmQnLFxuICAgIGljb246ICfwn5OKJyxcbiAgICBoYXNTdWJtZW51OiB0cnVlLFxuICAgIHN1Ym1lbnU6IFtcbiAgICAgIHsgbmFtZTogJ1NhbGVzIHN1bW1hcnknLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3NhbGVzLXN1bW1hcnknLCBpY29uOiAn8J+TiCcgfSxcbiAgICAgIHsgbmFtZTogJ1NhbGVzIGJ5IGl0ZW0nLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3NhbGVzLWJ5LWl0ZW0nLCBpY29uOiAn8J+TpicgfSxcbiAgICAgIHsgbmFtZTogJ1NhbGVzIGJ5IGNhdGVnb3J5JywgaHJlZjogJy9kYXNoYm9hcmQvcmVwb3J0cy9zYWxlcy1ieS1jYXRlZ29yeScsIGljb246ICfwn5OCJyB9LFxuICAgICAgeyBuYW1lOiAnU2FsZXMgYnkgZW1wbG95ZWUnLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3NhbGVzLWJ5LWVtcGxveWVlJywgaWNvbjogJ/CfkaQnIH0sXG4gICAgICB7IG5hbWU6ICdTYWxlcyBieSBwYXltZW50IHR5cGUnLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3NhbGVzLWJ5LXBheW1lbnQnLCBpY29uOiAn8J+SsycgfSxcbiAgICAgIHsgbmFtZTogJ1JlY2VpcHRzJywgaHJlZjogJy9kYXNoYm9hcmQvcmVwb3J0cy9yZWNlaXB0cycsIGljb246ICfwn6e+JyB9LFxuICAgICAgeyBuYW1lOiAnU2FsZXMgYnkgbW9kaWZpZXInLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3NhbGVzLWJ5LW1vZGlmaWVyJywgaWNvbjogJ/CflKcnIH0sXG4gICAgICB7IG5hbWU6ICdEaXNjb3VudHMnLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL2Rpc2NvdW50cycsIGljb246ICfwn4+377iPJyB9LFxuICAgICAgeyBuYW1lOiAnVGF4ZXMnLCBocmVmOiAnL2Rhc2hib2FyZC9yZXBvcnRzL3RheGVzJywgaWNvbjogJ/CfkrAnIH0sXG4gICAgXVxuICB9LFxuICB7IG5hbWU6ICdJbnZlbnRvcnknLCBocmVmOiAnL2Rhc2hib2FyZC9pbnZlbnRvcnknLCBpY29uOiAn8J+TpicgfSxcbiAgeyBuYW1lOiAnQ2xpZW50cycsIGhyZWY6ICcvZGFzaGJvYXJkL2NsaWVudHMnLCBpY29uOiAn8J+RpScgfSxcbiAgeyBuYW1lOiAnUmV0YWlsJywgaHJlZjogJy9kYXNoYm9hcmQvcmV0YWlsJywgaWNvbjogJ/Cfm5InIH0sXG4gIHsgbmFtZTogJ1dob2xlc2FsZScsIGhyZWY6ICcvZGFzaGJvYXJkL3dob2xlc2FsZScsIGljb246ICfwn4+iJyB9LFxuICB7IG5hbWU6ICdQdXJjaGFzZSBPcmRlcnMnLCBocmVmOiAnL2Rhc2hib2FyZC9wdXJjaGFzZS1vcmRlcnMnLCBpY29uOiAn8J+amicgfSxcbiAgeyBuYW1lOiAnQ29hY2hlcycsIGhyZWY6ICcvZGFzaGJvYXJkL2NvYWNoZXMnLCBpY29uOiAn8J+Pg+KAjeKZgu+4jycgfSxcbiAgeyBuYW1lOiAnU2V0dGluZ3MnLCBocmVmOiAnL2Rhc2hib2FyZC9zZXR0aW5ncycsIGljb246ICfimpnvuI8nIH0sXG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKVxuICBjb25zdCB7IHVzZXIsIHNpZ25PdXQgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbZXhwYW5kZWRNZW51cywgc2V0RXhwYW5kZWRNZW51c10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oWydSZXBvcnRzJ10pIC8vIFJlcG9ydHMgZXhwYW5kZWQgYnkgZGVmYXVsdFxuXG4gIGNvbnN0IGhhbmRsZVNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdTaWduIG91dCBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdG9nZ2xlU3VibWVudSA9IChtZW51TmFtZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0RXhwYW5kZWRNZW51cyhwcmV2ID0+XG4gICAgICBwcmV2LmluY2x1ZGVzKG1lbnVOYW1lKVxuICAgICAgICA/IHByZXYuZmlsdGVyKG5hbWUgPT4gbmFtZSAhPT0gbWVudU5hbWUpXG4gICAgICAgIDogWy4uLnByZXYsIG1lbnVOYW1lXVxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLWdyYXktMTAwXCI+XG4gICAgICB7LyogU2lkZWJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggbWQ6ZmxleC1jb2wgbWQ6dy02NCBiZy13aGl0ZSBzaGFkb3ctbGdcIj5cbiAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC0xNiBweC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMFwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlXCI+TnV0cmlQcm8gQWRtaW48L2gxPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS02IHNwYWNlLXktMVwiPlxuICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4ge1xuICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBwYXRobmFtZSA9PT0gaXRlbS5ocmVmXG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTMgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgICAgICAgaXNBY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCB0ZXh0LWJsdWUtNzAwIGJvcmRlci1yLTIgYm9yZGVyLWJsdWUtNzAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgaG92ZXI6dGV4dC1ncmF5LTkwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1yLTMgdGV4dC1sZ1wiPntpdGVtLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIClcbiAgICAgICAgICB9KX1cbiAgICAgICAgPC9uYXY+XG5cbiAgICAgICAgey8qIFVzZXIgSW5mbyAmIFNpZ24gT3V0ICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgYmctZ3JheS01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAge3VzZXI/LmVtYWlsIHx8ICdEZW1vIFVzZXInfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPkFkbWluaXN0cmF0b3I8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVTaWduT3V0fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0zIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBTaWduIE91dFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIENvbnRlbnQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICB7LyogTW9iaWxlIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW4gYmctd2hpdGUgc2hhZG93LXNtIGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTQgcHktM1wiPlxuICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+TnV0cmlQcm8gQWRtaW48L2gxPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNpZ25PdXR9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFNpZ24gT3V0XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFBhZ2UgQ29udGVudCAqL31cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0byBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJ1c2VQYXRobmFtZSIsInVzZUF1dGgiLCJCdXR0b24iLCJ1c2VTdGF0ZSIsIm5hdmlnYXRpb24iLCJuYW1lIiwiaHJlZiIsImljb24iLCJoYXNTdWJtZW51Iiwic3VibWVudSIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwicGF0aG5hbWUiLCJ1c2VyIiwic2lnbk91dCIsImV4cGFuZGVkTWVudXMiLCJzZXRFeHBhbmRlZE1lbnVzIiwiaGFuZGxlU2lnbk91dCIsImVycm9yIiwiY29uc29sZSIsInRvZ2dsZVN1Ym1lbnUiLCJtZW51TmFtZSIsInByZXYiLCJpbmNsdWRlcyIsImZpbHRlciIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwibmF2IiwibWFwIiwiaXRlbSIsImlzQWN0aXZlIiwic3BhbiIsInAiLCJlbWFpbCIsInZhcmlhbnQiLCJzaXplIiwib25DbGljayIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/layout.tsx\n"));

/***/ })

});