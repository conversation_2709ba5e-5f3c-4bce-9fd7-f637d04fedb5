"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+react-dom@2.1.4_react-dom@18.3.1_react@18.3.1/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-size@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;