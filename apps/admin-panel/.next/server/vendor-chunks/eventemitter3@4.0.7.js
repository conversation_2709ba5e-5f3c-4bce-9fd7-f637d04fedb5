"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventemitter3@4.0.7";
exports.ids = ["vendor-chunks/eventemitter3@4.0.7"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js":
/*!****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js ***!
  \****************************************************************************************/
/***/ ((module) => {

eval("\nvar has = Object.prototype.hasOwnProperty, prefix = \"~\";\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */ function Events() {}\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n    Events.prototype = Object.create(null);\n    //\n    // This hack is needed because the `__proto__` property is still inherited in\n    // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n    //\n    if (!new Events().__proto__) prefix = false;\n}\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */ function EE(fn, context, once) {\n    this.fn = fn;\n    this.context = context;\n    this.once = once || false;\n}\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */ function addListener(emitter, event, fn, context, once) {\n    if (typeof fn !== \"function\") {\n        throw new TypeError(\"The listener must be a function\");\n    }\n    var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;\n    if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n    else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n    else emitter._events[evt] = [\n        emitter._events[evt],\n        listener\n    ];\n    return emitter;\n}\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */ function clearEvent(emitter, evt) {\n    if (--emitter._eventsCount === 0) emitter._events = new Events();\n    else delete emitter._events[evt];\n}\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */ function EventEmitter() {\n    this._events = new Events();\n    this._eventsCount = 0;\n}\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */ EventEmitter.prototype.eventNames = function eventNames() {\n    var names = [], events, name;\n    if (this._eventsCount === 0) return names;\n    for(name in events = this._events){\n        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n    }\n    if (Object.getOwnPropertySymbols) {\n        return names.concat(Object.getOwnPropertySymbols(events));\n    }\n    return names;\n};\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */ EventEmitter.prototype.listeners = function listeners(event) {\n    var evt = prefix ? prefix + event : event, handlers = this._events[evt];\n    if (!handlers) return [];\n    if (handlers.fn) return [\n        handlers.fn\n    ];\n    for(var i = 0, l = handlers.length, ee = new Array(l); i < l; i++){\n        ee[i] = handlers[i].fn;\n    }\n    return ee;\n};\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */ EventEmitter.prototype.listenerCount = function listenerCount(event) {\n    var evt = prefix ? prefix + event : event, listeners = this._events[evt];\n    if (!listeners) return 0;\n    if (listeners.fn) return 1;\n    return listeners.length;\n};\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */ EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n    var evt = prefix ? prefix + event : event;\n    if (!this._events[evt]) return false;\n    var listeners = this._events[evt], len = arguments.length, args, i;\n    if (listeners.fn) {\n        if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n        switch(len){\n            case 1:\n                return listeners.fn.call(listeners.context), true;\n            case 2:\n                return listeners.fn.call(listeners.context, a1), true;\n            case 3:\n                return listeners.fn.call(listeners.context, a1, a2), true;\n            case 4:\n                return listeners.fn.call(listeners.context, a1, a2, a3), true;\n            case 5:\n                return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n            case 6:\n                return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n        }\n        for(i = 1, args = new Array(len - 1); i < len; i++){\n            args[i - 1] = arguments[i];\n        }\n        listeners.fn.apply(listeners.context, args);\n    } else {\n        var length = listeners.length, j;\n        for(i = 0; i < length; i++){\n            if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n            switch(len){\n                case 1:\n                    listeners[i].fn.call(listeners[i].context);\n                    break;\n                case 2:\n                    listeners[i].fn.call(listeners[i].context, a1);\n                    break;\n                case 3:\n                    listeners[i].fn.call(listeners[i].context, a1, a2);\n                    break;\n                case 4:\n                    listeners[i].fn.call(listeners[i].context, a1, a2, a3);\n                    break;\n                default:\n                    if (!args) for(j = 1, args = new Array(len - 1); j < len; j++){\n                        args[j - 1] = arguments[j];\n                    }\n                    listeners[i].fn.apply(listeners[i].context, args);\n            }\n        }\n    }\n    return true;\n};\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */ EventEmitter.prototype.on = function on(event, fn, context) {\n    return addListener(this, event, fn, context, false);\n};\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */ EventEmitter.prototype.once = function once(event, fn, context) {\n    return addListener(this, event, fn, context, true);\n};\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */ EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n    var evt = prefix ? prefix + event : event;\n    if (!this._events[evt]) return this;\n    if (!fn) {\n        clearEvent(this, evt);\n        return this;\n    }\n    var listeners = this._events[evt];\n    if (listeners.fn) {\n        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {\n            clearEvent(this, evt);\n        }\n    } else {\n        for(var i = 0, events = [], length = listeners.length; i < length; i++){\n            if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {\n                events.push(listeners[i]);\n            }\n        }\n        //\n        // Reset the array, or remove it completely if we have no more listeners.\n        //\n        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n        else clearEvent(this, evt);\n    }\n    return this;\n};\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */ EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n    var evt;\n    if (event) {\n        evt = prefix ? prefix + event : event;\n        if (this._events[evt]) clearEvent(this, evt);\n    } else {\n        this._events = new Events();\n        this._eventsCount = 0;\n    }\n    return this;\n};\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n//\n// Expose the module.\n//\nif (true) {\n    module.exports = EventEmitter;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/eventemitter3@4.0.7/node_modules/eventemitter3/index.js\n");

/***/ })

};
;