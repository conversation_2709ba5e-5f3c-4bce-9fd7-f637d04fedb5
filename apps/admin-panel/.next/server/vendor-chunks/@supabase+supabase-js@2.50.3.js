"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.50.3";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.50.3"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */ class SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */ constructor(supabaseUrl, supabaseKey, options){\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl) throw new Error(\"supabaseUrl is required.\");\n        if (!supabaseKey) throw new Error(\"supabaseKey is required.\");\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL(\"realtime/v1\", baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace(\"http\", \"ws\");\n        this.authUrl = new URL(\"auth/v1\", baseUrl);\n        this.storageUrl = new URL(\"storage/v1\", baseUrl);\n        this.functionsUrl = new URL(\"functions/v1\", baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split(\".\")[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), {\n                storageKey: defaultStorageKey\n            }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : \"\";\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        } else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop)=>{\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                }\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({\n            headers: this.headers,\n            accessToken: this._getAccessToken.bind(this)\n        }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL(\"rest/v1\", baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */ get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */ get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */ from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */ schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */ channel(name, opts = {\n        config: {}\n    }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */ getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */ removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */ removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function*() {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: \"Authorization\" in this.headers\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), {\n            params: Object.assign({\n                apikey: this.supabaseKey\n            }, options === null || options === void 0 ? void 0 : options.params)\n        }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session)=>{\n            this._handleTokenChanged(event, \"CLIENT\", session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === \"TOKEN_REFRESHED\" || event === \"SIGNED_IN\") && this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        } else if (event === \"SIGNED_OUT\") {\n            this.realtime.setAuth();\n            if (source == \"STORAGE\") this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n} //# sourceMappingURL=SupabaseClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */ const createClient = (supabaseUrl, supabaseKey, options)=>{\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjMvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdXBhYmFzZS1qcy9kaXN0L21vZHVsZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEM7QUFDWjtBQUN1QjtBQUM4RTtBQUNqRztBQUN1QjtBQUM3RDs7Q0FFQyxHQUNNLE1BQU1RLGVBQWUsQ0FBQ0MsYUFBYUMsYUFBYUM7SUFDbkQsT0FBTyxJQUFJWCx1REFBY0EsQ0FBQ1MsYUFBYUMsYUFBYUM7QUFDeEQsRUFBRSxDQUNGLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMy9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzPzQ3MmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFN1cGFiYXNlQ2xpZW50IGZyb20gJy4vU3VwYWJhc2VDbGllbnQnO1xuZXhwb3J0ICogZnJvbSAnQHN1cGFiYXNlL2F1dGgtanMnO1xuZXhwb3J0IHsgUG9zdGdyZXN0RXJyb3IsIH0gZnJvbSAnQHN1cGFiYXNlL3Bvc3RncmVzdC1qcyc7XG5leHBvcnQgeyBGdW5jdGlvbnNIdHRwRXJyb3IsIEZ1bmN0aW9uc0ZldGNoRXJyb3IsIEZ1bmN0aW9uc1JlbGF5RXJyb3IsIEZ1bmN0aW9uc0Vycm9yLCBGdW5jdGlvblJlZ2lvbiwgfSBmcm9tICdAc3VwYWJhc2UvZnVuY3Rpb25zLWpzJztcbmV4cG9ydCAqIGZyb20gJ0BzdXBhYmFzZS9yZWFsdGltZS1qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIFN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnLi9TdXBhYmFzZUNsaWVudCc7XG4vKipcbiAqIENyZWF0ZXMgYSBuZXcgU3VwYWJhc2UgQ2xpZW50LlxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlQ2xpZW50ID0gKHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgb3B0aW9ucykgPT4ge1xuICAgIHJldHVybiBuZXcgU3VwYWJhc2VDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCBvcHRpb25zKTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOlsiU3VwYWJhc2VDbGllbnQiLCJQb3N0Z3Jlc3RFcnJvciIsIkZ1bmN0aW9uc0h0dHBFcnJvciIsIkZ1bmN0aW9uc0ZldGNoRXJyb3IiLCJGdW5jdGlvbnNSZWxheUVycm9yIiwiRnVuY3Rpb25zRXJyb3IiLCJGdW5jdGlvblJlZ2lvbiIsImRlZmF1bHQiLCJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInN1cGFiYXNlS2V5Iiwib3B0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options){\n        super(options);\n    }\n} //# sourceMappingURL=SupabaseAuthClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjMvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdXBhYmFzZS1qcy9kaXN0L21vZHVsZS9saWIvU3VwYWJhc2VBdXRoQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStDO0FBQ3hDLE1BQU1DLDJCQUEyQkQseURBQVVBO0lBQzlDRSxZQUFZQyxPQUFPLENBQUU7UUFDakIsS0FBSyxDQUFDQTtJQUNWO0FBQ0osRUFDQSw4Q0FBOEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjMvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdXBhYmFzZS1qcy9kaXN0L21vZHVsZS9saWIvU3VwYWJhc2VBdXRoQ2xpZW50LmpzP2VlMmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXV0aENsaWVudCB9IGZyb20gJ0BzdXBhYmFzZS9hdXRoLWpzJztcbmV4cG9ydCBjbGFzcyBTdXBhYmFzZUF1dGhDbGllbnQgZXh0ZW5kcyBBdXRoQ2xpZW50IHtcbiAgICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgICAgIHN1cGVyKG9wdGlvbnMpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVN1cGFiYXNlQXV0aENsaWVudC5qcy5tYXAiXSwibmFtZXMiOlsiQXV0aENsaWVudCIsIlN1cGFiYXNlQXV0aENsaWVudCIsImNvbnN0cnVjdG9yIiwib3B0aW9ucyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = \"\";\n// @ts-ignore\nif (typeof Deno !== \"undefined\") {\n    JS_ENV = \"deno\";\n} else if (typeof document !== \"undefined\") {\n    JS_ENV = \"web\";\n} else if (typeof navigator !== \"undefined\" && navigator.product === \"ReactNative\") {\n    JS_ENV = \"react-native\";\n} else {\n    JS_ENV = \"node\";\n}\nconst DEFAULT_HEADERS = {\n    \"X-Client-Info\": `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`\n};\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: \"public\"\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: \"implicit\"\n};\nconst DEFAULT_REALTIME_OPTIONS = {}; //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch)=>{\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    } else if (typeof fetch === \"undefined\") {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    } else {\n        _fetch = fetch;\n    }\n    return (...args)=>_fetch(...args);\n};\nconst resolveHeadersConstructor = ()=>{\n    if (typeof Headers === \"undefined\") {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch)=>{\n    const fetch1 = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init)=>__awaiter(void 0, void 0, void 0, function*() {\n            var _a;\n            const accessToken = (_a = yield getAccessToken()) !== null && _a !== void 0 ? _a : supabaseKey;\n            let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n            if (!headers.has(\"apikey\")) {\n                headers.set(\"apikey\", supabaseKey);\n            }\n            if (!headers.has(\"Authorization\")) {\n                headers.set(\"Authorization\", `Bearer ${accessToken}`);\n            }\n            return fetch1(input, Object.assign(Object.assign({}, init), {\n                headers\n            }));\n        });\n}; //# sourceMappingURL=fetch.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n        var r = Math.random() * 16 | 0, v = c == \"x\" ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith(\"/\") ? url : url + \"/\";\n}\nconst isBrowser = ()=>\"undefined\" !== \"undefined\";\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), {\n            headers: Object.assign(Object.assign({}, (_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {}), (_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})\n        }),\n        accessToken: ()=>__awaiter(this, void 0, void 0, function*() {\n                return \"\";\n            })\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    } else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n} //# sourceMappingURL=helpers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = \"2.50.3\"; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjMvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdXBhYmFzZS1qcy9kaXN0L21vZHVsZS9saWIvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU8sTUFBTUEsVUFBVSxTQUFTLENBQ2hDLG1DQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMy9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzPzRiOGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi41MC4zJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.3/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;