"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+storage-js@2.7.1";
exports.ids = ["vendor-chunks/@supabase+storage-js@2.7.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageClient: () => (/* binding */ StorageClient)\n/* harmony export */ });\n/* harmony import */ var _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./packages/StorageFileApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\");\n/* harmony import */ var _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./packages/StorageBucketApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\");\n\n\nclass StorageClient extends _packages_StorageBucketApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n    constructor(url, headers = {}, fetch){\n        super(url, headers, fetch);\n    }\n    /**\n     * Perform file operation in a bucket.\n     *\n     * @param id The bucket id to operate on.\n     */ from(id) {\n        return new _packages_StorageFileApi__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.url, this.headers, id, this.fetch);\n    }\n} //# sourceMappingURL=StorageClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3RvcmFnZS1qcy9kaXN0L21vZHVsZS9TdG9yYWdlQ2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUF1RDtBQUNJO0FBQ3BELE1BQU1FLHNCQUFzQkQsa0VBQWdCQTtJQUMvQ0UsWUFBWUMsR0FBRyxFQUFFQyxVQUFVLENBQUMsQ0FBQyxFQUFFQyxLQUFLLENBQUU7UUFDbEMsS0FBSyxDQUFDRixLQUFLQyxTQUFTQztJQUN4QjtJQUNBOzs7O0tBSUMsR0FDREMsS0FBS0MsRUFBRSxFQUFFO1FBQ0wsT0FBTyxJQUFJUixnRUFBY0EsQ0FBQyxJQUFJLENBQUNJLEdBQUcsRUFBRSxJQUFJLENBQUNDLE9BQU8sRUFBRUcsSUFBSSxJQUFJLENBQUNGLEtBQUs7SUFDcEU7QUFDSixFQUNBLHlDQUF5QyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N0b3JhZ2UtanNAMi43LjEvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zdG9yYWdlLWpzL2Rpc3QvbW9kdWxlL1N0b3JhZ2VDbGllbnQuanM/MDA3OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3RvcmFnZUZpbGVBcGkgZnJvbSAnLi9wYWNrYWdlcy9TdG9yYWdlRmlsZUFwaSc7XG5pbXBvcnQgU3RvcmFnZUJ1Y2tldEFwaSBmcm9tICcuL3BhY2thZ2VzL1N0b3JhZ2VCdWNrZXRBcGknO1xuZXhwb3J0IGNsYXNzIFN0b3JhZ2VDbGllbnQgZXh0ZW5kcyBTdG9yYWdlQnVja2V0QXBpIHtcbiAgICBjb25zdHJ1Y3Rvcih1cmwsIGhlYWRlcnMgPSB7fSwgZmV0Y2gpIHtcbiAgICAgICAgc3VwZXIodXJsLCBoZWFkZXJzLCBmZXRjaCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBlcmZvcm0gZmlsZSBvcGVyYXRpb24gaW4gYSBidWNrZXQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgVGhlIGJ1Y2tldCBpZCB0byBvcGVyYXRlIG9uLlxuICAgICAqL1xuICAgIGZyb20oaWQpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBTdG9yYWdlRmlsZUFwaSh0aGlzLnVybCwgdGhpcy5oZWFkZXJzLCBpZCwgdGhpcy5mZXRjaCk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9U3RvcmFnZUNsaWVudC5qcy5tYXAiXSwibmFtZXMiOlsiU3RvcmFnZUZpbGVBcGkiLCJTdG9yYWdlQnVja2V0QXBpIiwiU3RvcmFnZUNsaWVudCIsImNvbnN0cnVjdG9yIiwidXJsIiwiaGVhZGVycyIsImZldGNoIiwiZnJvbSIsImlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js\");\n\nconst DEFAULT_HEADERS = {\n    \"X-Client-Info\": `storage-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`\n}; //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3RvcmFnZS1qcy9kaXN0L21vZHVsZS9saWIvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DO0FBQzdCLE1BQU1DLGtCQUFrQjtJQUFFLGlCQUFpQixDQUFDLFdBQVcsRUFBRUQsNkNBQU9BLENBQUMsQ0FBQztBQUFDLEVBQUUsQ0FDNUUscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3RvcmFnZS1qc0AyLjcuMS9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N0b3JhZ2UtanMvZGlzdC9tb2R1bGUvbGliL2NvbnN0YW50cy5qcz8xNTdiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24nO1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfSEVBREVSUyA9IHsgJ1gtQ2xpZW50LUluZm8nOiBgc3RvcmFnZS1qcy8ke3ZlcnNpb259YCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29uc3RhbnRzLmpzLm1hcCJdLCJuYW1lcyI6WyJ2ZXJzaW9uIiwiREVGQVVMVF9IRUFERVJTIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageApiError: () => (/* binding */ StorageApiError),\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageUnknownError: () => (/* binding */ StorageUnknownError),\n/* harmony export */   isStorageError: () => (/* binding */ isStorageError)\n/* harmony export */ });\nclass StorageError extends Error {\n    constructor(message){\n        super(message);\n        this.__isStorageError = true;\n        this.name = \"StorageError\";\n    }\n}\nfunction isStorageError(error) {\n    return typeof error === \"object\" && error !== null && \"__isStorageError\" in error;\n}\nclass StorageApiError extends StorageError {\n    constructor(message, status){\n        super(message);\n        this.name = \"StorageApiError\";\n        this.status = status;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status\n        };\n    }\n}\nclass StorageUnknownError extends StorageError {\n    constructor(message, originalError){\n        super(message);\n        this.name = \"StorageUnknownError\";\n        this.originalError = originalError;\n    }\n} //# sourceMappingURL=errors.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   head: () => (/* binding */ head),\n/* harmony export */   post: () => (/* binding */ post),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   remove: () => (/* binding */ remove)\n/* harmony export */ });\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nconst _getErrorMessage = (err)=>err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst handleError = (error, reject, options)=>__awaiter(void 0, void 0, void 0, function*() {\n        const Res = yield (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveResponse)();\n        if (error instanceof Res && !(options === null || options === void 0 ? void 0 : options.noResolveJson)) {\n            error.json().then((err)=>{\n                reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageApiError(_getErrorMessage(err), error.status || 500));\n            }).catch((err)=>{\n                reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(err), err));\n            });\n        } else {\n            reject(new _errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError(_getErrorMessage(error), error));\n        }\n    });\nconst _getRequestParams = (method, options, parameters, body)=>{\n    const params = {\n        method,\n        headers: (options === null || options === void 0 ? void 0 : options.headers) || {}\n    };\n    if (method === \"GET\") {\n        return params;\n    }\n    params.headers = Object.assign({\n        \"Content-Type\": \"application/json\"\n    }, options === null || options === void 0 ? void 0 : options.headers);\n    if (body) {\n        params.body = JSON.stringify(body);\n    }\n    return Object.assign(Object.assign({}, params), parameters);\n};\nfunction _handleRequest(fetcher, method, url, options, parameters, body) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return new Promise((resolve, reject)=>{\n            fetcher(url, _getRequestParams(method, options, parameters, body)).then((result)=>{\n                if (!result.ok) throw result;\n                if (options === null || options === void 0 ? void 0 : options.noResolveJson) return result;\n                return result.json();\n            }).then((data)=>resolve(data)).catch((error)=>handleError(error, reject, options));\n        });\n    });\n}\nfunction get(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return _handleRequest(fetcher, \"GET\", url, options, parameters);\n    });\n}\nfunction post(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return _handleRequest(fetcher, \"POST\", url, options, parameters, body);\n    });\n}\nfunction put(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return _handleRequest(fetcher, \"PUT\", url, options, parameters, body);\n    });\n}\nfunction head(fetcher, url, options, parameters) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return _handleRequest(fetcher, \"HEAD\", url, Object.assign(Object.assign({}, options), {\n            noResolveJson: true\n        }), parameters);\n    });\n}\nfunction remove(fetcher, url, body, options, parameters) {\n    return __awaiter(this, void 0, void 0, function*() {\n        return _handleRequest(fetcher, \"DELETE\", url, options, parameters, body);\n    });\n} //# sourceMappingURL=fetch.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   recursiveToCamel: () => (/* binding */ recursiveToCamel),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveResponse: () => (/* binding */ resolveResponse)\n/* harmony export */ });\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nconst resolveFetch = (customFetch)=>{\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    } else if (typeof fetch === \"undefined\") {\n        _fetch = (...args)=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch1 })=>fetch1(...args));\n    } else {\n        _fetch = fetch;\n    }\n    return (...args)=>_fetch(...args);\n};\nconst resolveResponse = ()=>__awaiter(void 0, void 0, void 0, function*() {\n        if (typeof Response === \"undefined\") {\n            // @ts-ignore\n            return (yield Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23))).Response;\n        }\n        return Response;\n    });\nconst recursiveToCamel = (item)=>{\n    if (Array.isArray(item)) {\n        return item.map((el)=>recursiveToCamel(el));\n    } else if (typeof item === \"function\" || item !== Object(item)) {\n        return item;\n    }\n    const result = {};\n    Object.entries(item).forEach(([key, value])=>{\n        const newKey = key.replace(/([-_][a-z])/gi, (c)=>c.toUpperCase().replace(/[-_]/g, \"\"));\n        result[newKey] = recursiveToCamel(value);\n    });\n    return result;\n}; //# sourceMappingURL=helpers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n// generated by genversion\nconst version = \"2.7.1\"; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3RvcmFnZS1qcy9kaXN0L21vZHVsZS9saWIvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsMEJBQTBCO0FBQ25CLE1BQU1BLFVBQVUsUUFBUSxDQUMvQixtQ0FBbUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStzdG9yYWdlLWpzQDIuNy4xL25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3RvcmFnZS1qcy9kaXN0L21vZHVsZS9saWIvdmVyc2lvbi5qcz81MzExIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGdlbmVyYXRlZCBieSBnZW52ZXJzaW9uXG5leHBvcnQgY29uc3QgdmVyc2lvbiA9ICcyLjcuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6WyJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js":
/*!**************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageBucketApi)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\nclass StorageBucketApi {\n    constructor(url, headers = {}, fetch){\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_HEADERS), headers);\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n    }\n    /**\n     * Retrieves the details of all Storage buckets within an existing project.\n     */ listBuckets() {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket`, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing Storage bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to retrieve.\n     */ getBucket(id) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/bucket/${id}`, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a new Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are creating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations. By default, buckets are private.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     * @returns newly created bucket id\n     */ createBucket(id, options = {\n        public: false\n    }) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes\n                }, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Updates a Storage bucket\n     *\n     * @param id A unique identifier for the bucket you are updating.\n     * @param options.public The visibility of the bucket. Public buckets don't require an authorization token to download objects, but still require a valid token for all other operations.\n     * @param options.fileSizeLimit specifies the max file size in bytes that can be uploaded to this bucket.\n     * The global file size limit takes precedence over this value.\n     * The default value is null, which doesn't set a per bucket file size limit.\n     * @param options.allowedMimeTypes specifies the allowed mime types that this bucket can accept during upload.\n     * The default value is null, which allows files with all mime types to be uploaded.\n     * Each mime type specified can be a wildcard, e.g. image/*, or a specific mime type, e.g. image/png.\n     */ updateBucket(id, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.put)(this.fetch, `${this.url}/bucket/${id}`, {\n                    id,\n                    name: id,\n                    public: options.public,\n                    file_size_limit: options.fileSizeLimit,\n                    allowed_mime_types: options.allowedMimeTypes\n                }, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Removes all objects inside a single bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to empty.\n     */ emptyBucket(id) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/bucket/${id}/empty`, {}, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Deletes an existing bucket. A bucket can't be deleted with existing objects inside it.\n     * You must first `empty()` the bucket.\n     *\n     * @param id The unique identifier of the bucket you would like to delete.\n     */ deleteBucket(id) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.remove)(this.fetch, `${this.url}/bucket/${id}`, {}, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n} //# sourceMappingURL=StorageBucketApi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js":
/*!************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StorageFileApi)\n/* harmony export */ });\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/lib/helpers.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\nconst DEFAULT_SEARCH_OPTIONS = {\n    limit: 100,\n    offset: 0,\n    sortBy: {\n        column: \"name\",\n        order: \"asc\"\n    }\n};\nconst DEFAULT_FILE_OPTIONS = {\n    cacheControl: \"3600\",\n    contentType: \"text/plain;charset=UTF-8\",\n    upsert: false\n};\nclass StorageFileApi {\n    constructor(url, headers = {}, bucketId, fetch){\n        this.url = url;\n        this.headers = headers;\n        this.bucketId = bucketId;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.resolveFetch)(fetch);\n    }\n    /**\n     * Uploads a file to an existing bucket or replaces an existing file at the specified path with a new one.\n     *\n     * @param method HTTP method.\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */ uploadOrUpdate(method, path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                let body;\n                const options = Object.assign(Object.assign({}, DEFAULT_FILE_OPTIONS), fileOptions);\n                let headers = Object.assign(Object.assign({}, this.headers), method === \"POST\" && {\n                    \"x-upsert\": String(options.upsert)\n                });\n                const metadata = options.metadata;\n                if (typeof Blob !== \"undefined\" && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append(\"cacheControl\", options.cacheControl);\n                    if (metadata) {\n                        body.append(\"metadata\", this.encodeMetadata(metadata));\n                    }\n                    body.append(\"\", fileBody);\n                } else if (typeof FormData !== \"undefined\" && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append(\"cacheControl\", options.cacheControl);\n                    if (metadata) {\n                        body.append(\"metadata\", this.encodeMetadata(metadata));\n                    }\n                } else {\n                    body = fileBody;\n                    headers[\"cache-control\"] = `max-age=${options.cacheControl}`;\n                    headers[\"content-type\"] = options.contentType;\n                    if (metadata) {\n                        headers[\"x-metadata\"] = this.toBase64(this.encodeMetadata(metadata));\n                    }\n                }\n                if (fileOptions === null || fileOptions === void 0 ? void 0 : fileOptions.headers) {\n                    headers = Object.assign(Object.assign({}, headers), fileOptions.headers);\n                }\n                const cleanPath = this._removeEmptyFolders(path);\n                const _path = this._getFinalPath(cleanPath);\n                const res = yield this.fetch(`${this.url}/object/${_path}`, Object.assign({\n                    method,\n                    body: body,\n                    headers\n                }, (options === null || options === void 0 ? void 0 : options.duplex) ? {\n                    duplex: options.duplex\n                } : {}));\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: {\n                            path: cleanPath,\n                            id: data.Id,\n                            fullPath: data.Key\n                        },\n                        error: null\n                    };\n                } else {\n                    const error = data;\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Uploads a file to an existing bucket.\n     *\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */ upload(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function*() {\n            return this.uploadOrUpdate(\"POST\", path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Upload a file with a token generated from `createSignedUploadUrl`.\n     * @param path The file path, including the file name. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to upload.\n     * @param token The token generated from `createSignedUploadUrl`\n     * @param fileBody The body of the file to be stored in the bucket.\n     */ uploadToSignedUrl(path, token, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function*() {\n            const cleanPath = this._removeEmptyFolders(path);\n            const _path = this._getFinalPath(cleanPath);\n            const url = new URL(this.url + `/object/upload/sign/${_path}`);\n            url.searchParams.set(\"token\", token);\n            try {\n                let body;\n                const options = Object.assign({\n                    upsert: DEFAULT_FILE_OPTIONS.upsert\n                }, fileOptions);\n                const headers = Object.assign(Object.assign({}, this.headers), {\n                    \"x-upsert\": String(options.upsert)\n                });\n                if (typeof Blob !== \"undefined\" && fileBody instanceof Blob) {\n                    body = new FormData();\n                    body.append(\"cacheControl\", options.cacheControl);\n                    body.append(\"\", fileBody);\n                } else if (typeof FormData !== \"undefined\" && fileBody instanceof FormData) {\n                    body = fileBody;\n                    body.append(\"cacheControl\", options.cacheControl);\n                } else {\n                    body = fileBody;\n                    headers[\"cache-control\"] = `max-age=${options.cacheControl}`;\n                    headers[\"content-type\"] = options.contentType;\n                }\n                const res = yield this.fetch(url.toString(), {\n                    method: \"PUT\",\n                    body: body,\n                    headers\n                });\n                const data = yield res.json();\n                if (res.ok) {\n                    return {\n                        data: {\n                            path: cleanPath,\n                            fullPath: data.Key\n                        },\n                        error: null\n                    };\n                } else {\n                    const error = data;\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed upload URL.\n     * Signed upload URLs can be used to upload files to the bucket without further authentication.\n     * They are valid for 2 hours.\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param options.upsert If set to true, allows the file to be overwritten if it already exists.\n     */ createSignedUploadUrl(path, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                let _path = this._getFinalPath(path);\n                const headers = Object.assign({}, this.headers);\n                if (options === null || options === void 0 ? void 0 : options.upsert) {\n                    headers[\"x-upsert\"] = \"true\";\n                }\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/upload/sign/${_path}`, {}, {\n                    headers\n                });\n                const url = new URL(this.url + data.url);\n                const token = url.searchParams.get(\"token\");\n                if (!token) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_1__.StorageError(\"No token returned by API\");\n                }\n                return {\n                    data: {\n                        signedUrl: url.toString(),\n                        path,\n                        token\n                    },\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Replaces an existing file at the specified path with a new one.\n     *\n     * @param path The relative file path. Should be of the format `folder/subfolder/filename.png`. The bucket must already exist before attempting to update.\n     * @param fileBody The body of the file to be stored in the bucket.\n     */ update(path, fileBody, fileOptions) {\n        return __awaiter(this, void 0, void 0, function*() {\n            return this.uploadOrUpdate(\"PUT\", path, fileBody, fileOptions);\n        });\n    }\n    /**\n     * Moves an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-new.png`.\n     * @param options The destination options.\n     */ move(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/move`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n                }, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Copies an existing file to a new path in the same bucket.\n     *\n     * @param fromPath The original file path, including the current file name. For example `folder/image.png`.\n     * @param toPath The new file path, including the new file name. For example `folder/image-copy.png`.\n     * @param options The destination options.\n     */ copy(fromPath, toPath, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/copy`, {\n                    bucketId: this.bucketId,\n                    sourceKey: fromPath,\n                    destinationKey: toPath,\n                    destinationBucket: options === null || options === void 0 ? void 0 : options.destinationBucket\n                }, {\n                    headers: this.headers\n                });\n                return {\n                    data: {\n                        path: data.Key\n                    },\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates a signed URL. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param path The file path, including the current file name. For example `folder/image.png`.\n     * @param expiresIn The number of seconds until the signed URL expires. For example, `60` for a URL which is valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */ createSignedUrl(path, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                let _path = this._getFinalPath(path);\n                let data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/sign/${_path}`, Object.assign({\n                    expiresIn\n                }, (options === null || options === void 0 ? void 0 : options.transform) ? {\n                    transform: options.transform\n                } : {}), {\n                    headers: this.headers\n                });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? \"\" : options.download}` : \"\";\n                const signedUrl = encodeURI(`${this.url}${data.signedURL}${downloadQueryParam}`);\n                data = {\n                    signedUrl\n                };\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Creates multiple signed URLs. Use a signed URL to share a file for a fixed amount of time.\n     *\n     * @param paths The file paths to be downloaded, including the current file names. For example `['folder/image.png', 'folder2/image2.png']`.\n     * @param expiresIn The number of seconds until the signed URLs expire. For example, `60` for URLs which are valid for one minute.\n     * @param options.download triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     */ createSignedUrls(paths, expiresIn, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/sign/${this.bucketId}`, {\n                    expiresIn,\n                    paths\n                }, {\n                    headers: this.headers\n                });\n                const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `&download=${options.download === true ? \"\" : options.download}` : \"\";\n                return {\n                    data: data.map((datum)=>Object.assign(Object.assign({}, datum), {\n                            signedUrl: datum.signedURL ? encodeURI(`${this.url}${datum.signedURL}${downloadQueryParam}`) : null\n                        })),\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Downloads a file from a private bucket. For public buckets, make a request to the URL returned from `getPublicUrl` instead.\n     *\n     * @param path The full path and file name of the file to be downloaded. For example `folder/image.png`.\n     * @param options.transform Transform the asset before serving it to the client.\n     */ download(path, options) {\n        return __awaiter(this, void 0, void 0, function*() {\n            const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== \"undefined\";\n            const renderPath = wantsTransformation ? \"render/image/authenticated\" : \"object\";\n            const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n            const queryString = transformationQuery ? `?${transformationQuery}` : \"\";\n            try {\n                const _path = this._getFinalPath(path);\n                const res = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/${renderPath}/${_path}${queryString}`, {\n                    headers: this.headers,\n                    noResolveJson: true\n                });\n                const data = yield res.blob();\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Retrieves the details of an existing file.\n     * @param path\n     */ info(path) {\n        return __awaiter(this, void 0, void 0, function*() {\n            const _path = this._getFinalPath(path);\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.get)(this.fetch, `${this.url}/object/info/${_path}`, {\n                    headers: this.headers\n                });\n                return {\n                    data: (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_0__.recursiveToCamel)(data),\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Checks the existence of a file.\n     * @param path\n     */ exists(path) {\n        return __awaiter(this, void 0, void 0, function*() {\n            const _path = this._getFinalPath(path);\n            try {\n                yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.head)(this.fetch, `${this.url}/object/${_path}`, {\n                    headers: this.headers\n                });\n                return {\n                    data: true,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error) && error instanceof _lib_errors__WEBPACK_IMPORTED_MODULE_1__.StorageUnknownError) {\n                    const originalError = error.originalError;\n                    if ([\n                        400,\n                        404\n                    ].includes(originalError === null || originalError === void 0 ? void 0 : originalError.status)) {\n                        return {\n                            data: false,\n                            error\n                        };\n                    }\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * A simple convenience function to get the URL for an asset in a public bucket. If you do not want to use this function, you can construct the public URL by concatenating the bucket URL with the path to the asset.\n     * This function does not verify if the bucket is public. If a public URL is created for a bucket which is not public, you will not be able to download the asset.\n     *\n     * @param path The path and name of the file to generate the public URL for. For example `folder/image.png`.\n     * @param options.download Triggers the file as a download if set to true. Set this parameter as the name of the file if you want to trigger the download with a different filename.\n     * @param options.transform Transform the asset before serving it to the client.\n     */ getPublicUrl(path, options) {\n        const _path = this._getFinalPath(path);\n        const _queryString = [];\n        const downloadQueryParam = (options === null || options === void 0 ? void 0 : options.download) ? `download=${options.download === true ? \"\" : options.download}` : \"\";\n        if (downloadQueryParam !== \"\") {\n            _queryString.push(downloadQueryParam);\n        }\n        const wantsTransformation = typeof (options === null || options === void 0 ? void 0 : options.transform) !== \"undefined\";\n        const renderPath = wantsTransformation ? \"render/image\" : \"object\";\n        const transformationQuery = this.transformOptsToQueryString((options === null || options === void 0 ? void 0 : options.transform) || {});\n        if (transformationQuery !== \"\") {\n            _queryString.push(transformationQuery);\n        }\n        let queryString = _queryString.join(\"&\");\n        if (queryString !== \"\") {\n            queryString = `?${queryString}`;\n        }\n        return {\n            data: {\n                publicUrl: encodeURI(`${this.url}/${renderPath}/public/${_path}${queryString}`)\n            }\n        };\n    }\n    /**\n     * Deletes files within the same bucket\n     *\n     * @param paths An array of files to delete, including the path and file name. For example [`'folder/image.png'`].\n     */ remove(paths) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.remove)(this.fetch, `${this.url}/object/${this.bucketId}`, {\n                    prefixes: paths\n                }, {\n                    headers: this.headers\n                });\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * Get file metadata\n     * @param id the file id to retrieve metadata\n     */ // async getMetadata(\n    //   id: string\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await get(this.fetch, `${this.url}/metadata/${id}`, { headers: this.headers })\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Update file metadata\n     * @param id the file id to update metadata\n     * @param meta the new file metadata\n     */ // async updateMetadata(\n    //   id: string,\n    //   meta: Metadata\n    // ): Promise<\n    //   | {\n    //       data: Metadata\n    //       error: null\n    //     }\n    //   | {\n    //       data: null\n    //       error: StorageError\n    //     }\n    // > {\n    //   try {\n    //     const data = await post(\n    //       this.fetch,\n    //       `${this.url}/metadata/${id}`,\n    //       { ...meta },\n    //       { headers: this.headers }\n    //     )\n    //     return { data, error: null }\n    //   } catch (error) {\n    //     if (isStorageError(error)) {\n    //       return { data: null, error }\n    //     }\n    //     throw error\n    //   }\n    // }\n    /**\n     * Lists all the files within a bucket.\n     * @param path The folder path.\n     */ list(path, options, parameters) {\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const body = Object.assign(Object.assign(Object.assign({}, DEFAULT_SEARCH_OPTIONS), options), {\n                    prefix: path || \"\"\n                });\n                const data = yield (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_2__.post)(this.fetch, `${this.url}/object/list/${this.bucketId}`, body, {\n                    headers: this.headers\n                }, parameters);\n                return {\n                    data,\n                    error: null\n                };\n            } catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_1__.isStorageError)(error)) {\n                    return {\n                        data: null,\n                        error\n                    };\n                }\n                throw error;\n            }\n        });\n    }\n    encodeMetadata(metadata) {\n        return JSON.stringify(metadata);\n    }\n    toBase64(data) {\n        if (typeof Buffer !== \"undefined\") {\n            return Buffer.from(data).toString(\"base64\");\n        }\n        return btoa(data);\n    }\n    _getFinalPath(path) {\n        return `${this.bucketId}/${path}`;\n    }\n    _removeEmptyFolders(path) {\n        return path.replace(/^\\/|\\/$/g, \"\").replace(/\\/+/g, \"/\");\n    }\n    transformOptsToQueryString(transform) {\n        const params = [];\n        if (transform.width) {\n            params.push(`width=${transform.width}`);\n        }\n        if (transform.height) {\n            params.push(`height=${transform.height}`);\n        }\n        if (transform.resize) {\n            params.push(`resize=${transform.resize}`);\n        }\n        if (transform.format) {\n            params.push(`format=${transform.format}`);\n        }\n        if (transform.quality) {\n            params.push(`quality=${transform.quality}`);\n        }\n        return params.join(\"&\");\n    }\n} //# sourceMappingURL=StorageFileApi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js\n");

/***/ })

};
;