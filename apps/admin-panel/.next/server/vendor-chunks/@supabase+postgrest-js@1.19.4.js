"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+postgrest-js@1.19.4";
exports.ids = ["vendor-chunks/@supabase+postgrest-js@1.19.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder){\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = builder.headers;\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = builder.shouldThrowOnError;\n        this.signal = builder.signal;\n        this.isMaybeSingle = builder.isMaybeSingle;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        } else if (typeof fetch === \"undefined\") {\n            this.fetch = node_fetch_1.default;\n        } else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */ throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */ setHeader(name, value) {\n        this.headers = Object.assign({}, this.headers);\n        this.headers[name] = value;\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n        // skip\n        } else if ([\n            \"GET\",\n            \"HEAD\"\n        ].includes(this.method)) {\n            this.headers[\"Accept-Profile\"] = this.schema;\n        } else {\n            this.headers[\"Content-Profile\"] = this.schema;\n        }\n        if (this.method !== \"GET\" && this.method !== \"HEAD\") {\n            this.headers[\"Content-Type\"] = \"application/json\";\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal\n        }).then(async (res)=>{\n            var _a, _b, _c;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== \"HEAD\") {\n                    const body = await res.text();\n                    if (body === \"\") {\n                    // Prefer: return=minimal\n                    } else if (this.headers[\"Accept\"] === \"text/csv\") {\n                        data = body;\n                    } else if (this.headers[\"Accept\"] && this.headers[\"Accept\"].includes(\"application/vnd.pgrst.plan+text\")) {\n                        data = body;\n                    } else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_a = this.headers[\"Prefer\"]) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_b = res.headers.get(\"content-range\")) === null || _b === void 0 ? void 0 : _b.split(\"/\");\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === \"GET\" && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: \"PGRST116\",\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: \"JSON object requested, multiple (or no) rows returned\"\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = \"Not Acceptable\";\n                    } else if (data.length === 1) {\n                        data = data[0];\n                    } else {\n                        data = null;\n                    }\n                }\n            } else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = \"OK\";\n                    }\n                } catch (_d) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === \"\") {\n                        status = 204;\n                        statusText = \"No Content\";\n                    } else {\n                        error = {\n                            message: body\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes(\"0 rows\"))) {\n                    error = null;\n                    status = 200;\n                    statusText = \"OK\";\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError)=>{\n                var _a, _b, _c;\n                return {\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : \"FetchError\"}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : \"\"}`,\n                        hint: \"\",\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : \"\"}`\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: \"\"\n                };\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */ returns() {\n        /* istanbul ignore next */ return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */ overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder; //# sourceMappingURL=PostgrestBuilder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL1Bvc3RncmVzdEJ1aWxkZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixJQUFJQSxrQkFBa0IsSUFBSyxJQUFJLElBQUksQ0FBQ0EsZUFBZSxJQUFLLFNBQVVDLEdBQUc7SUFDakUsT0FBTyxPQUFRQSxJQUFJQyxVQUFVLEdBQUlELE1BQU07UUFBRSxXQUFXQTtJQUFJO0FBQzVEO0FBQ0FFLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdELGFBQWE7QUFDYixNQUFNQyxlQUFlUCxnQkFBZ0JRLG1CQUFPQSxDQUFDLHVJQUFzQjtBQUNuRSxNQUFNQyxtQkFBbUJULGdCQUFnQlEsbUJBQU9BLENBQUMscUpBQWtCO0FBQ25FLE1BQU1FO0lBQ0ZDLFlBQVlDLE9BQU8sQ0FBRTtRQUNqQixJQUFJLENBQUNDLGtCQUFrQixHQUFHO1FBQzFCLElBQUksQ0FBQ0MsTUFBTSxHQUFHRixRQUFRRSxNQUFNO1FBQzVCLElBQUksQ0FBQ0MsR0FBRyxHQUFHSCxRQUFRRyxHQUFHO1FBQ3RCLElBQUksQ0FBQ0MsT0FBTyxHQUFHSixRQUFRSSxPQUFPO1FBQzlCLElBQUksQ0FBQ0MsTUFBTSxHQUFHTCxRQUFRSyxNQUFNO1FBQzVCLElBQUksQ0FBQ0MsSUFBSSxHQUFHTixRQUFRTSxJQUFJO1FBQ3hCLElBQUksQ0FBQ0wsa0JBQWtCLEdBQUdELFFBQVFDLGtCQUFrQjtRQUNwRCxJQUFJLENBQUNNLE1BQU0sR0FBR1AsUUFBUU8sTUFBTTtRQUM1QixJQUFJLENBQUNDLGFBQWEsR0FBR1IsUUFBUVEsYUFBYTtRQUMxQyxJQUFJUixRQUFRUyxLQUFLLEVBQUU7WUFDZixJQUFJLENBQUNBLEtBQUssR0FBR1QsUUFBUVMsS0FBSztRQUM5QixPQUNLLElBQUksT0FBT0EsVUFBVSxhQUFhO1lBQ25DLElBQUksQ0FBQ0EsS0FBSyxHQUFHZCxhQUFhZSxPQUFPO1FBQ3JDLE9BQ0s7WUFDRCxJQUFJLENBQUNELEtBQUssR0FBR0E7UUFDakI7SUFDSjtJQUNBOzs7OztLQUtDLEdBQ0RFLGVBQWU7UUFDWCxJQUFJLENBQUNWLGtCQUFrQixHQUFHO1FBQzFCLE9BQU8sSUFBSTtJQUNmO0lBQ0E7O0tBRUMsR0FDRFcsVUFBVUMsSUFBSSxFQUFFbkIsS0FBSyxFQUFFO1FBQ25CLElBQUksQ0FBQ1UsT0FBTyxHQUFHYixPQUFPdUIsTUFBTSxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUNWLE9BQU87UUFDN0MsSUFBSSxDQUFDQSxPQUFPLENBQUNTLEtBQUssR0FBR25CO1FBQ3JCLE9BQU8sSUFBSTtJQUNmO0lBQ0FxQixLQUFLQyxXQUFXLEVBQUVDLFVBQVUsRUFBRTtRQUMxQiw2REFBNkQ7UUFDN0QsSUFBSSxJQUFJLENBQUNaLE1BQU0sS0FBS2EsV0FBVztRQUMzQixPQUFPO1FBQ1gsT0FDSyxJQUFJO1lBQUM7WUFBTztTQUFPLENBQUNDLFFBQVEsQ0FBQyxJQUFJLENBQUNqQixNQUFNLEdBQUc7WUFDNUMsSUFBSSxDQUFDRSxPQUFPLENBQUMsaUJBQWlCLEdBQUcsSUFBSSxDQUFDQyxNQUFNO1FBQ2hELE9BQ0s7WUFDRCxJQUFJLENBQUNELE9BQU8sQ0FBQyxrQkFBa0IsR0FBRyxJQUFJLENBQUNDLE1BQU07UUFDakQ7UUFDQSxJQUFJLElBQUksQ0FBQ0gsTUFBTSxLQUFLLFNBQVMsSUFBSSxDQUFDQSxNQUFNLEtBQUssUUFBUTtZQUNqRCxJQUFJLENBQUNFLE9BQU8sQ0FBQyxlQUFlLEdBQUc7UUFDbkM7UUFDQSw2REFBNkQ7UUFDN0Qsb0RBQW9EO1FBQ3BELE1BQU1nQixTQUFTLElBQUksQ0FBQ1gsS0FBSztRQUN6QixJQUFJWSxNQUFNRCxPQUFPLElBQUksQ0FBQ2pCLEdBQUcsQ0FBQ21CLFFBQVEsSUFBSTtZQUNsQ3BCLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1lBQ25CRSxTQUFTLElBQUksQ0FBQ0EsT0FBTztZQUNyQkUsTUFBTWlCLEtBQUtDLFNBQVMsQ0FBQyxJQUFJLENBQUNsQixJQUFJO1lBQzlCQyxRQUFRLElBQUksQ0FBQ0EsTUFBTTtRQUN2QixHQUFHUSxJQUFJLENBQUMsT0FBT007WUFDWCxJQUFJSSxJQUFJQyxJQUFJQztZQUNaLElBQUlDLFFBQVE7WUFDWixJQUFJQyxPQUFPO1lBQ1gsSUFBSUMsUUFBUTtZQUNaLElBQUlDLFNBQVNWLElBQUlVLE1BQU07WUFDdkIsSUFBSUMsYUFBYVgsSUFBSVcsVUFBVTtZQUMvQixJQUFJWCxJQUFJWSxFQUFFLEVBQUU7Z0JBQ1IsSUFBSSxJQUFJLENBQUMvQixNQUFNLEtBQUssUUFBUTtvQkFDeEIsTUFBTUksT0FBTyxNQUFNZSxJQUFJYSxJQUFJO29CQUMzQixJQUFJNUIsU0FBUyxJQUFJO29CQUNiLHlCQUF5QjtvQkFDN0IsT0FDSyxJQUFJLElBQUksQ0FBQ0YsT0FBTyxDQUFDLFNBQVMsS0FBSyxZQUFZO3dCQUM1Q3lCLE9BQU92QjtvQkFDWCxPQUNLLElBQUksSUFBSSxDQUFDRixPQUFPLENBQUMsU0FBUyxJQUMzQixJQUFJLENBQUNBLE9BQU8sQ0FBQyxTQUFTLENBQUNlLFFBQVEsQ0FBQyxvQ0FBb0M7d0JBQ3BFVSxPQUFPdkI7b0JBQ1gsT0FDSzt3QkFDRHVCLE9BQU9OLEtBQUtZLEtBQUssQ0FBQzdCO29CQUN0QjtnQkFDSjtnQkFDQSxNQUFNOEIsY0FBYyxDQUFDWCxLQUFLLElBQUksQ0FBQ3JCLE9BQU8sQ0FBQyxTQUFTLE1BQU0sUUFBUXFCLE9BQU8sS0FBSyxJQUFJLEtBQUssSUFBSUEsR0FBR1ksS0FBSyxDQUFDO2dCQUNoRyxNQUFNQyxlQUFlLENBQUNaLEtBQUtMLElBQUlqQixPQUFPLENBQUNtQyxHQUFHLENBQUMsZ0JBQWUsTUFBTyxRQUFRYixPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdjLEtBQUssQ0FBQztnQkFDM0csSUFBSUosZUFBZUUsZ0JBQWdCQSxhQUFhRyxNQUFNLEdBQUcsR0FBRztvQkFDeERYLFFBQVFZLFNBQVNKLFlBQVksQ0FBQyxFQUFFO2dCQUNwQztnQkFDQSxnRkFBZ0Y7Z0JBQ2hGLGtFQUFrRTtnQkFDbEUsSUFBSSxJQUFJLENBQUM5QixhQUFhLElBQUksSUFBSSxDQUFDTixNQUFNLEtBQUssU0FBU3lDLE1BQU1DLE9BQU8sQ0FBQ2YsT0FBTztvQkFDcEUsSUFBSUEsS0FBS1ksTUFBTSxHQUFHLEdBQUc7d0JBQ2pCYixRQUFROzRCQUNKLG1IQUFtSDs0QkFDbkhpQixNQUFNOzRCQUNOQyxTQUFTLENBQUMsZ0JBQWdCLEVBQUVqQixLQUFLWSxNQUFNLENBQUMsdURBQXVELENBQUM7NEJBQ2hHTSxNQUFNOzRCQUNOQyxTQUFTO3dCQUNiO3dCQUNBbkIsT0FBTzt3QkFDUEMsUUFBUTt3QkFDUkMsU0FBUzt3QkFDVEMsYUFBYTtvQkFDakIsT0FDSyxJQUFJSCxLQUFLWSxNQUFNLEtBQUssR0FBRzt3QkFDeEJaLE9BQU9BLElBQUksQ0FBQyxFQUFFO29CQUNsQixPQUNLO3dCQUNEQSxPQUFPO29CQUNYO2dCQUNKO1lBQ0osT0FDSztnQkFDRCxNQUFNdkIsT0FBTyxNQUFNZSxJQUFJYSxJQUFJO2dCQUMzQixJQUFJO29CQUNBTixRQUFRTCxLQUFLWSxLQUFLLENBQUM3QjtvQkFDbkIscUVBQXFFO29CQUNyRSxJQUFJcUMsTUFBTUMsT0FBTyxDQUFDaEIsVUFBVVAsSUFBSVUsTUFBTSxLQUFLLEtBQUs7d0JBQzVDRixPQUFPLEVBQUU7d0JBQ1RELFFBQVE7d0JBQ1JHLFNBQVM7d0JBQ1RDLGFBQWE7b0JBQ2pCO2dCQUNKLEVBQ0EsT0FBT2lCLElBQUk7b0JBQ1AscUVBQXFFO29CQUNyRSxJQUFJNUIsSUFBSVUsTUFBTSxLQUFLLE9BQU96QixTQUFTLElBQUk7d0JBQ25DeUIsU0FBUzt3QkFDVEMsYUFBYTtvQkFDakIsT0FDSzt3QkFDREosUUFBUTs0QkFDSm9CLFNBQVMxQzt3QkFDYjtvQkFDSjtnQkFDSjtnQkFDQSxJQUFJc0IsU0FBUyxJQUFJLENBQUNwQixhQUFhLElBQUssRUFBQ21CLEtBQUtDLFVBQVUsUUFBUUEsVUFBVSxLQUFLLElBQUksS0FBSyxJQUFJQSxNQUFNa0IsT0FBTyxNQUFNLFFBQVFuQixPQUFPLEtBQUssSUFBSSxLQUFLLElBQUlBLEdBQUdSLFFBQVEsQ0FBQyxTQUFRLEdBQUk7b0JBQ2hLUyxRQUFRO29CQUNSRyxTQUFTO29CQUNUQyxhQUFhO2dCQUNqQjtnQkFDQSxJQUFJSixTQUFTLElBQUksQ0FBQzNCLGtCQUFrQixFQUFFO29CQUNsQyxNQUFNLElBQUlKLGlCQUFpQmEsT0FBTyxDQUFDa0I7Z0JBQ3ZDO1lBQ0o7WUFDQSxNQUFNc0Isb0JBQW9CO2dCQUN0QnRCO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztZQUNKO1lBQ0EsT0FBT2tCO1FBQ1g7UUFDQSxJQUFJLENBQUMsSUFBSSxDQUFDakQsa0JBQWtCLEVBQUU7WUFDMUJvQixNQUFNQSxJQUFJOEIsS0FBSyxDQUFDLENBQUNDO2dCQUNiLElBQUkzQixJQUFJQyxJQUFJQztnQkFDWixPQUFRO29CQUNKQyxPQUFPO3dCQUNIb0IsU0FBUyxDQUFDLEVBQUUsQ0FBQ3ZCLEtBQUsyQixlQUFlLFFBQVFBLGVBQWUsS0FBSyxJQUFJLEtBQUssSUFBSUEsV0FBV3ZDLElBQUksTUFBTSxRQUFRWSxPQUFPLEtBQUssSUFBSUEsS0FBSyxhQUFhLEVBQUUsRUFBRTJCLGVBQWUsUUFBUUEsZUFBZSxLQUFLLElBQUksS0FBSyxJQUFJQSxXQUFXSixPQUFPLENBQUMsQ0FBQzt3QkFDek5GLFNBQVMsQ0FBQyxFQUFFLENBQUNwQixLQUFLMEIsZUFBZSxRQUFRQSxlQUFlLEtBQUssSUFBSSxLQUFLLElBQUlBLFdBQVdDLEtBQUssTUFBTSxRQUFRM0IsT0FBTyxLQUFLLElBQUlBLEtBQUssR0FBRyxDQUFDO3dCQUNqSXFCLE1BQU07d0JBQ05GLE1BQU0sQ0FBQyxFQUFFLENBQUNsQixLQUFLeUIsZUFBZSxRQUFRQSxlQUFlLEtBQUssSUFBSSxLQUFLLElBQUlBLFdBQVdQLElBQUksTUFBTSxRQUFRbEIsT0FBTyxLQUFLLElBQUlBLEtBQUssR0FBRyxDQUFDO29CQUNqSTtvQkFDQUUsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsUUFBUTtvQkFDUkMsWUFBWTtnQkFDaEI7WUFDSjtRQUNKO1FBQ0EsT0FBT1gsSUFBSU4sSUFBSSxDQUFDQyxhQUFhQztJQUNqQztJQUNBOzs7OztLQUtDLEdBQ0RxQyxVQUFVO1FBQ04sd0JBQXdCLEdBQ3hCLE9BQU8sSUFBSTtJQUNmO0lBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztLQXFCQyxHQUNEQyxnQkFBZ0I7UUFDWixPQUFPLElBQUk7SUFDZjtBQUNKO0FBQ0E5RCxrQkFBZSxHQUFHSyxrQkFDbEIsNENBQTRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy9Qb3N0Z3Jlc3RCdWlsZGVyLmpzPzBlNWYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG4vLyBAdHMtaWdub3JlXG5jb25zdCBub2RlX2ZldGNoXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIkBzdXBhYmFzZS9ub2RlLWZldGNoXCIpKTtcbmNvbnN0IFBvc3RncmVzdEVycm9yXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcIi4vUG9zdGdyZXN0RXJyb3JcIikpO1xuY2xhc3MgUG9zdGdyZXN0QnVpbGRlciB7XG4gICAgY29uc3RydWN0b3IoYnVpbGRlcikge1xuICAgICAgICB0aGlzLnNob3VsZFRocm93T25FcnJvciA9IGZhbHNlO1xuICAgICAgICB0aGlzLm1ldGhvZCA9IGJ1aWxkZXIubWV0aG9kO1xuICAgICAgICB0aGlzLnVybCA9IGJ1aWxkZXIudXJsO1xuICAgICAgICB0aGlzLmhlYWRlcnMgPSBidWlsZGVyLmhlYWRlcnM7XG4gICAgICAgIHRoaXMuc2NoZW1hID0gYnVpbGRlci5zY2hlbWE7XG4gICAgICAgIHRoaXMuYm9keSA9IGJ1aWxkZXIuYm9keTtcbiAgICAgICAgdGhpcy5zaG91bGRUaHJvd09uRXJyb3IgPSBidWlsZGVyLnNob3VsZFRocm93T25FcnJvcjtcbiAgICAgICAgdGhpcy5zaWduYWwgPSBidWlsZGVyLnNpZ25hbDtcbiAgICAgICAgdGhpcy5pc01heWJlU2luZ2xlID0gYnVpbGRlci5pc01heWJlU2luZ2xlO1xuICAgICAgICBpZiAoYnVpbGRlci5mZXRjaCkge1xuICAgICAgICAgICAgdGhpcy5mZXRjaCA9IGJ1aWxkZXIuZmV0Y2g7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIGZldGNoID09PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgdGhpcy5mZXRjaCA9IG5vZGVfZmV0Y2hfMS5kZWZhdWx0O1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5mZXRjaCA9IGZldGNoO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIElmIHRoZXJlJ3MgYW4gZXJyb3Igd2l0aCB0aGUgcXVlcnksIHRocm93T25FcnJvciB3aWxsIHJlamVjdCB0aGUgcHJvbWlzZSBieVxuICAgICAqIHRocm93aW5nIHRoZSBlcnJvciBpbnN0ZWFkIG9mIHJldHVybmluZyBpdCBhcyBwYXJ0IG9mIGEgc3VjY2Vzc2Z1bCByZXNwb25zZS5cbiAgICAgKlxuICAgICAqIHtAbGluayBodHRwczovL2dpdGh1Yi5jb20vc3VwYWJhc2Uvc3VwYWJhc2UtanMvaXNzdWVzLzkyfVxuICAgICAqL1xuICAgIHRocm93T25FcnJvcigpIHtcbiAgICAgICAgdGhpcy5zaG91bGRUaHJvd09uRXJyb3IgPSB0cnVlO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2V0IGFuIEhUVFAgaGVhZGVyIGZvciB0aGUgcmVxdWVzdC5cbiAgICAgKi9cbiAgICBzZXRIZWFkZXIobmFtZSwgdmFsdWUpIHtcbiAgICAgICAgdGhpcy5oZWFkZXJzID0gT2JqZWN0LmFzc2lnbih7fSwgdGhpcy5oZWFkZXJzKTtcbiAgICAgICAgdGhpcy5oZWFkZXJzW25hbWVdID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH1cbiAgICB0aGVuKG9uZnVsZmlsbGVkLCBvbnJlamVjdGVkKSB7XG4gICAgICAgIC8vIGh0dHBzOi8vcG9zdGdyZXN0Lm9yZy9lbi9zdGFibGUvYXBpLmh0bWwjc3dpdGNoaW5nLXNjaGVtYXNcbiAgICAgICAgaWYgKHRoaXMuc2NoZW1hID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIC8vIHNraXBcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChbJ0dFVCcsICdIRUFEJ10uaW5jbHVkZXModGhpcy5tZXRob2QpKSB7XG4gICAgICAgICAgICB0aGlzLmhlYWRlcnNbJ0FjY2VwdC1Qcm9maWxlJ10gPSB0aGlzLnNjaGVtYTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHRoaXMuaGVhZGVyc1snQ29udGVudC1Qcm9maWxlJ10gPSB0aGlzLnNjaGVtYTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5tZXRob2QgIT09ICdHRVQnICYmIHRoaXMubWV0aG9kICE9PSAnSEVBRCcpIHtcbiAgICAgICAgICAgIHRoaXMuaGVhZGVyc1snQ29udGVudC1UeXBlJ10gPSAnYXBwbGljYXRpb24vanNvbic7XG4gICAgICAgIH1cbiAgICAgICAgLy8gTk9URTogSW52b2tlIHcvbyBgdGhpc2AgdG8gYXZvaWQgaWxsZWdhbCBpbnZvY2F0aW9uIGVycm9yLlxuICAgICAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vc3VwYWJhc2UvcG9zdGdyZXN0LWpzL3B1bGwvMjQ3XG4gICAgICAgIGNvbnN0IF9mZXRjaCA9IHRoaXMuZmV0Y2g7XG4gICAgICAgIGxldCByZXMgPSBfZmV0Y2godGhpcy51cmwudG9TdHJpbmcoKSwge1xuICAgICAgICAgICAgbWV0aG9kOiB0aGlzLm1ldGhvZCxcbiAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMuaGVhZGVycyxcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHRoaXMuYm9keSksXG4gICAgICAgICAgICBzaWduYWw6IHRoaXMuc2lnbmFsLFxuICAgICAgICB9KS50aGVuKGFzeW5jIChyZXMpID0+IHtcbiAgICAgICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICAgICAgbGV0IGVycm9yID0gbnVsbDtcbiAgICAgICAgICAgIGxldCBkYXRhID0gbnVsbDtcbiAgICAgICAgICAgIGxldCBjb3VudCA9IG51bGw7XG4gICAgICAgICAgICBsZXQgc3RhdHVzID0gcmVzLnN0YXR1cztcbiAgICAgICAgICAgIGxldCBzdGF0dXNUZXh0ID0gcmVzLnN0YXR1c1RleHQ7XG4gICAgICAgICAgICBpZiAocmVzLm9rKSB7XG4gICAgICAgICAgICAgICAgaWYgKHRoaXMubWV0aG9kICE9PSAnSEVBRCcpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcy50ZXh0KCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChib2R5ID09PSAnJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gUHJlZmVyOiByZXR1cm49bWluaW1hbFxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHRoaXMuaGVhZGVyc1snQWNjZXB0J10gPT09ICd0ZXh0L2NzdicpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSBib2R5O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKHRoaXMuaGVhZGVyc1snQWNjZXB0J10gJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuaGVhZGVyc1snQWNjZXB0J10uaW5jbHVkZXMoJ2FwcGxpY2F0aW9uL3ZuZC5wZ3JzdC5wbGFuK3RleHQnKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IGJvZHk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gSlNPTi5wYXJzZShib2R5KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBjb3VudEhlYWRlciA9IChfYSA9IHRoaXMuaGVhZGVyc1snUHJlZmVyJ10pID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5tYXRjaCgvY291bnQ9KGV4YWN0fHBsYW5uZWR8ZXN0aW1hdGVkKS8pO1xuICAgICAgICAgICAgICAgIGNvbnN0IGNvbnRlbnRSYW5nZSA9IChfYiA9IHJlcy5oZWFkZXJzLmdldCgnY29udGVudC1yYW5nZScpKSA9PT0gbnVsbCB8fCBfYiA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Iuc3BsaXQoJy8nKTtcbiAgICAgICAgICAgICAgICBpZiAoY291bnRIZWFkZXIgJiYgY29udGVudFJhbmdlICYmIGNvbnRlbnRSYW5nZS5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvdW50ID0gcGFyc2VJbnQoY29udGVudFJhbmdlWzFdKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gVGVtcG9yYXJ5IHBhcnRpYWwgZml4IGZvciBodHRwczovL2dpdGh1Yi5jb20vc3VwYWJhc2UvcG9zdGdyZXN0LWpzL2lzc3Vlcy8zNjFcbiAgICAgICAgICAgICAgICAvLyBJc3N1ZSBwZXJzaXN0cyBlLmcuIGZvciBgLmluc2VydChbLi4uXSkuc2VsZWN0KCkubWF5YmVTaW5nbGUoKWBcbiAgICAgICAgICAgICAgICBpZiAodGhpcy5pc01heWJlU2luZ2xlICYmIHRoaXMubWV0aG9kID09PSAnR0VUJyAmJiBBcnJheS5pc0FycmF5KGRhdGEpKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9Qb3N0Z1JFU1QvcG9zdGdyZXN0L2Jsb2IvYTg2N2Q3OWM0MjQxOWFmMTZjMThjM2ZiMDE5ZWJhOGRmOTkyNjI2Zi9zcmMvUG9zdGdSRVNUL0Vycm9yLmhzI0w1NTNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2RlOiAnUEdSU1QxMTYnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRldGFpbHM6IGBSZXN1bHRzIGNvbnRhaW4gJHtkYXRhLmxlbmd0aH0gcm93cywgYXBwbGljYXRpb24vdm5kLnBncnN0Lm9iamVjdCtqc29uIHJlcXVpcmVzIDEgcm93YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBoaW50OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICdKU09OIG9iamVjdCByZXF1ZXN0ZWQsIG11bHRpcGxlIChvciBubykgcm93cyByZXR1cm5lZCcsXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb3VudCA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXMgPSA0MDY7XG4gICAgICAgICAgICAgICAgICAgICAgICBzdGF0dXNUZXh0ID0gJ05vdCBBY2NlcHRhYmxlJztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChkYXRhLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YSA9IGRhdGFbMF07XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXMudGV4dCgpO1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGVycm9yID0gSlNPTi5wYXJzZShib2R5KTtcbiAgICAgICAgICAgICAgICAgICAgLy8gV29ya2Fyb3VuZCBmb3IgaHR0cHM6Ly9naXRodWIuY29tL3N1cGFiYXNlL3Bvc3RncmVzdC1qcy9pc3N1ZXMvMjk1XG4gICAgICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGVycm9yKSAmJiByZXMuc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yID0gbnVsbDtcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1cyA9IDIwMDtcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1c1RleHQgPSAnT0snO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChfZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBXb3JrYXJvdW5kIGZvciBodHRwczovL2dpdGh1Yi5jb20vc3VwYWJhc2UvcG9zdGdyZXN0LWpzL2lzc3Vlcy8yOTVcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDQwNCAmJiBib2R5ID09PSAnJykge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzID0gMjA0O1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzVGV4dCA9ICdObyBDb250ZW50JztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGJvZHksXG4gICAgICAgICAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChlcnJvciAmJiB0aGlzLmlzTWF5YmVTaW5nbGUgJiYgKChfYyA9IGVycm9yID09PSBudWxsIHx8IGVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBlcnJvci5kZXRhaWxzKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MuaW5jbHVkZXMoJzAgcm93cycpKSkge1xuICAgICAgICAgICAgICAgICAgICBlcnJvciA9IG51bGw7XG4gICAgICAgICAgICAgICAgICAgIHN0YXR1cyA9IDIwMDtcbiAgICAgICAgICAgICAgICAgICAgc3RhdHVzVGV4dCA9ICdPSyc7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChlcnJvciAmJiB0aGlzLnNob3VsZFRocm93T25FcnJvcikge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUG9zdGdyZXN0RXJyb3JfMS5kZWZhdWx0KGVycm9yKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBwb3N0Z3Jlc3RSZXNwb25zZSA9IHtcbiAgICAgICAgICAgICAgICBlcnJvcixcbiAgICAgICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgICAgIGNvdW50LFxuICAgICAgICAgICAgICAgIHN0YXR1cyxcbiAgICAgICAgICAgICAgICBzdGF0dXNUZXh0LFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIHJldHVybiBwb3N0Z3Jlc3RSZXNwb25zZTtcbiAgICAgICAgfSk7XG4gICAgICAgIGlmICghdGhpcy5zaG91bGRUaHJvd09uRXJyb3IpIHtcbiAgICAgICAgICAgIHJlcyA9IHJlcy5jYXRjaCgoZmV0Y2hFcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBfYSwgX2IsIF9jO1xuICAgICAgICAgICAgICAgIHJldHVybiAoe1xuICAgICAgICAgICAgICAgICAgICBlcnJvcjoge1xuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7KF9hID0gZmV0Y2hFcnJvciA9PT0gbnVsbCB8fCBmZXRjaEVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmZXRjaEVycm9yLm5hbWUpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6ICdGZXRjaEVycm9yJ306ICR7ZmV0Y2hFcnJvciA9PT0gbnVsbCB8fCBmZXRjaEVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmZXRjaEVycm9yLm1lc3NhZ2V9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRldGFpbHM6IGAkeyhfYiA9IGZldGNoRXJyb3IgPT09IG51bGwgfHwgZmV0Y2hFcnJvciA9PT0gdm9pZCAwID8gdm9pZCAwIDogZmV0Y2hFcnJvci5zdGFjaykgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogJyd9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGhpbnQ6ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29kZTogYCR7KF9jID0gZmV0Y2hFcnJvciA9PT0gbnVsbCB8fCBmZXRjaEVycm9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmZXRjaEVycm9yLmNvZGUpICE9PSBudWxsICYmIF9jICE9PSB2b2lkIDAgPyBfYyA6ICcnfWAsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIGRhdGE6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgIGNvdW50OiBudWxsLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IDAsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6ICcnLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlcy50aGVuKG9uZnVsZmlsbGVkLCBvbnJlamVjdGVkKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogT3ZlcnJpZGUgdGhlIHR5cGUgb2YgdGhlIHJldHVybmVkIGBkYXRhYC5cbiAgICAgKlxuICAgICAqIEB0eXBlUGFyYW0gTmV3UmVzdWx0IC0gVGhlIG5ldyByZXN1bHQgdHlwZSB0byBvdmVycmlkZSB3aXRoXG4gICAgICogQGRlcHJlY2F0ZWQgVXNlIG92ZXJyaWRlVHlwZXM8eW91clR5cGUsIHsgbWVyZ2U6IGZhbHNlIH0+KCkgbWV0aG9kIGF0IHRoZSBlbmQgb2YgeW91ciBjYWxsIGNoYWluIGluc3RlYWRcbiAgICAgKi9cbiAgICByZXR1cm5zKCkge1xuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAqL1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgLyoqXG4gICAgICogT3ZlcnJpZGUgdGhlIHR5cGUgb2YgdGhlIHJldHVybmVkIGBkYXRhYCBmaWVsZCBpbiB0aGUgcmVzcG9uc2UuXG4gICAgICpcbiAgICAgKiBAdHlwZVBhcmFtIE5ld1Jlc3VsdCAtIFRoZSBuZXcgdHlwZSB0byBjYXN0IHRoZSByZXNwb25zZSBkYXRhIHRvXG4gICAgICogQHR5cGVQYXJhbSBPcHRpb25zIC0gT3B0aW9uYWwgdHlwZSBjb25maWd1cmF0aW9uIChkZWZhdWx0cyB0byB7IG1lcmdlOiB0cnVlIH0pXG4gICAgICogQHR5cGVQYXJhbSBPcHRpb25zLm1lcmdlIC0gV2hlbiB0cnVlLCBtZXJnZXMgdGhlIG5ldyB0eXBlIHdpdGggZXhpc3RpbmcgcmV0dXJuIHR5cGUuIFdoZW4gZmFsc2UsIHJlcGxhY2VzIHRoZSBleGlzdGluZyB0eXBlcyBlbnRpcmVseSAoZGVmYXVsdHMgdG8gdHJ1ZSlcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHR5cGVzY3JpcHRcbiAgICAgKiAvLyBNZXJnZSB3aXRoIGV4aXN0aW5nIHR5cGVzIChkZWZhdWx0IGJlaGF2aW9yKVxuICAgICAqIGNvbnN0IHF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgKiAgIC5mcm9tKCd1c2VycycpXG4gICAgICogICAuc2VsZWN0KClcbiAgICAgKiAgIC5vdmVycmlkZVR5cGVzPHsgY3VzdG9tX2ZpZWxkOiBzdHJpbmcgfT4oKVxuICAgICAqXG4gICAgICogLy8gUmVwbGFjZSBleGlzdGluZyB0eXBlcyBjb21wbGV0ZWx5XG4gICAgICogY29uc3QgcmVwbGFjZVF1ZXJ5ID0gc3VwYWJhc2VcbiAgICAgKiAgIC5mcm9tKCd1c2VycycpXG4gICAgICogICAuc2VsZWN0KClcbiAgICAgKiAgIC5vdmVycmlkZVR5cGVzPHsgaWQ6IG51bWJlcjsgbmFtZTogc3RyaW5nIH0sIHsgbWVyZ2U6IGZhbHNlIH0+KClcbiAgICAgKiBgYGBcbiAgICAgKiBAcmV0dXJucyBBIFBvc3RncmVzdEJ1aWxkZXIgaW5zdGFuY2Ugd2l0aCB0aGUgbmV3IHR5cGVcbiAgICAgKi9cbiAgICBvdmVycmlkZVR5cGVzKCkge1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG59XG5leHBvcnRzLmRlZmF1bHQgPSBQb3N0Z3Jlc3RCdWlsZGVyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9UG9zdGdyZXN0QnVpbGRlci5qcy5tYXAiXSwibmFtZXMiOlsiX19pbXBvcnREZWZhdWx0IiwibW9kIiwiX19lc01vZHVsZSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwibm9kZV9mZXRjaF8xIiwicmVxdWlyZSIsIlBvc3RncmVzdEVycm9yXzEiLCJQb3N0Z3Jlc3RCdWlsZGVyIiwiY29uc3RydWN0b3IiLCJidWlsZGVyIiwic2hvdWxkVGhyb3dPbkVycm9yIiwibWV0aG9kIiwidXJsIiwiaGVhZGVycyIsInNjaGVtYSIsImJvZHkiLCJzaWduYWwiLCJpc01heWJlU2luZ2xlIiwiZmV0Y2giLCJkZWZhdWx0IiwidGhyb3dPbkVycm9yIiwic2V0SGVhZGVyIiwibmFtZSIsImFzc2lnbiIsInRoZW4iLCJvbmZ1bGZpbGxlZCIsIm9ucmVqZWN0ZWQiLCJ1bmRlZmluZWQiLCJpbmNsdWRlcyIsIl9mZXRjaCIsInJlcyIsInRvU3RyaW5nIiwiSlNPTiIsInN0cmluZ2lmeSIsIl9hIiwiX2IiLCJfYyIsImVycm9yIiwiZGF0YSIsImNvdW50Iiwic3RhdHVzIiwic3RhdHVzVGV4dCIsIm9rIiwidGV4dCIsInBhcnNlIiwiY291bnRIZWFkZXIiLCJtYXRjaCIsImNvbnRlbnRSYW5nZSIsImdldCIsInNwbGl0IiwibGVuZ3RoIiwicGFyc2VJbnQiLCJBcnJheSIsImlzQXJyYXkiLCJjb2RlIiwiZGV0YWlscyIsImhpbnQiLCJtZXNzYWdlIiwiX2QiLCJwb3N0Z3Jlc3RSZXNwb25zZSIsImNhdGNoIiwiZmV0Y2hFcnJvciIsInN0YWNrIiwicmV0dXJucyIsIm92ZXJyaWRlVHlwZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */ class PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */ constructor(url, { headers = {}, schema, fetch } = {}){\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */ from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */ schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ rpc(fn, args = {}, { head = false, get = false, count } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? \"HEAD\" : \"GET\";\n            Object.entries(args)// params with undefined value needs to be filtered out, otherwise it'll\n            // show up as `?param=undefined`\n            .filter(([_, value])=>value !== undefined)// array values need special syntax\n            .map(([name, value])=>[\n                    name,\n                    Array.isArray(value) ? `{${value.join(\",\")}}` : `${value}`\n                ]).forEach(([name, value])=>{\n                url.searchParams.append(name, value);\n            });\n        } else {\n            method = \"POST\";\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers[\"Prefer\"] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient; //# sourceMappingURL=PostgrestClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */ class PostgrestError extends Error {\n    constructor(context){\n        super(context.message);\n        this.name = \"PostgrestError\";\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError; //# sourceMappingURL=PostgrestError.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL1Bvc3RncmVzdEVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLDhDQUE2QztJQUFFRyxPQUFPO0FBQUssQ0FBQyxFQUFDO0FBQzdEOzs7O0NBSUMsR0FDRCxNQUFNQyx1QkFBdUJDO0lBQ3pCQyxZQUFZQyxPQUFPLENBQUU7UUFDakIsS0FBSyxDQUFDQSxRQUFRQyxPQUFPO1FBQ3JCLElBQUksQ0FBQ0MsSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDQyxPQUFPLEdBQUdILFFBQVFHLE9BQU87UUFDOUIsSUFBSSxDQUFDQyxJQUFJLEdBQUdKLFFBQVFJLElBQUk7UUFDeEIsSUFBSSxDQUFDQyxJQUFJLEdBQUdMLFFBQVFLLElBQUk7SUFDNUI7QUFDSjtBQUNBVixrQkFBZSxHQUFHRSxnQkFDbEIsMENBQTBDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy9Qb3N0Z3Jlc3RFcnJvci5qcz8wODQyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBFcnJvciBmb3JtYXRcbiAqXG4gKiB7QGxpbmsgaHR0cHM6Ly9wb3N0Z3Jlc3Qub3JnL2VuL3N0YWJsZS9hcGkuaHRtbD9oaWdobGlnaHQ9b3B0aW9ucyNlcnJvcnMtYW5kLWh0dHAtc3RhdHVzLWNvZGVzfVxuICovXG5jbGFzcyBQb3N0Z3Jlc3RFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250ZXh0KSB7XG4gICAgICAgIHN1cGVyKGNvbnRleHQubWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdQb3N0Z3Jlc3RFcnJvcic7XG4gICAgICAgIHRoaXMuZGV0YWlscyA9IGNvbnRleHQuZGV0YWlscztcbiAgICAgICAgdGhpcy5oaW50ID0gY29udGV4dC5oaW50O1xuICAgICAgICB0aGlzLmNvZGUgPSBjb250ZXh0LmNvZGU7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gUG9zdGdyZXN0RXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Qb3N0Z3Jlc3RFcnJvci5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJQb3N0Z3Jlc3RFcnJvciIsIkVycm9yIiwiY29uc3RydWN0b3IiLCJjb250ZXh0IiwibWVzc2FnZSIsIm5hbWUiLCJkZXRhaWxzIiwiaGludCIsImNvZGUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */ like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */ likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(\",\")}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */ likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(\",\")}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */ ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */ ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(\",\")}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */ ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(\",\")}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */ is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */ in(column, values) {\n        const cleanedValues = Array.from(new Set(values)).map((s)=>{\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === \"string\" && new RegExp(\"[,()]\").test(s)) return `\"${s}\"`;\n            else return `${s}`;\n        }).join(\",\");\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */ contains(column, value) {\n        if (typeof value === \"string\") {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        } else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(\",\")}}`);\n        } else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */ containedBy(column, value) {\n        if (typeof value === \"string\") {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        } else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(\",\")}}`);\n        } else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */ rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */ rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */ rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */ rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */ rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */ overlaps(column, value) {\n        if (typeof value === \"string\") {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        } else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(\",\")}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */ textSearch(column, query, { config, type } = {}) {\n        let typePart = \"\";\n        if (type === \"plain\") {\n            typePart = \"pl\";\n        } else if (type === \"phrase\") {\n            typePart = \"ph\";\n        } else if (type === \"websearch\") {\n            typePart = \"w\";\n        }\n        const configPart = config === undefined ? \"\" : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */ match(query) {\n        Object.entries(query).forEach(([column, value])=>{\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */ not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */ or(filters, { foreignTable, referencedTable = foreignTable } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : \"or\";\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */ filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder; //# sourceMappingURL=PostgrestFilterBuilder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch }){\n        this.url = url;\n        this.headers = headers;\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ select(columns, { head = false, count } = {}) {\n        const method = head ? \"HEAD\" : \"GET\";\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : \"*\").split(\"\").map((c)=>{\n            if (/\\s/.test(c) && !quoted) {\n                return \"\";\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        }).join(\"\");\n        this.url.searchParams.set(\"select\", cleanedColumns);\n        if (count) {\n            this.headers[\"Prefer\"] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */ insert(values, { count, defaultToNull = true } = {}) {\n        const method = \"POST\";\n        const prefersHeaders = [];\n        if (this.headers[\"Prefer\"]) {\n            prefersHeaders.push(this.headers[\"Prefer\"]);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push(\"missing=default\");\n        }\n        this.headers[\"Prefer\"] = prefersHeaders.join(\",\");\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x)=>acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [\n                    ...new Set(columns)\n                ].map((column)=>`\"${column}\"`);\n                this.url.searchParams.set(\"columns\", uniqueColumns.join(\",\"));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */ upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true } = {}) {\n        const method = \"POST\";\n        const prefersHeaders = [\n            `resolution=${ignoreDuplicates ? \"ignore\" : \"merge\"}-duplicates`\n        ];\n        if (onConflict !== undefined) this.url.searchParams.set(\"on_conflict\", onConflict);\n        if (this.headers[\"Prefer\"]) {\n            prefersHeaders.push(this.headers[\"Prefer\"]);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push(\"missing=default\");\n        }\n        this.headers[\"Prefer\"] = prefersHeaders.join(\",\");\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x)=>acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [\n                    ...new Set(columns)\n                ].map((column)=>`\"${column}\"`);\n                this.url.searchParams.set(\"columns\", uniqueColumns.join(\",\"));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ update(values, { count } = {}) {\n        const method = \"PATCH\";\n        const prefersHeaders = [];\n        if (this.headers[\"Prefer\"]) {\n            prefersHeaders.push(this.headers[\"Prefer\"]);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        this.headers[\"Prefer\"] = prefersHeaders.join(\",\");\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */ delete({ count } = {}) {\n        const method = \"DELETE\";\n        const prefersHeaders = [];\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (this.headers[\"Prefer\"]) {\n            prefersHeaders.unshift(this.headers[\"Prefer\"]);\n        }\n        this.headers[\"Prefer\"] = prefersHeaders.join(\",\");\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder; //# sourceMappingURL=PostgrestQueryBuilder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!****************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */ select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : \"*\").split(\"\").map((c)=>{\n            if (/\\s/.test(c) && !quoted) {\n                return \"\";\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        }).join(\"\");\n        this.url.searchParams.set(\"select\", cleanedColumns);\n        if (this.headers[\"Prefer\"]) {\n            this.headers[\"Prefer\"] += \",\";\n        }\n        this.headers[\"Prefer\"] += \"return=representation\";\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */ order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : \"order\";\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : \"\"}${column}.${ascending ? \"asc\" : \"desc\"}${nullsFirst === undefined ? \"\" : nullsFirst ? \".nullsfirst\" : \".nullslast\"}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */ limit(count, { foreignTable, referencedTable = foreignTable } = {}) {\n        const key = typeof referencedTable === \"undefined\" ? \"limit\" : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */ range(from, to, { foreignTable, referencedTable = foreignTable } = {}) {\n        const keyOffset = typeof referencedTable === \"undefined\" ? \"offset\" : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === \"undefined\" ? \"limit\" : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */ abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */ single() {\n        this.headers[\"Accept\"] = \"application/vnd.pgrst.object+json\";\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */ maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === \"GET\") {\n            this.headers[\"Accept\"] = \"application/json\";\n        } else {\n            this.headers[\"Accept\"] = \"application/vnd.pgrst.object+json\";\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */ csv() {\n        this.headers[\"Accept\"] = \"text/csv\";\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */ geojson() {\n        this.headers[\"Accept\"] = \"application/geo+json\";\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */ explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = \"text\" } = {}) {\n        var _a;\n        const options = [\n            analyze ? \"analyze\" : null,\n            verbose ? \"verbose\" : null,\n            settings ? \"settings\" : null,\n            buffers ? \"buffers\" : null,\n            wal ? \"wal\" : null\n        ].filter(Boolean).join(\"|\");\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers[\"Accept\"]) !== null && _a !== void 0 ? _a : \"application/json\";\n        this.headers[\"Accept\"] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n        if (format === \"json\") return this;\n        else return this;\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */ rollback() {\n        var _a;\n        if (((_a = this.headers[\"Prefer\"]) !== null && _a !== void 0 ? _a : \"\").trim().length > 0) {\n            this.headers[\"Prefer\"] += \",tx=rollback\";\n        } else {\n            this.headers[\"Prefer\"] = \"tx=rollback\";\n        }\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */ returns() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder; //# sourceMappingURL=PostgrestTransformBuilder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\");\nexports.DEFAULT_HEADERS = {\n    \"X-Client-Info\": `postgrest-js/${version_1.version}`\n}; //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsdUJBQXVCLEdBQUcsS0FBSztBQUMvQixNQUFNRyxZQUFZQyxtQkFBT0EsQ0FBQyx1SUFBVztBQUNyQ0osdUJBQXVCLEdBQUc7SUFBRSxpQkFBaUIsQ0FBQyxhQUFhLEVBQUVHLFVBQVVFLE9BQU8sQ0FBQyxDQUFDO0FBQUMsR0FDakYscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Bvc3RncmVzdC1qcy9kaXN0L2Nqcy9jb25zdGFudHMuanM/ZmJiYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuREVGQVVMVF9IRUFERVJTID0gdm9pZCAwO1xuY29uc3QgdmVyc2lvbl8xID0gcmVxdWlyZShcIi4vdmVyc2lvblwiKTtcbmV4cG9ydHMuREVGQVVMVF9IRUFERVJTID0geyAnWC1DbGllbnQtSW5mbyc6IGBwb3N0Z3Jlc3QtanMvJHt2ZXJzaW9uXzEudmVyc2lvbn1gIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiREVGQVVMVF9IRUFERVJTIiwidmVyc2lvbl8xIiwicmVxdWlyZSIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = this && this.__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.version = void 0;\nexports.version = \"0.0.0-automated\"; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYkEsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELGVBQWUsR0FBRyxLQUFLO0FBQ3ZCQSxlQUFlLEdBQUcsbUJBQ2xCLG1DQUFtQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9wb3N0Z3Jlc3QtanMvZGlzdC9janMvdmVyc2lvbi5qcz85ZDA5Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy52ZXJzaW9uID0gdm9pZCAwO1xuZXhwb3J0cy52ZXJzaW9uID0gJzAuMC4wLWF1dG9tYXRlZCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst { PostgrestClient, PostgrestQueryBuilder, PostgrestFilterBuilder, PostgrestTransformBuilder, PostgrestBuilder, PostgrestError } = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__;\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    PostgrestClient,\n    PostgrestQueryBuilder,\n    PostgrestFilterBuilder,\n    PostgrestTransformBuilder,\n    PostgrestBuilder,\n    PostgrestError\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvZXNtL3dyYXBwZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1DO0FBQ25DLE1BQU0sRUFDSkMsZUFBZSxFQUNmQyxxQkFBcUIsRUFDckJDLHNCQUFzQixFQUN0QkMseUJBQXlCLEVBQ3pCQyxnQkFBZ0IsRUFDaEJDLGNBQWMsRUFDZixHQUFHTiwwQ0FBS0E7QUFTUjtBQUVELGdDQUFnQztBQUNoQyxpRUFBZTtJQUNiQztJQUNBQztJQUNBQztJQUNBQztJQUNBQztJQUNBQztBQUNGLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvZXNtL3dyYXBwZXIubWpzPzgyNDkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGluZGV4IGZyb20gJy4uL2Nqcy9pbmRleC5qcydcbmNvbnN0IHtcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdEZpbHRlckJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufSA9IGluZGV4XG5cbmV4cG9ydCB7XG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdENsaWVudCxcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0UXVlcnlCdWlsZGVyLFxuICBQb3N0Z3Jlc3RUcmFuc2Zvcm1CdWlsZGVyLFxuICBQb3N0Z3Jlc3RFcnJvcixcbn1cblxuLy8gY29tcGF0aWJpbGl0eSB3aXRoIENKUyBvdXRwdXRcbmV4cG9ydCBkZWZhdWx0IHtcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdEZpbHRlckJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufVxuIl0sIm5hbWVzIjpbImluZGV4IiwiUG9zdGdyZXN0Q2xpZW50IiwiUG9zdGdyZXN0UXVlcnlCdWlsZGVyIiwiUG9zdGdyZXN0RmlsdGVyQnVpbGRlciIsIlBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIiLCJQb3N0Z3Jlc3RCdWlsZGVyIiwiUG9zdGdyZXN0RXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ })

};
;