"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!***************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \***************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ })

};
;