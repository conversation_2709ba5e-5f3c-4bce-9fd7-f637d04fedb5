"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1";
exports.ids = ["vendor-chunks/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./UI */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/UI.js\");\n/* harmony import */ var _sidecar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sidecar */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/sidecar.js\");\n\n\n\n\nvar ReactRemoveScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function(props, ref) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll, (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({}, props, {\n        ref: ref,\n        sideCar: _sidecar__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    }));\n});\nReactRemoveScroll.classNames = _UI__WEBPACK_IMPORTED_MODULE_2__.RemoveScroll.classNames;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactRemoveScroll);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L0NvbWJpbmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFpQztBQUNGO0FBQ0s7QUFDSjtBQUNoQyxJQUFJSSxrQ0FBb0JILDZDQUFnQixDQUFDLFNBQVVLLEtBQUssRUFBRUMsR0FBRztJQUFJLHFCQUFRTixnREFBbUIsQ0FBQ0MsNkNBQVlBLEVBQUVGLCtDQUFRQSxDQUFDLENBQUMsR0FBR00sT0FBTztRQUFFQyxLQUFLQTtRQUFLRSxTQUFTTixnREFBT0E7SUFBQztBQUFNO0FBQ2xLQyxrQkFBa0JNLFVBQVUsR0FBR1IsNkNBQVlBLENBQUNRLFVBQVU7QUFDdEQsaUVBQWVOLGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtcmVtb3ZlLXNjcm9sbEAyLjcuMV9AdHlwZXMrcmVhY3RAMTguMy4yM19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL3JlYWN0LXJlbW92ZS1zY3JvbGwvZGlzdC9lczIwMTUvQ29tYmluYXRpb24uanM/YTgxMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBfX2Fzc2lnbiB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUmVtb3ZlU2Nyb2xsIH0gZnJvbSAnLi9VSSc7XG5pbXBvcnQgU2lkZUNhciBmcm9tICcuL3NpZGVjYXInO1xudmFyIFJlYWN0UmVtb3ZlU2Nyb2xsID0gUmVhY3QuZm9yd2FyZFJlZihmdW5jdGlvbiAocHJvcHMsIHJlZikgeyByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoUmVtb3ZlU2Nyb2xsLCBfX2Fzc2lnbih7fSwgcHJvcHMsIHsgcmVmOiByZWYsIHNpZGVDYXI6IFNpZGVDYXIgfSkpKTsgfSk7XG5SZWFjdFJlbW92ZVNjcm9sbC5jbGFzc05hbWVzID0gUmVtb3ZlU2Nyb2xsLmNsYXNzTmFtZXM7XG5leHBvcnQgZGVmYXVsdCBSZWFjdFJlbW92ZVNjcm9sbDtcbiJdLCJuYW1lcyI6WyJfX2Fzc2lnbiIsIlJlYWN0IiwiUmVtb3ZlU2Nyb2xsIiwiU2lkZUNhciIsIlJlYWN0UmVtb3ZlU2Nyb2xsIiwiZm9yd2FyZFJlZiIsInByb3BzIiwicmVmIiwiY3JlYXRlRWxlbWVudCIsInNpZGVDYXIiLCJjbGFzc05hbWVzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/SideEffect.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/SideEffect.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScrollSideCar: () => (/* binding */ RemoveScrollSideCar),\n/* harmony export */   getDeltaXY: () => (/* binding */ getDeltaXY),\n/* harmony export */   getTouchXY: () => (/* binding */ getTouchXY)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(ssr)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll-bar/dist/es2015/index.js\");\n/* harmony import */ var react_style_singleton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-style-singleton */ \"(ssr)/../../node_modules/.pnpm/react-style-singleton@2.2.3_@types+react@18.3.23_react@18.3.1/node_modules/react-style-singleton/dist/es2015/index.js\");\n/* harmony import */ var _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./aggresiveCapture */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\");\n/* harmony import */ var _handleScroll__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./handleScroll */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/handleScroll.js\");\n\n\n\n\n\n\nvar getTouchXY = function(event) {\n    return \"changedTouches\" in event ? [\n        event.changedTouches[0].clientX,\n        event.changedTouches[0].clientY\n    ] : [\n        0,\n        0\n    ];\n};\nvar getDeltaXY = function(event) {\n    return [\n        event.deltaX,\n        event.deltaY\n    ];\n};\nvar extractRef = function(ref) {\n    return ref && \"current\" in ref ? ref.current : ref;\n};\nvar deltaCompare = function(x, y) {\n    return x[0] === y[0] && x[1] === y[1];\n};\nvar generateStyle = function(id) {\n    return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\");\n};\nvar idCounter = 0;\nvar lockStack = [];\nfunction RemoveScrollSideCar(props) {\n    var shouldPreventQueue = react__WEBPACK_IMPORTED_MODULE_0__.useRef([]);\n    var touchStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef([\n        0,\n        0\n    ]);\n    var activeAxis = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n    var id = react__WEBPACK_IMPORTED_MODULE_0__.useState(idCounter++)[0];\n    var Style = react__WEBPACK_IMPORTED_MODULE_0__.useState(react_style_singleton__WEBPACK_IMPORTED_MODULE_2__.styleSingleton)[0];\n    var lastProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function() {\n        lastProps.current = props;\n    }, [\n        props\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function() {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__spreadArray)([\n                props.lockRef.current\n            ], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function(el) {\n                return el.classList.add(\"allow-interactivity-\".concat(id));\n            });\n            return function() {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function(el) {\n                    return el.classList.remove(\"allow-interactivity-\".concat(id));\n                });\n            };\n        }\n        return;\n    }, [\n        props.inert,\n        props.lockRef.current,\n        props.shards\n    ]);\n    var shouldCancelEvent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(event, parent) {\n        if (\"touches\" in event && event.touches.length === 2 || event.type === \"wheel\" && event.ctrlKey) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = \"deltaX\" in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = \"deltaY\" in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? \"h\" : \"v\";\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if (\"touches\" in event && moveDirection === \"h\" && target.type === \"range\") {\n            return false;\n        }\n        var canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        } else {\n            currentAxis = moveDirection === \"v\" ? \"h\" : \"v\";\n            canBeScrolledInMainDirection = (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.locationCouldBeScrolled)(moveDirection, target);\n        // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && \"changedTouches\" in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return (0,_handleScroll__WEBPACK_IMPORTED_MODULE_4__.handleScroll)(cancelingAxis, parent, event, cancelingAxis === \"h\" ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = \"deltaY\" in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function(e) {\n            return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta);\n        })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || []).map(extractRef).filter(Boolean).filter(function(node) {\n                return node.contains(event.target);\n            });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(name, delta, target, should) {\n        var event = {\n            name: name,\n            delta: delta,\n            target: target,\n            should: should,\n            shadowParent: getOutermostShadowParent(target)\n        };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function() {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function(e) {\n                return e !== event;\n            });\n        }, 1);\n    }, []);\n    var scrollTouchStart = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function(event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(function() {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove\n        });\n        document.addEventListener(\"wheel\", shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        document.addEventListener(\"touchmove\", shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        document.addEventListener(\"touchstart\", scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        return function() {\n            lockStack = lockStack.filter(function(inst) {\n                return inst !== Style;\n            });\n            document.removeEventListener(\"wheel\", shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n            document.removeEventListener(\"touchmove\", shouldPrevent, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n            document.removeEventListener(\"touchstart\", scrollTouchStart, _aggresiveCapture__WEBPACK_IMPORTED_MODULE_5__.nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, inert ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Style, {\n        styles: generateStyle(id)\n    }) : null, removeScrollBar ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react_remove_scroll_bar__WEBPACK_IMPORTED_MODULE_1__.RemoveScrollBar, {\n        noRelative: props.noRelative,\n        gapMode: props.gapMode\n    }) : null);\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while(node !== null){\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L1NpZGVFZmZlY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFzQztBQUNQO0FBQzJCO0FBQ0g7QUFDUDtBQUN1QjtBQUNoRSxJQUFJTyxhQUFhLFNBQVVDLEtBQUs7SUFDbkMsT0FBTyxvQkFBb0JBLFFBQVE7UUFBQ0EsTUFBTUMsY0FBYyxDQUFDLEVBQUUsQ0FBQ0MsT0FBTztRQUFFRixNQUFNQyxjQUFjLENBQUMsRUFBRSxDQUFDRSxPQUFPO0tBQUMsR0FBRztRQUFDO1FBQUc7S0FBRTtBQUNsSCxFQUFFO0FBQ0ssSUFBSUMsYUFBYSxTQUFVSixLQUFLO0lBQUksT0FBTztRQUFDQSxNQUFNSyxNQUFNO1FBQUVMLE1BQU1NLE1BQU07S0FBQztBQUFFLEVBQUU7QUFDbEYsSUFBSUMsYUFBYSxTQUFVQyxHQUFHO0lBQzFCLE9BQU9BLE9BQU8sYUFBYUEsTUFBTUEsSUFBSUMsT0FBTyxHQUFHRDtBQUNuRDtBQUNBLElBQUlFLGVBQWUsU0FBVUMsQ0FBQyxFQUFFQyxDQUFDO0lBQUksT0FBT0QsQ0FBQyxDQUFDLEVBQUUsS0FBS0MsQ0FBQyxDQUFDLEVBQUUsSUFBSUQsQ0FBQyxDQUFDLEVBQUUsS0FBS0MsQ0FBQyxDQUFDLEVBQUU7QUFBRTtBQUM1RSxJQUFJQyxnQkFBZ0IsU0FBVUMsRUFBRTtJQUFJLE9BQU8sNEJBQTRCQyxNQUFNLENBQUNELElBQUkscURBQXFEQyxNQUFNLENBQUNELElBQUk7QUFBOEI7QUFDaEwsSUFBSUUsWUFBWTtBQUNoQixJQUFJQyxZQUFZLEVBQUU7QUFDWCxTQUFTQyxvQkFBb0JDLEtBQUs7SUFDckMsSUFBSUMscUJBQXFCM0IseUNBQVksQ0FBQyxFQUFFO0lBQ3hDLElBQUk2QixnQkFBZ0I3Qix5Q0FBWSxDQUFDO1FBQUM7UUFBRztLQUFFO0lBQ3ZDLElBQUk4QixhQUFhOUIseUNBQVk7SUFDN0IsSUFBSXFCLEtBQUtyQiwyQ0FBYyxDQUFDdUIsWUFBWSxDQUFDLEVBQUU7SUFDdkMsSUFBSVMsUUFBUWhDLDJDQUFjLENBQUNFLGlFQUFjQSxDQUFDLENBQUMsRUFBRTtJQUM3QyxJQUFJK0IsWUFBWWpDLHlDQUFZLENBQUMwQjtJQUM3QjFCLDRDQUFlLENBQUM7UUFDWmlDLFVBQVVqQixPQUFPLEdBQUdVO0lBQ3hCLEdBQUc7UUFBQ0E7S0FBTTtJQUNWMUIsNENBQWUsQ0FBQztRQUNaLElBQUkwQixNQUFNUyxLQUFLLEVBQUU7WUFDYkMsU0FBU0MsSUFBSSxDQUFDQyxTQUFTLENBQUNDLEdBQUcsQ0FBQyx1QkFBdUJqQixNQUFNLENBQUNEO1lBQzFELElBQUltQixVQUFVekMsb0RBQWFBLENBQUM7Z0JBQUMyQixNQUFNZSxPQUFPLENBQUN6QixPQUFPO2FBQUMsRUFBRSxDQUFDVSxNQUFNZ0IsTUFBTSxJQUFJLEVBQUUsRUFBRUMsR0FBRyxDQUFDN0IsYUFBYSxNQUFNOEIsTUFBTSxDQUFDQztZQUN4R0wsUUFBUU0sT0FBTyxDQUFDLFNBQVVDLEVBQUU7Z0JBQUksT0FBT0EsR0FBR1QsU0FBUyxDQUFDQyxHQUFHLENBQUMsdUJBQXVCakIsTUFBTSxDQUFDRDtZQUFNO1lBQzVGLE9BQU87Z0JBQ0hlLFNBQVNDLElBQUksQ0FBQ0MsU0FBUyxDQUFDVSxNQUFNLENBQUMsdUJBQXVCMUIsTUFBTSxDQUFDRDtnQkFDN0RtQixRQUFRTSxPQUFPLENBQUMsU0FBVUMsRUFBRTtvQkFBSSxPQUFPQSxHQUFHVCxTQUFTLENBQUNVLE1BQU0sQ0FBQyx1QkFBdUIxQixNQUFNLENBQUNEO2dCQUFNO1lBQ25HO1FBQ0o7UUFDQTtJQUNKLEdBQUc7UUFBQ0ssTUFBTVMsS0FBSztRQUFFVCxNQUFNZSxPQUFPLENBQUN6QixPQUFPO1FBQUVVLE1BQU1nQixNQUFNO0tBQUM7SUFDckQsSUFBSU8sb0JBQW9CakQsOENBQWlCLENBQUMsU0FBVU8sS0FBSyxFQUFFNEMsTUFBTTtRQUM3RCxJQUFJLGFBQWM1QyxTQUFTQSxNQUFNNkMsT0FBTyxDQUFDQyxNQUFNLEtBQUssS0FBTzlDLE1BQU0rQyxJQUFJLEtBQUssV0FBVy9DLE1BQU1nRCxPQUFPLEVBQUc7WUFDakcsT0FBTyxDQUFDdEIsVUFBVWpCLE9BQU8sQ0FBQ3dDLGNBQWM7UUFDNUM7UUFDQSxJQUFJQyxRQUFRbkQsV0FBV0M7UUFDdkIsSUFBSW1ELGFBQWE3QixjQUFjYixPQUFPO1FBQ3RDLElBQUlKLFNBQVMsWUFBWUwsUUFBUUEsTUFBTUssTUFBTSxHQUFHOEMsVUFBVSxDQUFDLEVBQUUsR0FBR0QsS0FBSyxDQUFDLEVBQUU7UUFDeEUsSUFBSTVDLFNBQVMsWUFBWU4sUUFBUUEsTUFBTU0sTUFBTSxHQUFHNkMsVUFBVSxDQUFDLEVBQUUsR0FBR0QsS0FBSyxDQUFDLEVBQUU7UUFDeEUsSUFBSUU7UUFDSixJQUFJQyxTQUFTckQsTUFBTXFELE1BQU07UUFDekIsSUFBSUMsZ0JBQWdCQyxLQUFLQyxHQUFHLENBQUNuRCxVQUFVa0QsS0FBS0MsR0FBRyxDQUFDbEQsVUFBVSxNQUFNO1FBQ2hFLDhFQUE4RTtRQUM5RSxJQUFJLGFBQWFOLFNBQVNzRCxrQkFBa0IsT0FBT0QsT0FBT04sSUFBSSxLQUFLLFNBQVM7WUFDeEUsT0FBTztRQUNYO1FBQ0EsSUFBSVUsK0JBQStCM0Qsc0VBQXVCQSxDQUFDd0QsZUFBZUQ7UUFDMUUsSUFBSSxDQUFDSSw4QkFBOEI7WUFDL0IsT0FBTztRQUNYO1FBQ0EsSUFBSUEsOEJBQThCO1lBQzlCTCxjQUFjRTtRQUNsQixPQUNLO1lBQ0RGLGNBQWNFLGtCQUFrQixNQUFNLE1BQU07WUFDNUNHLCtCQUErQjNELHNFQUF1QkEsQ0FBQ3dELGVBQWVEO1FBQ3RFLHFDQUFxQztRQUN6QztRQUNBLElBQUksQ0FBQ0ksOEJBQThCO1lBQy9CLE9BQU87UUFDWDtRQUNBLElBQUksQ0FBQ2xDLFdBQVdkLE9BQU8sSUFBSSxvQkFBb0JULFNBQVVLLENBQUFBLFVBQVVDLE1BQUssR0FBSTtZQUN4RWlCLFdBQVdkLE9BQU8sR0FBRzJDO1FBQ3pCO1FBQ0EsSUFBSSxDQUFDQSxhQUFhO1lBQ2QsT0FBTztRQUNYO1FBQ0EsSUFBSU0sZ0JBQWdCbkMsV0FBV2QsT0FBTyxJQUFJMkM7UUFDMUMsT0FBT3ZELDJEQUFZQSxDQUFDNkQsZUFBZWQsUUFBUTVDLE9BQU8wRCxrQkFBa0IsTUFBTXJELFNBQVNDLFFBQVE7SUFDL0YsR0FBRyxFQUFFO0lBQ0wsSUFBSXFELGdCQUFnQmxFLDhDQUFpQixDQUFDLFNBQVVtRSxNQUFNO1FBQ2xELElBQUk1RCxRQUFRNEQ7UUFDWixJQUFJLENBQUMzQyxVQUFVNkIsTUFBTSxJQUFJN0IsU0FBUyxDQUFDQSxVQUFVNkIsTUFBTSxHQUFHLEVBQUUsS0FBS3JCLE9BQU87WUFDaEUsc0JBQXNCO1lBQ3RCO1FBQ0o7UUFDQSxJQUFJb0MsUUFBUSxZQUFZN0QsUUFBUUksV0FBV0osU0FBU0QsV0FBV0M7UUFDL0QsSUFBSThELGNBQWMxQyxtQkFBbUJYLE9BQU8sQ0FBQzRCLE1BQU0sQ0FBQyxTQUFVMEIsQ0FBQztZQUFJLE9BQU9BLEVBQUVDLElBQUksS0FBS2hFLE1BQU0rQyxJQUFJLElBQUtnQixDQUFBQSxFQUFFVixNQUFNLEtBQUtyRCxNQUFNcUQsTUFBTSxJQUFJckQsTUFBTXFELE1BQU0sS0FBS1UsRUFBRUUsWUFBWSxLQUFLdkQsYUFBYXFELEVBQUVGLEtBQUssRUFBRUE7UUFBUSxFQUFFLENBQUMsRUFBRTtRQUN4TSxxQ0FBcUM7UUFDckMsSUFBSUMsZUFBZUEsWUFBWUksTUFBTSxFQUFFO1lBQ25DLElBQUlsRSxNQUFNbUUsVUFBVSxFQUFFO2dCQUNsQm5FLE1BQU1vRSxjQUFjO1lBQ3hCO1lBQ0E7UUFDSjtRQUNBLHlCQUF5QjtRQUN6QixJQUFJLENBQUNOLGFBQWE7WUFDZCxJQUFJTyxhQUFhLENBQUMzQyxVQUFVakIsT0FBTyxDQUFDMEIsTUFBTSxJQUFJLEVBQUUsRUFDM0NDLEdBQUcsQ0FBQzdCLFlBQ0o4QixNQUFNLENBQUNDLFNBQ1BELE1BQU0sQ0FBQyxTQUFVaUMsSUFBSTtnQkFBSSxPQUFPQSxLQUFLQyxRQUFRLENBQUN2RSxNQUFNcUQsTUFBTTtZQUFHO1lBQ2xFLElBQUltQixhQUFhSCxXQUFXdkIsTUFBTSxHQUFHLElBQUlKLGtCQUFrQjFDLE9BQU9xRSxVQUFVLENBQUMsRUFBRSxJQUFJLENBQUMzQyxVQUFVakIsT0FBTyxDQUFDZ0UsV0FBVztZQUNqSCxJQUFJRCxZQUFZO2dCQUNaLElBQUl4RSxNQUFNbUUsVUFBVSxFQUFFO29CQUNsQm5FLE1BQU1vRSxjQUFjO2dCQUN4QjtZQUNKO1FBQ0o7SUFDSixHQUFHLEVBQUU7SUFDTCxJQUFJTSxlQUFlakYsOENBQWlCLENBQUMsU0FBVXVFLElBQUksRUFBRUgsS0FBSyxFQUFFUixNQUFNLEVBQUVhLE1BQU07UUFDdEUsSUFBSWxFLFFBQVE7WUFBRWdFLE1BQU1BO1lBQU1ILE9BQU9BO1lBQU9SLFFBQVFBO1lBQVFhLFFBQVFBO1lBQVFELGNBQWNVLHlCQUF5QnRCO1FBQVE7UUFDdkhqQyxtQkFBbUJYLE9BQU8sQ0FBQ21FLElBQUksQ0FBQzVFO1FBQ2hDNkUsV0FBVztZQUNQekQsbUJBQW1CWCxPQUFPLEdBQUdXLG1CQUFtQlgsT0FBTyxDQUFDNEIsTUFBTSxDQUFDLFNBQVUwQixDQUFDO2dCQUFJLE9BQU9BLE1BQU0vRDtZQUFPO1FBQ3RHLEdBQUc7SUFDUCxHQUFHLEVBQUU7SUFDTCxJQUFJOEUsbUJBQW1CckYsOENBQWlCLENBQUMsU0FBVU8sS0FBSztRQUNwRHNCLGNBQWNiLE9BQU8sR0FBR1YsV0FBV0M7UUFDbkN1QixXQUFXZCxPQUFPLEdBQUdzRTtJQUN6QixHQUFHLEVBQUU7SUFDTCxJQUFJQyxjQUFjdkYsOENBQWlCLENBQUMsU0FBVU8sS0FBSztRQUMvQzBFLGFBQWExRSxNQUFNK0MsSUFBSSxFQUFFM0MsV0FBV0osUUFBUUEsTUFBTXFELE1BQU0sRUFBRVgsa0JBQWtCMUMsT0FBT21CLE1BQU1lLE9BQU8sQ0FBQ3pCLE9BQU87SUFDNUcsR0FBRyxFQUFFO0lBQ0wsSUFBSXdFLGtCQUFrQnhGLDhDQUFpQixDQUFDLFNBQVVPLEtBQUs7UUFDbkQwRSxhQUFhMUUsTUFBTStDLElBQUksRUFBRWhELFdBQVdDLFFBQVFBLE1BQU1xRCxNQUFNLEVBQUVYLGtCQUFrQjFDLE9BQU9tQixNQUFNZSxPQUFPLENBQUN6QixPQUFPO0lBQzVHLEdBQUcsRUFBRTtJQUNMaEIsNENBQWUsQ0FBQztRQUNad0IsVUFBVTJELElBQUksQ0FBQ25EO1FBQ2ZOLE1BQU0rRCxZQUFZLENBQUM7WUFDZkMsaUJBQWlCSDtZQUNqQkksZ0JBQWdCSjtZQUNoQkssb0JBQW9CSjtRQUN4QjtRQUNBcEQsU0FBU3lELGdCQUFnQixDQUFDLFNBQVMzQixlQUFlL0QseURBQVVBO1FBQzVEaUMsU0FBU3lELGdCQUFnQixDQUFDLGFBQWEzQixlQUFlL0QseURBQVVBO1FBQ2hFaUMsU0FBU3lELGdCQUFnQixDQUFDLGNBQWNSLGtCQUFrQmxGLHlEQUFVQTtRQUNwRSxPQUFPO1lBQ0hxQixZQUFZQSxVQUFVb0IsTUFBTSxDQUFDLFNBQVVrRCxJQUFJO2dCQUFJLE9BQU9BLFNBQVM5RDtZQUFPO1lBQ3RFSSxTQUFTMkQsbUJBQW1CLENBQUMsU0FBUzdCLGVBQWUvRCx5REFBVUE7WUFDL0RpQyxTQUFTMkQsbUJBQW1CLENBQUMsYUFBYTdCLGVBQWUvRCx5REFBVUE7WUFDbkVpQyxTQUFTMkQsbUJBQW1CLENBQUMsY0FBY1Ysa0JBQWtCbEYseURBQVVBO1FBQzNFO0lBQ0osR0FBRyxFQUFFO0lBQ0wsSUFBSTZGLGtCQUFrQnRFLE1BQU1zRSxlQUFlLEVBQUU3RCxRQUFRVCxNQUFNUyxLQUFLO0lBQ2hFLHFCQUFRbkMsZ0RBQW1CLENBQUNBLDJDQUFjLEVBQUUsTUFDeENtQyxzQkFBUW5DLGdEQUFtQixDQUFDZ0MsT0FBTztRQUFFbUUsUUFBUS9FLGNBQWNDO0lBQUksS0FBSyxNQUNwRTJFLGdDQUFrQmhHLGdEQUFtQixDQUFDQyxvRUFBZUEsRUFBRTtRQUFFbUcsWUFBWTFFLE1BQU0wRSxVQUFVO1FBQUVDLFNBQVMzRSxNQUFNMkUsT0FBTztJQUFDLEtBQUs7QUFDM0g7QUFDQSxTQUFTbkIseUJBQXlCTCxJQUFJO0lBQ2xDLElBQUlMLGVBQWU7SUFDbkIsTUFBT0ssU0FBUyxLQUFNO1FBQ2xCLElBQUlBLGdCQUFnQnlCLFlBQVk7WUFDNUI5QixlQUFlSyxLQUFLMEIsSUFBSTtZQUN4QjFCLE9BQU9BLEtBQUswQixJQUFJO1FBQ3BCO1FBQ0ExQixPQUFPQSxLQUFLMkIsVUFBVTtJQUMxQjtJQUNBLE9BQU9oQztBQUNYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1yZW1vdmUtc2Nyb2xsQDIuNy4xX0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9TaWRlRWZmZWN0LmpzPzI1MGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgX19zcHJlYWRBcnJheSB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUmVtb3ZlU2Nyb2xsQmFyIH0gZnJvbSAncmVhY3QtcmVtb3ZlLXNjcm9sbC1iYXInO1xuaW1wb3J0IHsgc3R5bGVTaW5nbGV0b24gfSBmcm9tICdyZWFjdC1zdHlsZS1zaW5nbGV0b24nO1xuaW1wb3J0IHsgbm9uUGFzc2l2ZSB9IGZyb20gJy4vYWdncmVzaXZlQ2FwdHVyZSc7XG5pbXBvcnQgeyBoYW5kbGVTY3JvbGwsIGxvY2F0aW9uQ291bGRCZVNjcm9sbGVkIH0gZnJvbSAnLi9oYW5kbGVTY3JvbGwnO1xuZXhwb3J0IHZhciBnZXRUb3VjaFhZID0gZnVuY3Rpb24gKGV2ZW50KSB7XG4gICAgcmV0dXJuICdjaGFuZ2VkVG91Y2hlcycgaW4gZXZlbnQgPyBbZXZlbnQuY2hhbmdlZFRvdWNoZXNbMF0uY2xpZW50WCwgZXZlbnQuY2hhbmdlZFRvdWNoZXNbMF0uY2xpZW50WV0gOiBbMCwgMF07XG59O1xuZXhwb3J0IHZhciBnZXREZWx0YVhZID0gZnVuY3Rpb24gKGV2ZW50KSB7IHJldHVybiBbZXZlbnQuZGVsdGFYLCBldmVudC5kZWx0YVldOyB9O1xudmFyIGV4dHJhY3RSZWYgPSBmdW5jdGlvbiAocmVmKSB7XG4gICAgcmV0dXJuIHJlZiAmJiAnY3VycmVudCcgaW4gcmVmID8gcmVmLmN1cnJlbnQgOiByZWY7XG59O1xudmFyIGRlbHRhQ29tcGFyZSA9IGZ1bmN0aW9uICh4LCB5KSB7IHJldHVybiB4WzBdID09PSB5WzBdICYmIHhbMV0gPT09IHlbMV07IH07XG52YXIgZ2VuZXJhdGVTdHlsZSA9IGZ1bmN0aW9uIChpZCkgeyByZXR1cm4gXCJcXG4gIC5ibG9jay1pbnRlcmFjdGl2aXR5LVwiLmNvbmNhdChpZCwgXCIge3BvaW50ZXItZXZlbnRzOiBub25lO31cXG4gIC5hbGxvdy1pbnRlcmFjdGl2aXR5LVwiKS5jb25jYXQoaWQsIFwiIHtwb2ludGVyLWV2ZW50czogYWxsO31cXG5cIik7IH07XG52YXIgaWRDb3VudGVyID0gMDtcbnZhciBsb2NrU3RhY2sgPSBbXTtcbmV4cG9ydCBmdW5jdGlvbiBSZW1vdmVTY3JvbGxTaWRlQ2FyKHByb3BzKSB7XG4gICAgdmFyIHNob3VsZFByZXZlbnRRdWV1ZSA9IFJlYWN0LnVzZVJlZihbXSk7XG4gICAgdmFyIHRvdWNoU3RhcnRSZWYgPSBSZWFjdC51c2VSZWYoWzAsIDBdKTtcbiAgICB2YXIgYWN0aXZlQXhpcyA9IFJlYWN0LnVzZVJlZigpO1xuICAgIHZhciBpZCA9IFJlYWN0LnVzZVN0YXRlKGlkQ291bnRlcisrKVswXTtcbiAgICB2YXIgU3R5bGUgPSBSZWFjdC51c2VTdGF0ZShzdHlsZVNpbmdsZXRvbilbMF07XG4gICAgdmFyIGxhc3RQcm9wcyA9IFJlYWN0LnVzZVJlZihwcm9wcyk7XG4gICAgUmVhY3QudXNlRWZmZWN0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgbGFzdFByb3BzLmN1cnJlbnQgPSBwcm9wcztcbiAgICB9LCBbcHJvcHNdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgICAgICBpZiAocHJvcHMuaW5lcnQpIHtcbiAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuY2xhc3NMaXN0LmFkZChcImJsb2NrLWludGVyYWN0aXZpdHktXCIuY29uY2F0KGlkKSk7XG4gICAgICAgICAgICB2YXIgYWxsb3dfMSA9IF9fc3ByZWFkQXJyYXkoW3Byb3BzLmxvY2tSZWYuY3VycmVudF0sIChwcm9wcy5zaGFyZHMgfHwgW10pLm1hcChleHRyYWN0UmVmKSwgdHJ1ZSkuZmlsdGVyKEJvb2xlYW4pO1xuICAgICAgICAgICAgYWxsb3dfMS5mb3JFYWNoKGZ1bmN0aW9uIChlbCkgeyByZXR1cm4gZWwuY2xhc3NMaXN0LmFkZChcImFsbG93LWludGVyYWN0aXZpdHktXCIuY29uY2F0KGlkKSk7IH0pO1xuICAgICAgICAgICAgcmV0dXJuIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LmNsYXNzTGlzdC5yZW1vdmUoXCJibG9jay1pbnRlcmFjdGl2aXR5LVwiLmNvbmNhdChpZCkpO1xuICAgICAgICAgICAgICAgIGFsbG93XzEuZm9yRWFjaChmdW5jdGlvbiAoZWwpIHsgcmV0dXJuIGVsLmNsYXNzTGlzdC5yZW1vdmUoXCJhbGxvdy1pbnRlcmFjdGl2aXR5LVwiLmNvbmNhdChpZCkpOyB9KTtcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuO1xuICAgIH0sIFtwcm9wcy5pbmVydCwgcHJvcHMubG9ja1JlZi5jdXJyZW50LCBwcm9wcy5zaGFyZHNdKTtcbiAgICB2YXIgc2hvdWxkQ2FuY2VsRXZlbnQgPSBSZWFjdC51c2VDYWxsYmFjayhmdW5jdGlvbiAoZXZlbnQsIHBhcmVudCkge1xuICAgICAgICBpZiAoKCd0b3VjaGVzJyBpbiBldmVudCAmJiBldmVudC50b3VjaGVzLmxlbmd0aCA9PT0gMikgfHwgKGV2ZW50LnR5cGUgPT09ICd3aGVlbCcgJiYgZXZlbnQuY3RybEtleSkpIHtcbiAgICAgICAgICAgIHJldHVybiAhbGFzdFByb3BzLmN1cnJlbnQuYWxsb3dQaW5jaFpvb207XG4gICAgICAgIH1cbiAgICAgICAgdmFyIHRvdWNoID0gZ2V0VG91Y2hYWShldmVudCk7XG4gICAgICAgIHZhciB0b3VjaFN0YXJ0ID0gdG91Y2hTdGFydFJlZi5jdXJyZW50O1xuICAgICAgICB2YXIgZGVsdGFYID0gJ2RlbHRhWCcgaW4gZXZlbnQgPyBldmVudC5kZWx0YVggOiB0b3VjaFN0YXJ0WzBdIC0gdG91Y2hbMF07XG4gICAgICAgIHZhciBkZWx0YVkgPSAnZGVsdGFZJyBpbiBldmVudCA/IGV2ZW50LmRlbHRhWSA6IHRvdWNoU3RhcnRbMV0gLSB0b3VjaFsxXTtcbiAgICAgICAgdmFyIGN1cnJlbnRBeGlzO1xuICAgICAgICB2YXIgdGFyZ2V0ID0gZXZlbnQudGFyZ2V0O1xuICAgICAgICB2YXIgbW92ZURpcmVjdGlvbiA9IE1hdGguYWJzKGRlbHRhWCkgPiBNYXRoLmFicyhkZWx0YVkpID8gJ2gnIDogJ3YnO1xuICAgICAgICAvLyBhbGxvdyBob3Jpem9udGFsIHRvdWNoIG1vdmUgb24gUmFuZ2UgaW5wdXRzLiBUaGV5IHdpbGwgbm90IGNhdXNlIGFueSBzY3JvbGxcbiAgICAgICAgaWYgKCd0b3VjaGVzJyBpbiBldmVudCAmJiBtb3ZlRGlyZWN0aW9uID09PSAnaCcgJiYgdGFyZ2V0LnR5cGUgPT09ICdyYW5nZScpIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgY2FuQmVTY3JvbGxlZEluTWFpbkRpcmVjdGlvbiA9IGxvY2F0aW9uQ291bGRCZVNjcm9sbGVkKG1vdmVEaXJlY3Rpb24sIHRhcmdldCk7XG4gICAgICAgIGlmICghY2FuQmVTY3JvbGxlZEluTWFpbkRpcmVjdGlvbikge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNhbkJlU2Nyb2xsZWRJbk1haW5EaXJlY3Rpb24pIHtcbiAgICAgICAgICAgIGN1cnJlbnRBeGlzID0gbW92ZURpcmVjdGlvbjtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGN1cnJlbnRBeGlzID0gbW92ZURpcmVjdGlvbiA9PT0gJ3YnID8gJ2gnIDogJ3YnO1xuICAgICAgICAgICAgY2FuQmVTY3JvbGxlZEluTWFpbkRpcmVjdGlvbiA9IGxvY2F0aW9uQ291bGRCZVNjcm9sbGVkKG1vdmVEaXJlY3Rpb24sIHRhcmdldCk7XG4gICAgICAgICAgICAvLyBvdGhlciBheGlzIG1pZ2h0IGJlIG5vdCBzY3JvbGxhYmxlXG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFjYW5CZVNjcm9sbGVkSW5NYWluRGlyZWN0aW9uKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFhY3RpdmVBeGlzLmN1cnJlbnQgJiYgJ2NoYW5nZWRUb3VjaGVzJyBpbiBldmVudCAmJiAoZGVsdGFYIHx8IGRlbHRhWSkpIHtcbiAgICAgICAgICAgIGFjdGl2ZUF4aXMuY3VycmVudCA9IGN1cnJlbnRBeGlzO1xuICAgICAgICB9XG4gICAgICAgIGlmICghY3VycmVudEF4aXMpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciBjYW5jZWxpbmdBeGlzID0gYWN0aXZlQXhpcy5jdXJyZW50IHx8IGN1cnJlbnRBeGlzO1xuICAgICAgICByZXR1cm4gaGFuZGxlU2Nyb2xsKGNhbmNlbGluZ0F4aXMsIHBhcmVudCwgZXZlbnQsIGNhbmNlbGluZ0F4aXMgPT09ICdoJyA/IGRlbHRhWCA6IGRlbHRhWSwgdHJ1ZSk7XG4gICAgfSwgW10pO1xuICAgIHZhciBzaG91bGRQcmV2ZW50ID0gUmVhY3QudXNlQ2FsbGJhY2soZnVuY3Rpb24gKF9ldmVudCkge1xuICAgICAgICB2YXIgZXZlbnQgPSBfZXZlbnQ7XG4gICAgICAgIGlmICghbG9ja1N0YWNrLmxlbmd0aCB8fCBsb2NrU3RhY2tbbG9ja1N0YWNrLmxlbmd0aCAtIDFdICE9PSBTdHlsZSkge1xuICAgICAgICAgICAgLy8gbm90IHRoZSBsYXN0IGFjdGl2ZVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIHZhciBkZWx0YSA9ICdkZWx0YVknIGluIGV2ZW50ID8gZ2V0RGVsdGFYWShldmVudCkgOiBnZXRUb3VjaFhZKGV2ZW50KTtcbiAgICAgICAgdmFyIHNvdXJjZUV2ZW50ID0gc2hvdWxkUHJldmVudFF1ZXVlLmN1cnJlbnQuZmlsdGVyKGZ1bmN0aW9uIChlKSB7IHJldHVybiBlLm5hbWUgPT09IGV2ZW50LnR5cGUgJiYgKGUudGFyZ2V0ID09PSBldmVudC50YXJnZXQgfHwgZXZlbnQudGFyZ2V0ID09PSBlLnNoYWRvd1BhcmVudCkgJiYgZGVsdGFDb21wYXJlKGUuZGVsdGEsIGRlbHRhKTsgfSlbMF07XG4gICAgICAgIC8vIHNlbGYgZXZlbnQsIGFuZCBzaG91bGQgYmUgY2FuY2VsZWRcbiAgICAgICAgaWYgKHNvdXJjZUV2ZW50ICYmIHNvdXJjZUV2ZW50LnNob3VsZCkge1xuICAgICAgICAgICAgaWYgKGV2ZW50LmNhbmNlbGFibGUpIHtcbiAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIG91dHNpZGUgb3Igc2hhcmQgZXZlbnRcbiAgICAgICAgaWYgKCFzb3VyY2VFdmVudCkge1xuICAgICAgICAgICAgdmFyIHNoYXJkTm9kZXMgPSAobGFzdFByb3BzLmN1cnJlbnQuc2hhcmRzIHx8IFtdKVxuICAgICAgICAgICAgICAgIC5tYXAoZXh0cmFjdFJlZilcbiAgICAgICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pXG4gICAgICAgICAgICAgICAgLmZpbHRlcihmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gbm9kZS5jb250YWlucyhldmVudC50YXJnZXQpOyB9KTtcbiAgICAgICAgICAgIHZhciBzaG91bGRTdG9wID0gc2hhcmROb2Rlcy5sZW5ndGggPiAwID8gc2hvdWxkQ2FuY2VsRXZlbnQoZXZlbnQsIHNoYXJkTm9kZXNbMF0pIDogIWxhc3RQcm9wcy5jdXJyZW50Lm5vSXNvbGF0aW9uO1xuICAgICAgICAgICAgaWYgKHNob3VsZFN0b3ApIHtcbiAgICAgICAgICAgICAgICBpZiAoZXZlbnQuY2FuY2VsYWJsZSkge1xuICAgICAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0sIFtdKTtcbiAgICB2YXIgc2hvdWxkQ2FuY2VsID0gUmVhY3QudXNlQ2FsbGJhY2soZnVuY3Rpb24gKG5hbWUsIGRlbHRhLCB0YXJnZXQsIHNob3VsZCkge1xuICAgICAgICB2YXIgZXZlbnQgPSB7IG5hbWU6IG5hbWUsIGRlbHRhOiBkZWx0YSwgdGFyZ2V0OiB0YXJnZXQsIHNob3VsZDogc2hvdWxkLCBzaGFkb3dQYXJlbnQ6IGdldE91dGVybW9zdFNoYWRvd1BhcmVudCh0YXJnZXQpIH07XG4gICAgICAgIHNob3VsZFByZXZlbnRRdWV1ZS5jdXJyZW50LnB1c2goZXZlbnQpO1xuICAgICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHNob3VsZFByZXZlbnRRdWV1ZS5jdXJyZW50ID0gc2hvdWxkUHJldmVudFF1ZXVlLmN1cnJlbnQuZmlsdGVyKGZ1bmN0aW9uIChlKSB7IHJldHVybiBlICE9PSBldmVudDsgfSk7XG4gICAgICAgIH0sIDEpO1xuICAgIH0sIFtdKTtcbiAgICB2YXIgc2Nyb2xsVG91Y2hTdGFydCA9IFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgICB0b3VjaFN0YXJ0UmVmLmN1cnJlbnQgPSBnZXRUb3VjaFhZKGV2ZW50KTtcbiAgICAgICAgYWN0aXZlQXhpcy5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIH0sIFtdKTtcbiAgICB2YXIgc2Nyb2xsV2hlZWwgPSBSZWFjdC51c2VDYWxsYmFjayhmdW5jdGlvbiAoZXZlbnQpIHtcbiAgICAgICAgc2hvdWxkQ2FuY2VsKGV2ZW50LnR5cGUsIGdldERlbHRhWFkoZXZlbnQpLCBldmVudC50YXJnZXQsIHNob3VsZENhbmNlbEV2ZW50KGV2ZW50LCBwcm9wcy5sb2NrUmVmLmN1cnJlbnQpKTtcbiAgICB9LCBbXSk7XG4gICAgdmFyIHNjcm9sbFRvdWNoTW92ZSA9IFJlYWN0LnVzZUNhbGxiYWNrKGZ1bmN0aW9uIChldmVudCkge1xuICAgICAgICBzaG91bGRDYW5jZWwoZXZlbnQudHlwZSwgZ2V0VG91Y2hYWShldmVudCksIGV2ZW50LnRhcmdldCwgc2hvdWxkQ2FuY2VsRXZlbnQoZXZlbnQsIHByb3BzLmxvY2tSZWYuY3VycmVudCkpO1xuICAgIH0sIFtdKTtcbiAgICBSZWFjdC51c2VFZmZlY3QoZnVuY3Rpb24gKCkge1xuICAgICAgICBsb2NrU3RhY2sucHVzaChTdHlsZSk7XG4gICAgICAgIHByb3BzLnNldENhbGxiYWNrcyh7XG4gICAgICAgICAgICBvblNjcm9sbENhcHR1cmU6IHNjcm9sbFdoZWVsLFxuICAgICAgICAgICAgb25XaGVlbENhcHR1cmU6IHNjcm9sbFdoZWVsLFxuICAgICAgICAgICAgb25Ub3VjaE1vdmVDYXB0dXJlOiBzY3JvbGxUb3VjaE1vdmUsXG4gICAgICAgIH0pO1xuICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd3aGVlbCcsIHNob3VsZFByZXZlbnQsIG5vblBhc3NpdmUpO1xuICAgICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCd0b3VjaG1vdmUnLCBzaG91bGRQcmV2ZW50LCBub25QYXNzaXZlKTtcbiAgICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigndG91Y2hzdGFydCcsIHNjcm9sbFRvdWNoU3RhcnQsIG5vblBhc3NpdmUpO1xuICAgICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgbG9ja1N0YWNrID0gbG9ja1N0YWNrLmZpbHRlcihmdW5jdGlvbiAoaW5zdCkgeyByZXR1cm4gaW5zdCAhPT0gU3R5bGU7IH0pO1xuICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignd2hlZWwnLCBzaG91bGRQcmV2ZW50LCBub25QYXNzaXZlKTtcbiAgICAgICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3RvdWNobW92ZScsIHNob3VsZFByZXZlbnQsIG5vblBhc3NpdmUpO1xuICAgICAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigndG91Y2hzdGFydCcsIHNjcm9sbFRvdWNoU3RhcnQsIG5vblBhc3NpdmUpO1xuICAgICAgICB9O1xuICAgIH0sIFtdKTtcbiAgICB2YXIgcmVtb3ZlU2Nyb2xsQmFyID0gcHJvcHMucmVtb3ZlU2Nyb2xsQmFyLCBpbmVydCA9IHByb3BzLmluZXJ0O1xuICAgIHJldHVybiAoUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCxcbiAgICAgICAgaW5lcnQgPyBSZWFjdC5jcmVhdGVFbGVtZW50KFN0eWxlLCB7IHN0eWxlczogZ2VuZXJhdGVTdHlsZShpZCkgfSkgOiBudWxsLFxuICAgICAgICByZW1vdmVTY3JvbGxCYXIgPyBSZWFjdC5jcmVhdGVFbGVtZW50KFJlbW92ZVNjcm9sbEJhciwgeyBub1JlbGF0aXZlOiBwcm9wcy5ub1JlbGF0aXZlLCBnYXBNb2RlOiBwcm9wcy5nYXBNb2RlIH0pIDogbnVsbCkpO1xufVxuZnVuY3Rpb24gZ2V0T3V0ZXJtb3N0U2hhZG93UGFyZW50KG5vZGUpIHtcbiAgICB2YXIgc2hhZG93UGFyZW50ID0gbnVsbDtcbiAgICB3aGlsZSAobm9kZSAhPT0gbnVsbCkge1xuICAgICAgICBpZiAobm9kZSBpbnN0YW5jZW9mIFNoYWRvd1Jvb3QpIHtcbiAgICAgICAgICAgIHNoYWRvd1BhcmVudCA9IG5vZGUuaG9zdDtcbiAgICAgICAgICAgIG5vZGUgPSBub2RlLmhvc3Q7XG4gICAgICAgIH1cbiAgICAgICAgbm9kZSA9IG5vZGUucGFyZW50Tm9kZTtcbiAgICB9XG4gICAgcmV0dXJuIHNoYWRvd1BhcmVudDtcbn1cbiJdLCJuYW1lcyI6WyJfX3NwcmVhZEFycmF5IiwiUmVhY3QiLCJSZW1vdmVTY3JvbGxCYXIiLCJzdHlsZVNpbmdsZXRvbiIsIm5vblBhc3NpdmUiLCJoYW5kbGVTY3JvbGwiLCJsb2NhdGlvbkNvdWxkQmVTY3JvbGxlZCIsImdldFRvdWNoWFkiLCJldmVudCIsImNoYW5nZWRUb3VjaGVzIiwiY2xpZW50WCIsImNsaWVudFkiLCJnZXREZWx0YVhZIiwiZGVsdGFYIiwiZGVsdGFZIiwiZXh0cmFjdFJlZiIsInJlZiIsImN1cnJlbnQiLCJkZWx0YUNvbXBhcmUiLCJ4IiwieSIsImdlbmVyYXRlU3R5bGUiLCJpZCIsImNvbmNhdCIsImlkQ291bnRlciIsImxvY2tTdGFjayIsIlJlbW92ZVNjcm9sbFNpZGVDYXIiLCJwcm9wcyIsInNob3VsZFByZXZlbnRRdWV1ZSIsInVzZVJlZiIsInRvdWNoU3RhcnRSZWYiLCJhY3RpdmVBeGlzIiwidXNlU3RhdGUiLCJTdHlsZSIsImxhc3RQcm9wcyIsInVzZUVmZmVjdCIsImluZXJ0IiwiZG9jdW1lbnQiLCJib2R5IiwiY2xhc3NMaXN0IiwiYWRkIiwiYWxsb3dfMSIsImxvY2tSZWYiLCJzaGFyZHMiLCJtYXAiLCJmaWx0ZXIiLCJCb29sZWFuIiwiZm9yRWFjaCIsImVsIiwicmVtb3ZlIiwic2hvdWxkQ2FuY2VsRXZlbnQiLCJ1c2VDYWxsYmFjayIsInBhcmVudCIsInRvdWNoZXMiLCJsZW5ndGgiLCJ0eXBlIiwiY3RybEtleSIsImFsbG93UGluY2hab29tIiwidG91Y2giLCJ0b3VjaFN0YXJ0IiwiY3VycmVudEF4aXMiLCJ0YXJnZXQiLCJtb3ZlRGlyZWN0aW9uIiwiTWF0aCIsImFicyIsImNhbkJlU2Nyb2xsZWRJbk1haW5EaXJlY3Rpb24iLCJjYW5jZWxpbmdBeGlzIiwic2hvdWxkUHJldmVudCIsIl9ldmVudCIsImRlbHRhIiwic291cmNlRXZlbnQiLCJlIiwibmFtZSIsInNoYWRvd1BhcmVudCIsInNob3VsZCIsImNhbmNlbGFibGUiLCJwcmV2ZW50RGVmYXVsdCIsInNoYXJkTm9kZXMiLCJub2RlIiwiY29udGFpbnMiLCJzaG91bGRTdG9wIiwibm9Jc29sYXRpb24iLCJzaG91bGRDYW5jZWwiLCJnZXRPdXRlcm1vc3RTaGFkb3dQYXJlbnQiLCJwdXNoIiwic2V0VGltZW91dCIsInNjcm9sbFRvdWNoU3RhcnQiLCJ1bmRlZmluZWQiLCJzY3JvbGxXaGVlbCIsInNjcm9sbFRvdWNoTW92ZSIsInNldENhbGxiYWNrcyIsIm9uU2Nyb2xsQ2FwdHVyZSIsIm9uV2hlZWxDYXB0dXJlIiwib25Ub3VjaE1vdmVDYXB0dXJlIiwiYWRkRXZlbnRMaXN0ZW5lciIsImluc3QiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicmVtb3ZlU2Nyb2xsQmFyIiwiY3JlYXRlRWxlbWVudCIsIkZyYWdtZW50Iiwic3R5bGVzIiwibm9SZWxhdGl2ZSIsImdhcE1vZGUiLCJTaGFkb3dSb290IiwiaG9zdCIsInBhcmVudE5vZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/SideEffect.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/UI.js":
/*!***********************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/UI.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RemoveScroll: () => (/* binding */ RemoveScroll)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/../../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-remove-scroll-bar/constants */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll-bar@2.3.8_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll-bar/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/use-callback-ref@1.3.3_@types+react@18.3.23_react@18.3.1/node_modules/use-callback-ref/dist/es2015/useMergeRef.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n\n\nvar nothing = function() {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */ var RemoveScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function(props, parentRef) {\n    var ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    var _a = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? \"div\" : _b, gapMode = props.gapMode, rest = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(props, [\n        \"forwardProps\",\n        \"children\",\n        \"className\",\n        \"removeScrollBar\",\n        \"enabled\",\n        \"shards\",\n        \"sideCar\",\n        \"noRelative\",\n        \"noIsolation\",\n        \"inert\",\n        \"allowPinchZoom\",\n        \"as\",\n        \"gapMode\"\n    ]);\n    var SideCar = sideCar;\n    var containerRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_3__.useMergeRefs)([\n        ref,\n        parentRef\n    ]);\n    var containerProps = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, rest), callbacks);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, enabled && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SideCar, {\n        sideCar: _medium__WEBPACK_IMPORTED_MODULE_4__.effectCar,\n        removeScrollBar: removeScrollBar,\n        shards: shards,\n        noRelative: noRelative,\n        noIsolation: noIsolation,\n        inert: inert,\n        setCallbacks: setCallbacks,\n        allowPinchZoom: !!allowPinchZoom,\n        lockRef: ref,\n        gapMode: gapMode\n    }), forwardProps ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children), (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps), {\n        ref: containerRef\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Container, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, containerProps, {\n        className: className,\n        ref: containerRef\n    }), children));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false\n};\nRemoveScroll.classNames = {\n    fullWidth: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.fullWidthClassName,\n    zeroRight: react_remove_scroll_bar_constants__WEBPACK_IMPORTED_MODULE_1__.zeroRightClassName\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/UI.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nonPassive: () => (/* binding */ nonPassive)\n/* harmony export */ });\nvar passiveSupported = false;\nif (false) { var options; }\nvar nonPassive = passiveSupported ? {\n    passive: false\n} : false;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L2FnZ3Jlc2l2ZUNhcHR1cmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLElBQUlBLG1CQUFtQjtBQUN2QixJQUFJLEtBQWtCLEVBQWEsZ0JBZ0JsQztBQUNNLElBQUlTLGFBQWFULG1CQUFtQjtJQUFFVSxTQUFTO0FBQU0sSUFBSSxNQUFNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1yZW1vdmUtc2Nyb2xsQDIuNy4xX0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9hZ2dyZXNpdmVDYXB0dXJlLmpzPzVlZDIiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHBhc3NpdmVTdXBwb3J0ZWQgPSBmYWxzZTtcbmlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIHRyeSB7XG4gICAgICAgIHZhciBvcHRpb25zID0gT2JqZWN0LmRlZmluZVByb3BlcnR5KHt9LCAncGFzc2l2ZScsIHtcbiAgICAgICAgICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgIHBhc3NpdmVTdXBwb3J0ZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIC8vIEB0cy1pZ25vcmVcbiAgICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Rlc3QnLCBvcHRpb25zLCBvcHRpb25zKTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZVxuICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigndGVzdCcsIG9wdGlvbnMsIG9wdGlvbnMpO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyKSB7XG4gICAgICAgIHBhc3NpdmVTdXBwb3J0ZWQgPSBmYWxzZTtcbiAgICB9XG59XG5leHBvcnQgdmFyIG5vblBhc3NpdmUgPSBwYXNzaXZlU3VwcG9ydGVkID8geyBwYXNzaXZlOiBmYWxzZSB9IDogZmFsc2U7XG4iXSwibmFtZXMiOlsicGFzc2l2ZVN1cHBvcnRlZCIsIm9wdGlvbnMiLCJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImdldCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZXJyIiwibm9uUGFzc2l2ZSIsInBhc3NpdmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/handleScroll.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/handleScroll.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleScroll: () => (/* binding */ handleScroll),\n/* harmony export */   locationCouldBeScrolled: () => (/* binding */ locationCouldBeScrolled)\n/* harmony export */ });\nvar alwaysContainsScroll = function(node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === \"TEXTAREA\";\n};\nvar elementCanBeScrolled = function(node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return(// not-not-scrollable\n    styles[overflow] !== \"hidden\" && // contains scroll inside self\n    !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === \"visible\"));\n};\nvar elementCouldBeVScrolled = function(node) {\n    return elementCanBeScrolled(node, \"overflowY\");\n};\nvar elementCouldBeHScrolled = function(node) {\n    return elementCanBeScrolled(node, \"overflowX\");\n};\nvar locationCouldBeScrolled = function(axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== \"undefined\" && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    }while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function(_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight\n    ];\n};\nvar getHScrollVariables = function(_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth\n    ];\n};\nvar elementCouldBeScrolled = function(axis, node) {\n    return axis === \"v\" ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function(axis, node) {\n    return axis === \"v\" ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function(axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */ return axis === \"h\" && direction === \"rtl\" ? -1 : 1;\n};\nvar handleScroll = function(axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1;\n    }while (// portaled content\n    !targetInLock && target !== document.body || // self content\n    targetInLock && (endTarget.contains(target) || endTarget === target));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive && (noOverscroll && Math.abs(availableScroll) < 1 || !noOverscroll && delta > availableScroll)) {\n        shouldCancelScroll = true;\n    } else if (!isDeltaPositive && (noOverscroll && Math.abs(availableScrollTop) < 1 || !noOverscroll && -delta > availableScrollTop)) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/handleScroll.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/medium.js":
/*!***************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/medium.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   effectCar: () => (/* binding */ effectCar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/../../node_modules/.pnpm/use-sidecar@1.1.3_@types+react@18.3.23_react@18.3.1/node_modules/use-sidecar/dist/es2015/medium.js\");\n\nvar effectCar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L21lZGl1bS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUMzQyxJQUFJQyxZQUFZRCxnRUFBbUJBLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L21lZGl1bS5qcz80NjQxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNpZGVjYXJNZWRpdW0gfSBmcm9tICd1c2Utc2lkZWNhcic7XG5leHBvcnQgdmFyIGVmZmVjdENhciA9IGNyZWF0ZVNpZGVjYXJNZWRpdW0oKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVTaWRlY2FyTWVkaXVtIiwiZWZmZWN0Q2FyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/medium.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/sidecar.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/sidecar.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/../../node_modules/.pnpm/use-sidecar@1.1.3_@types+react@18.3.23_react@18.3.1/node_modules/use-sidecar/dist/es2015/exports.js\");\n/* harmony import */ var _SideEffect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SideEffect */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/SideEffect.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/medium.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.exportSidecar)(_medium__WEBPACK_IMPORTED_MODULE_1__.effectCar, _SideEffect__WEBPACK_IMPORTED_MODULE_2__.RemoveScrollSideCar));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LXJlbW92ZS1zY3JvbGxAMi43LjFfQHR5cGVzK3JlYWN0QDE4LjMuMjNfcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9yZWFjdC1yZW1vdmUtc2Nyb2xsL2Rpc3QvZXMyMDE1L3NpZGVjYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE0QztBQUNPO0FBQ2Q7QUFDckMsaUVBQWVBLDBEQUFhQSxDQUFDRSw4Q0FBU0EsRUFBRUQsNERBQW1CQSxDQUFDQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1yZW1vdmUtc2Nyb2xsQDIuNy4xX0B0eXBlcytyZWFjdEAxOC4zLjIzX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvcmVhY3QtcmVtb3ZlLXNjcm9sbC9kaXN0L2VzMjAxNS9zaWRlY2FyLmpzPzc3ZTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZXhwb3J0U2lkZWNhciB9IGZyb20gJ3VzZS1zaWRlY2FyJztcbmltcG9ydCB7IFJlbW92ZVNjcm9sbFNpZGVDYXIgfSBmcm9tICcuL1NpZGVFZmZlY3QnO1xuaW1wb3J0IHsgZWZmZWN0Q2FyIH0gZnJvbSAnLi9tZWRpdW0nO1xuZXhwb3J0IGRlZmF1bHQgZXhwb3J0U2lkZWNhcihlZmZlY3RDYXIsIFJlbW92ZVNjcm9sbFNpZGVDYXIpO1xuIl0sIm5hbWVzIjpbImV4cG9ydFNpZGVjYXIiLCJSZW1vdmVTY3JvbGxTaWRlQ2FyIiwiZWZmZWN0Q2FyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/sidecar.js\n");

/***/ })

};
;