"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+core@1.7.2";
exports.ids = ["vendor-chunks/@floating-ui+core@1.7.2"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@floating-ui+core@1.7.2/node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@floating-ui+core@1.7.2/node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n    let { reference, floating } = _ref;\n    const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n    const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const isVertical = sideAxis === \"y\";\n    const commonX = reference.x + reference.width / 2 - floating.width / 2;\n    const commonY = reference.y + reference.height / 2 - floating.height / 2;\n    const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n    let coords;\n    switch(side){\n        case \"top\":\n            coords = {\n                x: commonX,\n                y: reference.y - floating.height\n            };\n            break;\n        case \"bottom\":\n            coords = {\n                x: commonX,\n                y: reference.y + reference.height\n            };\n            break;\n        case \"right\":\n            coords = {\n                x: reference.x + reference.width,\n                y: commonY\n            };\n            break;\n        case \"left\":\n            coords = {\n                x: reference.x - floating.width,\n                y: commonY\n            };\n            break;\n        default:\n            coords = {\n                x: reference.x,\n                y: reference.y\n            };\n    }\n    switch((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)){\n        case \"start\":\n            coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n        case \"end\":\n            coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n    }\n    return coords;\n}\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */ const computePosition = async (reference, floating, config)=>{\n    const { placement = \"bottom\", strategy = \"absolute\", middleware = [], platform } = config;\n    const validMiddleware = middleware.filter(Boolean);\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n    let rects = await platform.getElementRects({\n        reference,\n        floating,\n        strategy\n    });\n    let { x, y } = computeCoordsFromPlacement(rects, placement, rtl);\n    let statefulPlacement = placement;\n    let middlewareData = {};\n    let resetCount = 0;\n    for(let i = 0; i < validMiddleware.length; i++){\n        const { name, fn } = validMiddleware[i];\n        const { x: nextX, y: nextY, data, reset } = await fn({\n            x,\n            y,\n            initialPlacement: placement,\n            placement: statefulPlacement,\n            strategy,\n            middlewareData,\n            rects,\n            platform,\n            elements: {\n                reference,\n                floating\n            }\n        });\n        x = nextX != null ? nextX : x;\n        y = nextY != null ? nextY : y;\n        middlewareData = {\n            ...middlewareData,\n            [name]: {\n                ...middlewareData[name],\n                ...data\n            }\n        };\n        if (reset && resetCount <= 50) {\n            resetCount++;\n            if (typeof reset === \"object\") {\n                if (reset.placement) {\n                    statefulPlacement = reset.placement;\n                }\n                if (reset.rects) {\n                    rects = reset.rects === true ? await platform.getElementRects({\n                        reference,\n                        floating,\n                        strategy\n                    }) : reset.rects;\n                }\n                ({ x, y } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n            }\n            i = -1;\n        }\n    }\n    return {\n        x,\n        y,\n        placement: statefulPlacement,\n        strategy,\n        middlewareData\n    };\n};\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ async function detectOverflow(state, options) {\n    var _await$platform$isEle;\n    if (options === void 0) {\n        options = {};\n    }\n    const { x, y, platform, rects, elements, strategy } = state;\n    const { boundary = \"clippingAncestors\", rootBoundary = \"viewport\", elementContext = \"floating\", altBoundary = false, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const altContext = elementContext === \"floating\" ? \"reference\" : \"floating\";\n    const element = elements[altBoundary ? altContext : elementContext];\n    const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n        element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating)),\n        boundary,\n        rootBoundary,\n        strategy\n    }));\n    const rect = elementContext === \"floating\" ? {\n        x,\n        y,\n        width: rects.floating.width,\n        height: rects.floating.height\n    } : rects.reference;\n    const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n    const offsetScale = await (platform.isElement == null ? void 0 : platform.isElement(offsetParent)) ? await (platform.getScale == null ? void 0 : platform.getScale(offsetParent)) || {\n        x: 1,\n        y: 1\n    } : {\n        x: 1,\n        y: 1\n    };\n    const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n        elements,\n        rect,\n        offsetParent,\n        strategy\n    }) : rect);\n    return {\n        top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n        bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n        left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n        right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n    };\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = (options)=>({\n        name: \"arrow\",\n        options,\n        async fn (state) {\n            const { x, y, placement, rects, platform, elements, middlewareData } = state;\n            // Since `element` is required, we don't Partial<> the type.\n            const { element, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n            if (element == null) {\n                return {};\n            }\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            const coords = {\n                x,\n                y\n            };\n            const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n            const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n            const arrowDimensions = await platform.getDimensions(element);\n            const isYAxis = axis === \"y\";\n            const minProp = isYAxis ? \"top\" : \"left\";\n            const maxProp = isYAxis ? \"bottom\" : \"right\";\n            const clientProp = isYAxis ? \"clientHeight\" : \"clientWidth\";\n            const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n            const startDiff = coords[axis] - rects.reference[axis];\n            const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n            let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n            // DOM platform can return `window` as the `offsetParent`.\n            if (!clientSize || !await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent))) {\n                clientSize = elements.floating[clientProp] || rects.floating[length];\n            }\n            const centerToReference = endDiff / 2 - startDiff / 2;\n            // If the padding is large enough that it causes the arrow to no longer be\n            // centered, modify the padding so that it is centered.\n            const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n            const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n            const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n            // Make sure the arrow doesn't overflow the floating element if the center\n            // point is outside the floating element's bounds.\n            const min$1 = minPadding;\n            const max = clientSize - arrowDimensions[length] - maxPadding;\n            const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n            const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n            // If the reference is small enough that the arrow's padding causes it to\n            // to point to nothing for an aligned placement, adjust the offset of the\n            // floating element itself. To ensure `shift()` continues to take action,\n            // a single reset is performed when this is true.\n            const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n            const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n            return {\n                [axis]: coords[axis] + alignmentOffset,\n                data: {\n                    [axis]: offset,\n                    centerOffset: center - offset - alignmentOffset,\n                    ...shouldAddOffset && {\n                        alignmentOffset\n                    }\n                },\n                reset: shouldAddOffset\n            };\n        }\n    });\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n    const allowedPlacementsSortedByAlignment = alignment ? [\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment),\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)\n    ] : allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n    return allowedPlacementsSortedByAlignment.filter((placement)=>{\n        if (alignment) {\n            return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n        }\n        return true;\n    });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"autoPlacement\",\n        options,\n        async fn (state) {\n            var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n            const { rects, middlewareData, placement, platform, elements } = state;\n            const { crossAxis = false, alignment, allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements, autoAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n            const currentPlacement = placements$1[currentIndex];\n            if (currentPlacement == null) {\n                return {};\n            }\n            const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n            // Make `computeCoords` start from the right place.\n            if (placement !== currentPlacement) {\n                return {\n                    reset: {\n                        placement: placements$1[0]\n                    }\n                };\n            }\n            const currentOverflows = [\n                overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)],\n                overflow[alignmentSides[0]],\n                overflow[alignmentSides[1]]\n            ];\n            const allOverflows = [\n                ...((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || [],\n                {\n                    placement: currentPlacement,\n                    overflows: currentOverflows\n                }\n            ];\n            const nextPlacement = placements$1[currentIndex + 1];\n            // There are more placements to check.\n            if (nextPlacement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: nextPlacement\n                    }\n                };\n            }\n            const placementsSortedByMostSpace = allOverflows.map((d)=>{\n                const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n                return [\n                    d.placement,\n                    alignment && crossAxis ? // Check along the mainAxis and main crossAxis side.\n                    d.overflows.slice(0, 2).reduce((acc, v)=>acc + v, 0) : // Check only the mainAxis.\n                    d.overflows[0],\n                    d.overflows\n                ];\n            }).sort((a, b)=>a[1] - b[1]);\n            const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter((d)=>d[2].slice(0, // Aligned placements should not check their opposite crossAxis\n                // side.\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every((v)=>v <= 0));\n            const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n            if (resetPlacement !== placement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: resetPlacement\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"flip\",\n        options,\n        async fn (state) {\n            var _middlewareData$arrow, _middlewareData$flip;\n            const { placement, middlewareData, rects, initialPlacement, platform, elements } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true, fallbackPlacements: specifiedFallbackPlacements, fallbackStrategy = \"bestFit\", fallbackAxisSideDirection = \"none\", flipAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            // If a reset by the arrow was caused due to an alignment offset being\n            // added, we should skip any logic now since `flip()` has already done its\n            // work.\n            // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n            if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n            const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n            const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n            const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)\n            ] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n            const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== \"none\";\n            if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n                fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n            }\n            const placements = [\n                initialPlacement,\n                ...fallbackPlacements\n            ];\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const overflows = [];\n            let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n            if (checkMainAxis) {\n                overflows.push(overflow[side]);\n            }\n            if (checkCrossAxis) {\n                const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n                overflows.push(overflow[sides[0]], overflow[sides[1]]);\n            }\n            overflowsData = [\n                ...overflowsData,\n                {\n                    placement,\n                    overflows\n                }\n            ];\n            // One or more sides is overflowing.\n            if (!overflows.every((side)=>side <= 0)) {\n                var _middlewareData$flip2, _overflowsData$filter;\n                const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n                const nextPlacement = placements[nextIndex];\n                if (nextPlacement) {\n                    const ignoreCrossAxisOverflow = checkCrossAxis === \"alignment\" ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n                    if (!ignoreCrossAxisOverflow || // We leave the current main axis only if every placement on that axis\n                    // overflows the main axis.\n                    overflowsData.every((d)=>d.overflows[0] > 0 && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement) === initialSideAxis)) {\n                        // Try next placement and re-run the lifecycle.\n                        return {\n                            data: {\n                                index: nextIndex,\n                                overflows: overflowsData\n                            },\n                            reset: {\n                                placement: nextPlacement\n                            }\n                        };\n                    }\n                }\n                // First, find the candidates that fit on the mainAxis side of overflow,\n                // then find the placement that fits the best on the main crossAxis side.\n                let resetPlacement = (_overflowsData$filter = overflowsData.filter((d)=>d.overflows[0] <= 0).sort((a, b)=>a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n                // Otherwise fallback.\n                if (!resetPlacement) {\n                    switch(fallbackStrategy){\n                        case \"bestFit\":\n                            {\n                                var _overflowsData$filter2;\n                                const placement = (_overflowsData$filter2 = overflowsData.filter((d)=>{\n                                    if (hasFallbackAxisSideDirection) {\n                                        const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                                        return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal\n                                        // reading directions favoring greater width.\n                                        currentSideAxis === \"y\";\n                                    }\n                                    return true;\n                                }).map((d)=>[\n                                        d.placement,\n                                        d.overflows.filter((overflow)=>overflow > 0).reduce((acc, overflow)=>acc + overflow, 0)\n                                    ]).sort((a, b)=>a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                                if (placement) {\n                                    resetPlacement = placement;\n                                }\n                                break;\n                            }\n                        case \"initialPlacement\":\n                            resetPlacement = initialPlacement;\n                            break;\n                    }\n                }\n                if (placement !== resetPlacement) {\n                    return {\n                        reset: {\n                            placement: resetPlacement\n                        }\n                    };\n                }\n            }\n            return {};\n        }\n    };\n};\nfunction getSideOffsets(overflow, rect) {\n    return {\n        top: overflow.top - rect.height,\n        right: overflow.right - rect.width,\n        bottom: overflow.bottom - rect.height,\n        left: overflow.left - rect.width\n    };\n}\nfunction isAnySideFullyClipped(overflow) {\n    return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some((side)=>overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"hide\",\n        options,\n        async fn (state) {\n            const { rects } = state;\n            const { strategy = \"referenceHidden\", ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            switch(strategy){\n                case \"referenceHidden\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            elementContext: \"reference\"\n                        });\n                        const offsets = getSideOffsets(overflow, rects.reference);\n                        return {\n                            data: {\n                                referenceHiddenOffsets: offsets,\n                                referenceHidden: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                case \"escaped\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            altBoundary: true\n                        });\n                        const offsets = getSideOffsets(overflow, rects.floating);\n                        return {\n                            data: {\n                                escapedOffsets: offsets,\n                                escaped: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                default:\n                    {\n                        return {};\n                    }\n            }\n        }\n    };\n};\nfunction getBoundingRect(rects) {\n    const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.left));\n    const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.top));\n    const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.right));\n    const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.bottom));\n    return {\n        x: minX,\n        y: minY,\n        width: maxX - minX,\n        height: maxY - minY\n    };\n}\nfunction getRectsByLine(rects) {\n    const sortedRects = rects.slice().sort((a, b)=>a.y - b.y);\n    const groups = [];\n    let prevRect = null;\n    for(let i = 0; i < sortedRects.length; i++){\n        const rect = sortedRects[i];\n        if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n            groups.push([\n                rect\n            ]);\n        } else {\n            groups[groups.length - 1].push(rect);\n        }\n        prevRect = rect;\n    }\n    return groups.map((rect)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"inline\",\n        options,\n        async fn (state) {\n            const { placement, elements, rects, platform, strategy } = state;\n            // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n            // ClientRect's bounds, despite the event listener being triggered. A\n            // padding of 2 seems to handle this issue.\n            const { padding = 2, x, y } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const nativeClientRects = Array.from(await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference)) || []);\n            const clientRects = getRectsByLine(nativeClientRects);\n            const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            function getBoundingClientRect() {\n                // There are two rects and they are disjoined.\n                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n                    // Find the first rect in which the point is fully inside.\n                    return clientRects.find((rect)=>x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n                }\n                // There are 2 or more connected rects.\n                if (clientRects.length >= 2) {\n                    if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\") {\n                        const firstRect = clientRects[0];\n                        const lastRect = clientRects[clientRects.length - 1];\n                        const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"top\";\n                        const top = firstRect.top;\n                        const bottom = lastRect.bottom;\n                        const left = isTop ? firstRect.left : lastRect.left;\n                        const right = isTop ? firstRect.right : lastRect.right;\n                        const width = right - left;\n                        const height = bottom - top;\n                        return {\n                            top,\n                            bottom,\n                            left,\n                            right,\n                            width,\n                            height,\n                            x: left,\n                            y: top\n                        };\n                    }\n                    const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"left\";\n                    const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map((rect)=>rect.right));\n                    const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map((rect)=>rect.left));\n                    const measureRects = clientRects.filter((rect)=>isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n                    const top = measureRects[0].top;\n                    const bottom = measureRects[measureRects.length - 1].bottom;\n                    const left = minLeft;\n                    const right = maxRight;\n                    const width = right - left;\n                    const height = bottom - top;\n                    return {\n                        top,\n                        bottom,\n                        left,\n                        right,\n                        width,\n                        height,\n                        x: left,\n                        y: top\n                    };\n                }\n                return fallback;\n            }\n            const resetRects = await platform.getElementRects({\n                reference: {\n                    getBoundingClientRect\n                },\n                floating: elements.floating,\n                strategy\n            });\n            if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n                return {\n                    reset: {\n                        rects: resetRects\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\nconst originSides = /*#__PURE__*/ new Set([\n    \"left\",\n    \"top\"\n]);\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\nasync function convertValueToCoords(state, options) {\n    const { placement, platform, elements } = state;\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n    const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n    const mainAxisMulti = originSides.has(side) ? -1 : 1;\n    const crossAxisMulti = rtl && isVertical ? -1 : 1;\n    const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    // eslint-disable-next-line prefer-const\n    let { mainAxis, crossAxis, alignmentAxis } = typeof rawValue === \"number\" ? {\n        mainAxis: rawValue,\n        crossAxis: 0,\n        alignmentAxis: null\n    } : {\n        mainAxis: rawValue.mainAxis || 0,\n        crossAxis: rawValue.crossAxis || 0,\n        alignmentAxis: rawValue.alignmentAxis\n    };\n    if (alignment && typeof alignmentAxis === \"number\") {\n        crossAxis = alignment === \"end\" ? alignmentAxis * -1 : alignmentAxis;\n    }\n    return isVertical ? {\n        x: crossAxis * crossAxisMulti,\n        y: mainAxis * mainAxisMulti\n    } : {\n        x: mainAxis * mainAxisMulti,\n        y: crossAxis * crossAxisMulti\n    };\n}\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = function(options) {\n    if (options === void 0) {\n        options = 0;\n    }\n    return {\n        name: \"offset\",\n        options,\n        async fn (state) {\n            var _middlewareData$offse, _middlewareData$arrow;\n            const { x, y, placement, middlewareData } = state;\n            const diffCoords = await convertValueToCoords(state, options);\n            // If the placement is the same and the arrow caused an alignment offset\n            // then we don't need to change the positioning coordinates.\n            if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            return {\n                x: x + diffCoords.x,\n                y: y + diffCoords.y,\n                data: {\n                    ...diffCoords,\n                    placement\n                }\n            };\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"shift\",\n        options,\n        async fn (state) {\n            const { x, y, placement } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = false, limiter = {\n                fn: (_ref)=>{\n                    let { x, y } = _ref;\n                    return {\n                        x,\n                        y\n                    };\n                }\n            }, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            if (checkMainAxis) {\n                const minSide = mainAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = mainAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = mainAxisCoord + overflow[minSide];\n                const max = mainAxisCoord - overflow[maxSide];\n                mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n            }\n            if (checkCrossAxis) {\n                const minSide = crossAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = crossAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = crossAxisCoord + overflow[minSide];\n                const max = crossAxisCoord - overflow[maxSide];\n                crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n            }\n            const limitedCoords = limiter.fn({\n                ...state,\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            });\n            return {\n                ...limitedCoords,\n                data: {\n                    x: limitedCoords.x - x,\n                    y: limitedCoords.y - y,\n                    enabled: {\n                        [mainAxis]: checkMainAxis,\n                        [crossAxis]: checkCrossAxis\n                    }\n                }\n            };\n        }\n    };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        options,\n        fn (state) {\n            const { x, y, placement, rects, middlewareData } = state;\n            const { offset = 0, mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n            const computedOffset = typeof rawOffset === \"number\" ? {\n                mainAxis: rawOffset,\n                crossAxis: 0\n            } : {\n                mainAxis: 0,\n                crossAxis: 0,\n                ...rawOffset\n            };\n            if (checkMainAxis) {\n                const len = mainAxis === \"y\" ? \"height\" : \"width\";\n                const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n                const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n                if (mainAxisCoord < limitMin) {\n                    mainAxisCoord = limitMin;\n                } else if (mainAxisCoord > limitMax) {\n                    mainAxisCoord = limitMax;\n                }\n            }\n            if (checkCrossAxis) {\n                var _middlewareData$offse, _middlewareData$offse2;\n                const len = mainAxis === \"y\" ? \"width\" : \"height\";\n                const isOriginSide = originSides.has((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n                const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n                const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n                if (crossAxisCoord < limitMin) {\n                    crossAxisCoord = limitMin;\n                } else if (crossAxisCoord > limitMax) {\n                    crossAxisCoord = limitMax;\n                }\n            }\n            return {\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            };\n        }\n    };\n};\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"size\",\n        options,\n        async fn (state) {\n            var _state$middlewareData, _state$middlewareData2;\n            const { placement, rects, platform, elements } = state;\n            const { apply = ()=>{}, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n            const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n            const { width, height } = rects.floating;\n            let heightSide;\n            let widthSide;\n            if (side === \"top\" || side === \"bottom\") {\n                heightSide = side;\n                widthSide = alignment === (await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)) ? \"start\" : \"end\") ? \"left\" : \"right\";\n            } else {\n                widthSide = side;\n                heightSide = alignment === \"end\" ? \"top\" : \"bottom\";\n            }\n            const maximumClippingHeight = height - overflow.top - overflow.bottom;\n            const maximumClippingWidth = width - overflow.left - overflow.right;\n            const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n            const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n            const noShift = !state.middlewareData.shift;\n            let availableHeight = overflowAvailableHeight;\n            let availableWidth = overflowAvailableWidth;\n            if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n                availableWidth = maximumClippingWidth;\n            }\n            if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n                availableHeight = maximumClippingHeight;\n            }\n            if (noShift && !alignment) {\n                const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n                const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n                const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n                const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n                if (isYAxis) {\n                    availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n                } else {\n                    availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n                }\n            }\n            await apply({\n                ...state,\n                availableWidth,\n                availableHeight\n            });\n            const nextDimensions = await platform.getDimensions(elements.floating);\n            if (width !== nextDimensions.width || height !== nextDimensions.height) {\n                return {\n                    reset: {\n                        rects: true\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@floating-ui+core@1.7.2/node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ })

};
;