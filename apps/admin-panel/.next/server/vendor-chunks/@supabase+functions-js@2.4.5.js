"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+functions-js@2.4.5";
exports.ids = ["vendor-chunks/@supabase+functions-js@2.4.5"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionsClient: () => (/* binding */ FunctionsClient)\n/* harmony export */ });\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helper */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\");\nvar __awaiter = undefined && undefined.__awaiter || function(thisArg, _arguments, P, generator) {\n    function adopt(value) {\n        return value instanceof P ? value : new P(function(resolve) {\n            resolve(value);\n        });\n    }\n    return new (P || (P = Promise))(function(resolve, reject) {\n        function fulfilled(value) {\n            try {\n                step(generator.next(value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function rejected(value) {\n            try {\n                step(generator[\"throw\"](value));\n            } catch (e) {\n                reject(e);\n            }\n        }\n        function step(result) {\n            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n        }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nclass FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = _types__WEBPACK_IMPORTED_MODULE_0__.FunctionRegion.Any } = {}){\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = (0,_helper__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */ setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */ invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function*() {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                // Add region as query parameter using URL API\n                const url = new URL(`${this.url}/${functionName}`);\n                if (region && region !== \"any\") {\n                    _headers[\"x-region\"] = region;\n                    url.searchParams.set(\"forceFunctionRegion\", region);\n                }\n                let body;\n                if (functionArgs && (headers && !Object.prototype.hasOwnProperty.call(headers, \"Content-Type\") || !headers)) {\n                    if (typeof Blob !== \"undefined\" && functionArgs instanceof Blob || functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers[\"Content-Type\"] = \"application/octet-stream\";\n                        body = functionArgs;\n                    } else if (typeof functionArgs === \"string\") {\n                        // plain string\n                        _headers[\"Content-Type\"] = \"text/plain\";\n                        body = functionArgs;\n                    } else if (typeof FormData !== \"undefined\" && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    } else {\n                        // default, assume this is JSON\n                        _headers[\"Content-Type\"] = \"application/json\";\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(url.toString(), {\n                    method: method || \"POST\",\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body\n                }).catch((fetchError)=>{\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get(\"x-relay-error\");\n                if (isRelayError && isRelayError === \"true\") {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get(\"Content-Type\")) !== null && _a !== void 0 ? _a : \"text/plain\").split(\";\")[0].trim();\n                let data;\n                if (responseType === \"application/json\") {\n                    data = yield response.json();\n                } else if (responseType === \"application/octet-stream\") {\n                    data = yield response.blob();\n                } else if (responseType === \"text/event-stream\") {\n                    data = response;\n                } else if (responseType === \"multipart/form-data\") {\n                    data = yield response.formData();\n                } else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return {\n                    data,\n                    error: null,\n                    response\n                };\n            } catch (error) {\n                return {\n                    data: null,\n                    error,\n                    response: error instanceof _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError || error instanceof _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError ? error.context : undefined\n                };\n            }\n        });\n    }\n} //# sourceMappingURL=FunctionsClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch)\n/* harmony export */ });\nconst resolveFetch = (customFetch)=>{\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    } else if (typeof fetch === \"undefined\") {\n        _fetch = (...args)=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch1 })=>fetch1(...args));\n    } else {\n        _fetch = fetch;\n    }\n    return (...args)=>_fetch(...args);\n}; //# sourceMappingURL=helper.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjUvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9mdW5jdGlvbnMtanMvZGlzdC9tb2R1bGUvaGVscGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxlQUFlLENBQUNDO0lBQ3pCLElBQUlDO0lBQ0osSUFBSUQsYUFBYTtRQUNiQyxTQUFTRDtJQUNiLE9BQ0ssSUFBSSxPQUFPRSxVQUFVLGFBQWE7UUFDbkNELFNBQVMsQ0FBQyxHQUFHRSxPQUFTLG1PQUFPLENBQXdCQyxJQUFJLENBQUMsQ0FBQyxFQUFFQyxTQUFTSCxNQUFLLEVBQUUsR0FBS0EsVUFBU0M7SUFDL0YsT0FDSztRQUNERixTQUFTQztJQUNiO0lBQ0EsT0FBTyxDQUFDLEdBQUdDLE9BQVNGLFVBQVVFO0FBQ2xDLEVBQUUsQ0FDRixrQ0FBa0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjUvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9mdW5jdGlvbnMtanMvZGlzdC9tb2R1bGUvaGVscGVyLmpzPzU4MTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHJlc29sdmVGZXRjaCA9IChjdXN0b21GZXRjaCkgPT4ge1xuICAgIGxldCBfZmV0Y2g7XG4gICAgaWYgKGN1c3RvbUZldGNoKSB7XG4gICAgICAgIF9mZXRjaCA9IGN1c3RvbUZldGNoO1xuICAgIH1cbiAgICBlbHNlIGlmICh0eXBlb2YgZmV0Y2ggPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIF9mZXRjaCA9ICguLi5hcmdzKSA9PiBpbXBvcnQoJ0BzdXBhYmFzZS9ub2RlLWZldGNoJykudGhlbigoeyBkZWZhdWx0OiBmZXRjaCB9KSA9PiBmZXRjaCguLi5hcmdzKSk7XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICBfZmV0Y2ggPSBmZXRjaDtcbiAgICB9XG4gICAgcmV0dXJuICguLi5hcmdzKSA9PiBfZmV0Y2goLi4uYXJncyk7XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aGVscGVyLmpzLm1hcCJdLCJuYW1lcyI6WyJyZXNvbHZlRmV0Y2giLCJjdXN0b21GZXRjaCIsIl9mZXRjaCIsImZldGNoIiwiYXJncyIsInRoZW4iLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/helper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionRegion: () => (/* binding */ FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* binding */ FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* binding */ FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* binding */ FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* binding */ FunctionsRelayError)\n/* harmony export */ });\nclass FunctionsError extends Error {\n    constructor(message, name = \"FunctionsError\", context){\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nclass FunctionsFetchError extends FunctionsError {\n    constructor(context){\n        super(\"Failed to send a request to the Edge Function\", \"FunctionsFetchError\", context);\n    }\n}\nclass FunctionsRelayError extends FunctionsError {\n    constructor(context){\n        super(\"Relay Error invoking the Edge Function\", \"FunctionsRelayError\", context);\n    }\n}\nclass FunctionsHttpError extends FunctionsError {\n    constructor(context){\n        super(\"Edge Function returned a non-2xx status code\", \"FunctionsHttpError\", context);\n    }\n}\n// Define the enum for the 'region' property\nvar FunctionRegion;\n(function(FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {})); //# sourceMappingURL=types.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjUvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9mdW5jdGlvbnMtanMvZGlzdC9tb2R1bGUvdHlwZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTyxNQUFNQSx1QkFBdUJDO0lBQ2hDQyxZQUFZQyxPQUFPLEVBQUVDLE9BQU8sZ0JBQWdCLEVBQUVDLE9BQU8sQ0FBRTtRQUNuRCxLQUFLLENBQUNGO1FBQ04sSUFBSSxDQUFDQyxJQUFJLEdBQUdBO1FBQ1osSUFBSSxDQUFDQyxPQUFPLEdBQUdBO0lBQ25CO0FBQ0o7QUFDTyxNQUFNQyw0QkFBNEJOO0lBQ3JDRSxZQUFZRyxPQUFPLENBQUU7UUFDakIsS0FBSyxDQUFDLGlEQUFpRCx1QkFBdUJBO0lBQ2xGO0FBQ0o7QUFDTyxNQUFNRSw0QkFBNEJQO0lBQ3JDRSxZQUFZRyxPQUFPLENBQUU7UUFDakIsS0FBSyxDQUFDLDBDQUEwQyx1QkFBdUJBO0lBQzNFO0FBQ0o7QUFDTyxNQUFNRywyQkFBMkJSO0lBQ3BDRSxZQUFZRyxPQUFPLENBQUU7UUFDakIsS0FBSyxDQUFDLGdEQUFnRCxzQkFBc0JBO0lBQ2hGO0FBQ0o7QUFDQSw0Q0FBNEM7QUFDckMsSUFBSUksZUFBZTtBQUN6QixVQUFVQSxjQUFjO0lBQ3JCQSxjQUFjLENBQUMsTUFBTSxHQUFHO0lBQ3hCQSxjQUFjLENBQUMsZUFBZSxHQUFHO0lBQ2pDQSxjQUFjLENBQUMsZUFBZSxHQUFHO0lBQ2pDQSxjQUFjLENBQUMsV0FBVyxHQUFHO0lBQzdCQSxjQUFjLENBQUMsZUFBZSxHQUFHO0lBQ2pDQSxjQUFjLENBQUMsZUFBZSxHQUFHO0lBQ2pDQSxjQUFjLENBQUMsYUFBYSxHQUFHO0lBQy9CQSxjQUFjLENBQUMsYUFBYSxHQUFHO0lBQy9CQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0lBQzVCQSxjQUFjLENBQUMsVUFBVSxHQUFHO0FBQ2hDLEdBQUdBLGtCQUFtQkEsQ0FBQUEsaUJBQWlCLENBQUMsS0FDeEMsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2UrZnVuY3Rpb25zLWpzQDIuNC41L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvZnVuY3Rpb25zLWpzL2Rpc3QvbW9kdWxlL3R5cGVzLmpzPzBlNGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEZ1bmN0aW9uc0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKG1lc3NhZ2UsIG5hbWUgPSAnRnVuY3Rpb25zRXJyb3InLCBjb250ZXh0KSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICB0aGlzLm5hbWUgPSBuYW1lO1xuICAgICAgICB0aGlzLmNvbnRleHQgPSBjb250ZXh0O1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBGdW5jdGlvbnNGZXRjaEVycm9yIGV4dGVuZHMgRnVuY3Rpb25zRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKGNvbnRleHQpIHtcbiAgICAgICAgc3VwZXIoJ0ZhaWxlZCB0byBzZW5kIGEgcmVxdWVzdCB0byB0aGUgRWRnZSBGdW5jdGlvbicsICdGdW5jdGlvbnNGZXRjaEVycm9yJywgY29udGV4dCk7XG4gICAgfVxufVxuZXhwb3J0IGNsYXNzIEZ1bmN0aW9uc1JlbGF5RXJyb3IgZXh0ZW5kcyBGdW5jdGlvbnNFcnJvciB7XG4gICAgY29uc3RydWN0b3IoY29udGV4dCkge1xuICAgICAgICBzdXBlcignUmVsYXkgRXJyb3IgaW52b2tpbmcgdGhlIEVkZ2UgRnVuY3Rpb24nLCAnRnVuY3Rpb25zUmVsYXlFcnJvcicsIGNvbnRleHQpO1xuICAgIH1cbn1cbmV4cG9ydCBjbGFzcyBGdW5jdGlvbnNIdHRwRXJyb3IgZXh0ZW5kcyBGdW5jdGlvbnNFcnJvciB7XG4gICAgY29uc3RydWN0b3IoY29udGV4dCkge1xuICAgICAgICBzdXBlcignRWRnZSBGdW5jdGlvbiByZXR1cm5lZCBhIG5vbi0yeHggc3RhdHVzIGNvZGUnLCAnRnVuY3Rpb25zSHR0cEVycm9yJywgY29udGV4dCk7XG4gICAgfVxufVxuLy8gRGVmaW5lIHRoZSBlbnVtIGZvciB0aGUgJ3JlZ2lvbicgcHJvcGVydHlcbmV4cG9ydCB2YXIgRnVuY3Rpb25SZWdpb247XG4oZnVuY3Rpb24gKEZ1bmN0aW9uUmVnaW9uKSB7XG4gICAgRnVuY3Rpb25SZWdpb25bXCJBbnlcIl0gPSBcImFueVwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiQXBOb3J0aGVhc3QxXCJdID0gXCJhcC1ub3J0aGVhc3QtMVwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiQXBOb3J0aGVhc3QyXCJdID0gXCJhcC1ub3J0aGVhc3QtMlwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiQXBTb3V0aDFcIl0gPSBcImFwLXNvdXRoLTFcIjtcbiAgICBGdW5jdGlvblJlZ2lvbltcIkFwU291dGhlYXN0MVwiXSA9IFwiYXAtc291dGhlYXN0LTFcIjtcbiAgICBGdW5jdGlvblJlZ2lvbltcIkFwU291dGhlYXN0MlwiXSA9IFwiYXAtc291dGhlYXN0LTJcIjtcbiAgICBGdW5jdGlvblJlZ2lvbltcIkNhQ2VudHJhbDFcIl0gPSBcImNhLWNlbnRyYWwtMVwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiRXVDZW50cmFsMVwiXSA9IFwiZXUtY2VudHJhbC0xXCI7XG4gICAgRnVuY3Rpb25SZWdpb25bXCJFdVdlc3QxXCJdID0gXCJldS13ZXN0LTFcIjtcbiAgICBGdW5jdGlvblJlZ2lvbltcIkV1V2VzdDJcIl0gPSBcImV1LXdlc3QtMlwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiRXVXZXN0M1wiXSA9IFwiZXUtd2VzdC0zXCI7XG4gICAgRnVuY3Rpb25SZWdpb25bXCJTYUVhc3QxXCJdID0gXCJzYS1lYXN0LTFcIjtcbiAgICBGdW5jdGlvblJlZ2lvbltcIlVzRWFzdDFcIl0gPSBcInVzLWVhc3QtMVwiO1xuICAgIEZ1bmN0aW9uUmVnaW9uW1wiVXNXZXN0MVwiXSA9IFwidXMtd2VzdC0xXCI7XG4gICAgRnVuY3Rpb25SZWdpb25bXCJVc1dlc3QyXCJdID0gXCJ1cy13ZXN0LTJcIjtcbn0pKEZ1bmN0aW9uUmVnaW9uIHx8IChGdW5jdGlvblJlZ2lvbiA9IHt9KSk7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOlsiRnVuY3Rpb25zRXJyb3IiLCJFcnJvciIsImNvbnN0cnVjdG9yIiwibWVzc2FnZSIsIm5hbWUiLCJjb250ZXh0IiwiRnVuY3Rpb25zRmV0Y2hFcnJvciIsIkZ1bmN0aW9uc1JlbGF5RXJyb3IiLCJGdW5jdGlvbnNIdHRwRXJyb3IiLCJGdW5jdGlvblJlZ2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.5/node_modules/@supabase/functions-js/dist/module/types.js\n");

/***/ })

};
;