"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/whatwg-url@5.0.0";
exports.ids = ["vendor-chunks/whatwg-url@5.0.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL-impl.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL-impl.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nconst usm = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\");\nexports.implementation = class URLImpl {\n    constructor(constructorArgs){\n        const url = constructorArgs[0];\n        const base = constructorArgs[1];\n        let parsedBase = null;\n        if (base !== undefined) {\n            parsedBase = usm.basicURLParse(base);\n            if (parsedBase === \"failure\") {\n                throw new TypeError(\"Invalid base URL\");\n            }\n        }\n        const parsedURL = usm.basicURLParse(url, {\n            baseURL: parsedBase\n        });\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    // TODO: query stuff\n    }\n    get href() {\n        return usm.serializeURL(this._url);\n    }\n    set href(v) {\n        const parsedURL = usm.basicURLParse(v);\n        if (parsedURL === \"failure\") {\n            throw new TypeError(\"Invalid URL\");\n        }\n        this._url = parsedURL;\n    }\n    get origin() {\n        return usm.serializeURLOrigin(this._url);\n    }\n    get protocol() {\n        return this._url.scheme + \":\";\n    }\n    set protocol(v) {\n        usm.basicURLParse(v + \":\", {\n            url: this._url,\n            stateOverride: \"scheme start\"\n        });\n    }\n    get username() {\n        return this._url.username;\n    }\n    set username(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setTheUsername(this._url, v);\n    }\n    get password() {\n        return this._url.password;\n    }\n    set password(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        usm.setThePassword(this._url, v);\n    }\n    get host() {\n        const url = this._url;\n        if (url.host === null) {\n            return \"\";\n        }\n        if (url.port === null) {\n            return usm.serializeHost(url.host);\n        }\n        return usm.serializeHost(url.host) + \":\" + usm.serializeInteger(url.port);\n    }\n    set host(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"host\"\n        });\n    }\n    get hostname() {\n        if (this._url.host === null) {\n            return \"\";\n        }\n        return usm.serializeHost(this._url.host);\n    }\n    set hostname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"hostname\"\n        });\n    }\n    get port() {\n        if (this._url.port === null) {\n            return \"\";\n        }\n        return usm.serializeInteger(this._url.port);\n    }\n    set port(v) {\n        if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n            return;\n        }\n        if (v === \"\") {\n            this._url.port = null;\n        } else {\n            usm.basicURLParse(v, {\n                url: this._url,\n                stateOverride: \"port\"\n            });\n        }\n    }\n    get pathname() {\n        if (this._url.cannotBeABaseURL) {\n            return this._url.path[0];\n        }\n        if (this._url.path.length === 0) {\n            return \"\";\n        }\n        return \"/\" + this._url.path.join(\"/\");\n    }\n    set pathname(v) {\n        if (this._url.cannotBeABaseURL) {\n            return;\n        }\n        this._url.path = [];\n        usm.basicURLParse(v, {\n            url: this._url,\n            stateOverride: \"path start\"\n        });\n    }\n    get search() {\n        if (this._url.query === null || this._url.query === \"\") {\n            return \"\";\n        }\n        return \"?\" + this._url.query;\n    }\n    set search(v) {\n        // TODO: query stuff\n        const url = this._url;\n        if (v === \"\") {\n            url.query = null;\n            return;\n        }\n        const input = v[0] === \"?\" ? v.substring(1) : v;\n        url.query = \"\";\n        usm.basicURLParse(input, {\n            url,\n            stateOverride: \"query\"\n        });\n    }\n    get hash() {\n        if (this._url.fragment === null || this._url.fragment === \"\") {\n            return \"\";\n        }\n        return \"#\" + this._url.fragment;\n    }\n    set hash(v) {\n        if (v === \"\") {\n            this._url.fragment = null;\n            return;\n        }\n        const input = v[0] === \"#\" ? v.substring(1) : v;\n        this._url.fragment = \"\";\n        usm.basicURLParse(input, {\n            url: this._url,\n            stateOverride: \"fragment\"\n        });\n    }\n    toJSON() {\n        return this.href;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL-impl.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL.js":
/*!************************************************************************************!*\
  !*** ../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst conversions = __webpack_require__(/*! webidl-conversions */ \"(ssr)/../../node_modules/.pnpm/webidl-conversions@3.0.1/node_modules/webidl-conversions/lib/index.js\");\nconst utils = __webpack_require__(/*! ./utils.js */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/utils.js\");\nconst Impl = __webpack_require__(/*! .//URL-impl.js */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL-impl.js\");\nconst impl = utils.implSymbol;\nfunction URL(url) {\n    if (!this || this[impl] || !(this instanceof URL)) {\n        throw new TypeError(\"Failed to construct 'URL': Please use the 'new' operator, this DOM object constructor cannot be called as a function.\");\n    }\n    if (arguments.length < 1) {\n        throw new TypeError(\"Failed to construct 'URL': 1 argument required, but only \" + arguments.length + \" present.\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 2; ++i){\n        args[i] = arguments[i];\n    }\n    args[0] = conversions[\"USVString\"](args[0]);\n    if (args[1] !== undefined) {\n        args[1] = conversions[\"USVString\"](args[1]);\n    }\n    module.exports.setup(this, args);\n}\nURL.prototype.toJSON = function toJSON() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    const args = [];\n    for(let i = 0; i < arguments.length && i < 0; ++i){\n        args[i] = arguments[i];\n    }\n    return this[impl].toJSON.apply(this[impl], args);\n};\nObject.defineProperty(URL.prototype, \"href\", {\n    get () {\n        return this[impl].href;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].href = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nURL.prototype.toString = function() {\n    if (!this || !module.exports.is(this)) {\n        throw new TypeError(\"Illegal invocation\");\n    }\n    return this.href;\n};\nObject.defineProperty(URL.prototype, \"origin\", {\n    get () {\n        return this[impl].origin;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"protocol\", {\n    get () {\n        return this[impl].protocol;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].protocol = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"username\", {\n    get () {\n        return this[impl].username;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].username = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"password\", {\n    get () {\n        return this[impl].password;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].password = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"host\", {\n    get () {\n        return this[impl].host;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].host = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hostname\", {\n    get () {\n        return this[impl].hostname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hostname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"port\", {\n    get () {\n        return this[impl].port;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].port = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"pathname\", {\n    get () {\n        return this[impl].pathname;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].pathname = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"search\", {\n    get () {\n        return this[impl].search;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].search = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nObject.defineProperty(URL.prototype, \"hash\", {\n    get () {\n        return this[impl].hash;\n    },\n    set (V) {\n        V = conversions[\"USVString\"](V);\n        this[impl].hash = V;\n    },\n    enumerable: true,\n    configurable: true\n});\nmodule.exports = {\n    is (obj) {\n        return !!obj && obj[impl] instanceof Impl.implementation;\n    },\n    create (constructorArgs, privateData) {\n        let obj = Object.create(URL.prototype);\n        this.setup(obj, constructorArgs, privateData);\n        return obj;\n    },\n    setup (obj, constructorArgs, privateData) {\n        if (!privateData) privateData = {};\n        privateData.wrapper = obj;\n        obj[impl] = new Impl.implementation(constructorArgs, privateData);\n        obj[impl][utils.wrapperSymbol] = obj;\n    },\n    interface: URL,\n    expose: {\n        Window: {\n            URL: URL\n        },\n        Worker: {\n            URL: URL\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/public-api.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/public-api.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.URL = __webpack_require__(/*! ./URL */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/URL.js\")[\"interface\"];\nexports.serializeURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").serializeURL;\nexports.serializeURLOrigin = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").serializeURLOrigin;\nexports.basicURLParse = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").basicURLParse;\nexports.setTheUsername = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").setTheUsername;\nexports.setThePassword = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").setThePassword;\nexports.serializeHost = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").serializeHost;\nexports.serializeInteger = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").serializeInteger;\nexports.parseURL = __webpack_require__(/*! ./url-state-machine */ \"(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\").parseURL;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3doYXR3Zy11cmxANS4wLjAvbm9kZV9tb2R1bGVzL3doYXR3Zy11cmwvbGliL3B1YmxpYy1hcGkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFFYkEsaUpBQXdDO0FBQ3hDQSxzTEFBa0U7QUFDbEVBLGtNQUE4RTtBQUM5RUEsd0xBQW9FO0FBQ3BFQSwwTEFBc0U7QUFDdEVBLDBMQUFzRTtBQUN0RUEsd0xBQW9FO0FBQ3BFQSw4TEFBMEU7QUFDMUVBLDhLQUEwRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vd2hhdHdnLXVybEA1LjAuMC9ub2RlX21vZHVsZXMvd2hhdHdnLXVybC9saWIvcHVibGljLWFwaS5qcz83Y2QwIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLlVSTCA9IHJlcXVpcmUoXCIuL1VSTFwiKS5pbnRlcmZhY2U7XG5leHBvcnRzLnNlcmlhbGl6ZVVSTCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZVVSTDtcbmV4cG9ydHMuc2VyaWFsaXplVVJMT3JpZ2luID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuc2VyaWFsaXplVVJMT3JpZ2luO1xuZXhwb3J0cy5iYXNpY1VSTFBhcnNlID0gcmVxdWlyZShcIi4vdXJsLXN0YXRlLW1hY2hpbmVcIikuYmFzaWNVUkxQYXJzZTtcbmV4cG9ydHMuc2V0VGhlVXNlcm5hbWUgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXRUaGVVc2VybmFtZTtcbmV4cG9ydHMuc2V0VGhlUGFzc3dvcmQgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXRUaGVQYXNzd29yZDtcbmV4cG9ydHMuc2VyaWFsaXplSG9zdCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnNlcmlhbGl6ZUhvc3Q7XG5leHBvcnRzLnNlcmlhbGl6ZUludGVnZXIgPSByZXF1aXJlKFwiLi91cmwtc3RhdGUtbWFjaGluZVwiKS5zZXJpYWxpemVJbnRlZ2VyO1xuZXhwb3J0cy5wYXJzZVVSTCA9IHJlcXVpcmUoXCIuL3VybC1zdGF0ZS1tYWNoaW5lXCIpLnBhcnNlVVJMO1xuIl0sIm5hbWVzIjpbImV4cG9ydHMiLCJVUkwiLCJyZXF1aXJlIiwiaW50ZXJmYWNlIiwic2VyaWFsaXplVVJMIiwic2VyaWFsaXplVVJMT3JpZ2luIiwiYmFzaWNVUkxQYXJzZSIsInNldFRoZVVzZXJuYW1lIiwic2V0VGhlUGFzc3dvcmQiLCJzZXJpYWxpemVIb3N0Iiwic2VyaWFsaXplSW50ZWdlciIsInBhcnNlVVJMIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/public-api.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js ***!
  \**************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst punycode = __webpack_require__(/*! punycode */ \"punycode\");\nconst tr46 = __webpack_require__(/*! tr46 */ \"(ssr)/../../node_modules/.pnpm/tr46@0.0.3/node_modules/tr46/index.js\");\nconst specialSchemes = {\n    ftp: 21,\n    file: null,\n    gopher: 70,\n    http: 80,\n    https: 443,\n    ws: 80,\n    wss: 443\n};\nconst failure = Symbol(\"failure\");\nfunction countSymbols(str) {\n    return punycode.ucs2.decode(str).length;\n}\nfunction at(input, idx) {\n    const c = input[idx];\n    return isNaN(c) ? undefined : String.fromCodePoint(c);\n}\nfunction isASCIIDigit(c) {\n    return c >= 0x30 && c <= 0x39;\n}\nfunction isASCIIAlpha(c) {\n    return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;\n}\nfunction isASCIIAlphanumeric(c) {\n    return isASCIIAlpha(c) || isASCIIDigit(c);\n}\nfunction isASCIIHex(c) {\n    return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;\n}\nfunction isSingleDot(buffer) {\n    return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\n}\nfunction isDoubleDot(buffer) {\n    buffer = buffer.toLowerCase();\n    return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\n}\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\n    return isASCIIAlpha(cp1) && (cp2 === 58 || cp2 === 124);\n}\nfunction isWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\n}\nfunction isNormalizedWindowsDriveLetterString(string) {\n    return string.length === 2 && isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\n}\nfunction containsForbiddenHostCodePoint(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|%|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction containsForbiddenHostCodePointExcludingPercent(string) {\n    return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|\\?|@|\\[|\\\\|\\]/) !== -1;\n}\nfunction isSpecialScheme(scheme) {\n    return specialSchemes[scheme] !== undefined;\n}\nfunction isSpecial(url) {\n    return isSpecialScheme(url.scheme);\n}\nfunction defaultPort(scheme) {\n    return specialSchemes[scheme];\n}\nfunction percentEncode(c) {\n    let hex = c.toString(16).toUpperCase();\n    if (hex.length === 1) {\n        hex = \"0\" + hex;\n    }\n    return \"%\" + hex;\n}\nfunction utf8PercentEncode(c) {\n    const buf = new Buffer(c);\n    let str = \"\";\n    for(let i = 0; i < buf.length; ++i){\n        str += percentEncode(buf[i]);\n    }\n    return str;\n}\nfunction utf8PercentDecode(str) {\n    const input = new Buffer(str);\n    const output = [];\n    for(let i = 0; i < input.length; ++i){\n        if (input[i] !== 37) {\n            output.push(input[i]);\n        } else if (input[i] === 37 && isASCIIHex(input[i + 1]) && isASCIIHex(input[i + 2])) {\n            output.push(parseInt(input.slice(i + 1, i + 3).toString(), 16));\n            i += 2;\n        } else {\n            output.push(input[i]);\n        }\n    }\n    return new Buffer(output).toString();\n}\nfunction isC0ControlPercentEncode(c) {\n    return c <= 0x1F || c > 0x7E;\n}\nconst extraPathPercentEncodeSet = new Set([\n    32,\n    34,\n    35,\n    60,\n    62,\n    63,\n    96,\n    123,\n    125\n]);\nfunction isPathPercentEncode(c) {\n    return isC0ControlPercentEncode(c) || extraPathPercentEncodeSet.has(c);\n}\nconst extraUserinfoPercentEncodeSet = new Set([\n    47,\n    58,\n    59,\n    61,\n    64,\n    91,\n    92,\n    93,\n    94,\n    124\n]);\nfunction isUserinfoPercentEncode(c) {\n    return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\n}\nfunction percentEncodeChar(c, encodeSetPredicate) {\n    const cStr = String.fromCodePoint(c);\n    if (encodeSetPredicate(c)) {\n        return utf8PercentEncode(cStr);\n    }\n    return cStr;\n}\nfunction parseIPv4Number(input) {\n    let R = 10;\n    if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\n        input = input.substring(2);\n        R = 16;\n    } else if (input.length >= 2 && input.charAt(0) === \"0\") {\n        input = input.substring(1);\n        R = 8;\n    }\n    if (input === \"\") {\n        return 0;\n    }\n    const regex = R === 10 ? /[^0-9]/ : R === 16 ? /[^0-9A-Fa-f]/ : /[^0-7]/;\n    if (regex.test(input)) {\n        return failure;\n    }\n    return parseInt(input, R);\n}\nfunction parseIPv4(input) {\n    const parts = input.split(\".\");\n    if (parts[parts.length - 1] === \"\") {\n        if (parts.length > 1) {\n            parts.pop();\n        }\n    }\n    if (parts.length > 4) {\n        return input;\n    }\n    const numbers = [];\n    for (const part of parts){\n        if (part === \"\") {\n            return input;\n        }\n        const n = parseIPv4Number(part);\n        if (n === failure) {\n            return input;\n        }\n        numbers.push(n);\n    }\n    for(let i = 0; i < numbers.length - 1; ++i){\n        if (numbers[i] > 255) {\n            return failure;\n        }\n    }\n    if (numbers[numbers.length - 1] >= Math.pow(256, 5 - numbers.length)) {\n        return failure;\n    }\n    let ipv4 = numbers.pop();\n    let counter = 0;\n    for (const n of numbers){\n        ipv4 += n * Math.pow(256, 3 - counter);\n        ++counter;\n    }\n    return ipv4;\n}\nfunction serializeIPv4(address) {\n    let output = \"\";\n    let n = address;\n    for(let i = 1; i <= 4; ++i){\n        output = String(n % 256) + output;\n        if (i !== 4) {\n            output = \".\" + output;\n        }\n        n = Math.floor(n / 256);\n    }\n    return output;\n}\nfunction parseIPv6(input) {\n    const address = [\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0,\n        0\n    ];\n    let pieceIndex = 0;\n    let compress = null;\n    let pointer = 0;\n    input = punycode.ucs2.decode(input);\n    if (input[pointer] === 58) {\n        if (input[pointer + 1] !== 58) {\n            return failure;\n        }\n        pointer += 2;\n        ++pieceIndex;\n        compress = pieceIndex;\n    }\n    while(pointer < input.length){\n        if (pieceIndex === 8) {\n            return failure;\n        }\n        if (input[pointer] === 58) {\n            if (compress !== null) {\n                return failure;\n            }\n            ++pointer;\n            ++pieceIndex;\n            compress = pieceIndex;\n            continue;\n        }\n        let value = 0;\n        let length = 0;\n        while(length < 4 && isASCIIHex(input[pointer])){\n            value = value * 0x10 + parseInt(at(input, pointer), 16);\n            ++pointer;\n            ++length;\n        }\n        if (input[pointer] === 46) {\n            if (length === 0) {\n                return failure;\n            }\n            pointer -= length;\n            if (pieceIndex > 6) {\n                return failure;\n            }\n            let numbersSeen = 0;\n            while(input[pointer] !== undefined){\n                let ipv4Piece = null;\n                if (numbersSeen > 0) {\n                    if (input[pointer] === 46 && numbersSeen < 4) {\n                        ++pointer;\n                    } else {\n                        return failure;\n                    }\n                }\n                if (!isASCIIDigit(input[pointer])) {\n                    return failure;\n                }\n                while(isASCIIDigit(input[pointer])){\n                    const number = parseInt(at(input, pointer));\n                    if (ipv4Piece === null) {\n                        ipv4Piece = number;\n                    } else if (ipv4Piece === 0) {\n                        return failure;\n                    } else {\n                        ipv4Piece = ipv4Piece * 10 + number;\n                    }\n                    if (ipv4Piece > 255) {\n                        return failure;\n                    }\n                    ++pointer;\n                }\n                address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\n                ++numbersSeen;\n                if (numbersSeen === 2 || numbersSeen === 4) {\n                    ++pieceIndex;\n                }\n            }\n            if (numbersSeen !== 4) {\n                return failure;\n            }\n            break;\n        } else if (input[pointer] === 58) {\n            ++pointer;\n            if (input[pointer] === undefined) {\n                return failure;\n            }\n        } else if (input[pointer] !== undefined) {\n            return failure;\n        }\n        address[pieceIndex] = value;\n        ++pieceIndex;\n    }\n    if (compress !== null) {\n        let swaps = pieceIndex - compress;\n        pieceIndex = 7;\n        while(pieceIndex !== 0 && swaps > 0){\n            const temp = address[compress + swaps - 1];\n            address[compress + swaps - 1] = address[pieceIndex];\n            address[pieceIndex] = temp;\n            --pieceIndex;\n            --swaps;\n        }\n    } else if (compress === null && pieceIndex !== 8) {\n        return failure;\n    }\n    return address;\n}\nfunction serializeIPv6(address) {\n    let output = \"\";\n    const seqResult = findLongestZeroSequence(address);\n    const compress = seqResult.idx;\n    let ignore0 = false;\n    for(let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex){\n        if (ignore0 && address[pieceIndex] === 0) {\n            continue;\n        } else if (ignore0) {\n            ignore0 = false;\n        }\n        if (compress === pieceIndex) {\n            const separator = pieceIndex === 0 ? \"::\" : \":\";\n            output += separator;\n            ignore0 = true;\n            continue;\n        }\n        output += address[pieceIndex].toString(16);\n        if (pieceIndex !== 7) {\n            output += \":\";\n        }\n    }\n    return output;\n}\nfunction parseHost(input, isSpecialArg) {\n    if (input[0] === \"[\") {\n        if (input[input.length - 1] !== \"]\") {\n            return failure;\n        }\n        return parseIPv6(input.substring(1, input.length - 1));\n    }\n    if (!isSpecialArg) {\n        return parseOpaqueHost(input);\n    }\n    const domain = utf8PercentDecode(input);\n    const asciiDomain = tr46.toASCII(domain, false, tr46.PROCESSING_OPTIONS.NONTRANSITIONAL, false);\n    if (asciiDomain === null) {\n        return failure;\n    }\n    if (containsForbiddenHostCodePoint(asciiDomain)) {\n        return failure;\n    }\n    const ipv4Host = parseIPv4(asciiDomain);\n    if (typeof ipv4Host === \"number\" || ipv4Host === failure) {\n        return ipv4Host;\n    }\n    return asciiDomain;\n}\nfunction parseOpaqueHost(input) {\n    if (containsForbiddenHostCodePointExcludingPercent(input)) {\n        return failure;\n    }\n    let output = \"\";\n    const decoded = punycode.ucs2.decode(input);\n    for(let i = 0; i < decoded.length; ++i){\n        output += percentEncodeChar(decoded[i], isC0ControlPercentEncode);\n    }\n    return output;\n}\nfunction findLongestZeroSequence(arr) {\n    let maxIdx = null;\n    let maxLen = 1; // only find elements > 1\n    let currStart = null;\n    let currLen = 0;\n    for(let i = 0; i < arr.length; ++i){\n        if (arr[i] !== 0) {\n            if (currLen > maxLen) {\n                maxIdx = currStart;\n                maxLen = currLen;\n            }\n            currStart = null;\n            currLen = 0;\n        } else {\n            if (currStart === null) {\n                currStart = i;\n            }\n            ++currLen;\n        }\n    }\n    // if trailing zeros\n    if (currLen > maxLen) {\n        maxIdx = currStart;\n        maxLen = currLen;\n    }\n    return {\n        idx: maxIdx,\n        len: maxLen\n    };\n}\nfunction serializeHost(host) {\n    if (typeof host === \"number\") {\n        return serializeIPv4(host);\n    }\n    // IPv6 serializer\n    if (host instanceof Array) {\n        return \"[\" + serializeIPv6(host) + \"]\";\n    }\n    return host;\n}\nfunction trimControlChars(url) {\n    return url.replace(/^[\\u0000-\\u001F\\u0020]+|[\\u0000-\\u001F\\u0020]+$/g, \"\");\n}\nfunction trimTabAndNewline(url) {\n    return url.replace(/\\u0009|\\u000A|\\u000D/g, \"\");\n}\nfunction shortenPath(url) {\n    const path = url.path;\n    if (path.length === 0) {\n        return;\n    }\n    if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\n        return;\n    }\n    path.pop();\n}\nfunction includesCredentials(url) {\n    return url.username !== \"\" || url.password !== \"\";\n}\nfunction cannotHaveAUsernamePasswordPort(url) {\n    return url.host === null || url.host === \"\" || url.cannotBeABaseURL || url.scheme === \"file\";\n}\nfunction isNormalizedWindowsDriveLetter(string) {\n    return /^[A-Za-z]:$/.test(string);\n}\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\n    this.pointer = 0;\n    this.input = input;\n    this.base = base || null;\n    this.encodingOverride = encodingOverride || \"utf-8\";\n    this.stateOverride = stateOverride;\n    this.url = url;\n    this.failure = false;\n    this.parseError = false;\n    if (!this.url) {\n        this.url = {\n            scheme: \"\",\n            username: \"\",\n            password: \"\",\n            host: null,\n            port: null,\n            path: [],\n            query: null,\n            fragment: null,\n            cannotBeABaseURL: false\n        };\n        const res = trimControlChars(this.input);\n        if (res !== this.input) {\n            this.parseError = true;\n        }\n        this.input = res;\n    }\n    const res = trimTabAndNewline(this.input);\n    if (res !== this.input) {\n        this.parseError = true;\n    }\n    this.input = res;\n    this.state = stateOverride || \"scheme start\";\n    this.buffer = \"\";\n    this.atFlag = false;\n    this.arrFlag = false;\n    this.passwordTokenSeenFlag = false;\n    this.input = punycode.ucs2.decode(this.input);\n    for(; this.pointer <= this.input.length; ++this.pointer){\n        const c = this.input[this.pointer];\n        const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\n        // exec state machine\n        const ret = this[\"parse \" + this.state](c, cStr);\n        if (!ret) {\n            break; // terminate algorithm\n        } else if (ret === failure) {\n            this.failure = true;\n            break;\n        }\n    }\n}\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\n    if (isASCIIAlpha(c)) {\n        this.buffer += cStr.toLowerCase();\n        this.state = \"scheme\";\n    } else if (!this.stateOverride) {\n        this.state = \"no scheme\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\n    if (isASCIIAlphanumeric(c) || c === 43 || c === 45 || c === 46) {\n        this.buffer += cStr.toLowerCase();\n    } else if (c === 58) {\n        if (this.stateOverride) {\n            if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\n                return false;\n            }\n            if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\n                return false;\n            }\n            if (this.url.scheme === \"file\" && (this.url.host === \"\" || this.url.host === null)) {\n                return false;\n            }\n        }\n        this.url.scheme = this.buffer;\n        this.buffer = \"\";\n        if (this.stateOverride) {\n            return false;\n        }\n        if (this.url.scheme === \"file\") {\n            if (this.input[this.pointer + 1] !== 47 || this.input[this.pointer + 2] !== 47) {\n                this.parseError = true;\n            }\n            this.state = \"file\";\n        } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\n            this.state = \"special relative or authority\";\n        } else if (isSpecial(this.url)) {\n            this.state = \"special authority slashes\";\n        } else if (this.input[this.pointer + 1] === 47) {\n            this.state = \"path or authority\";\n            ++this.pointer;\n        } else {\n            this.url.cannotBeABaseURL = true;\n            this.url.path.push(\"\");\n            this.state = \"cannot-be-a-base-URL path\";\n        }\n    } else if (!this.stateOverride) {\n        this.buffer = \"\";\n        this.state = \"no scheme\";\n        this.pointer = -1;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\n    if (this.base === null || this.base.cannotBeABaseURL && c !== 35) {\n        return failure;\n    } else if (this.base.cannotBeABaseURL && c === 35) {\n        this.url.scheme = this.base.scheme;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.url.cannotBeABaseURL = true;\n        this.state = \"fragment\";\n    } else if (this.base.scheme === \"file\") {\n        this.state = \"file\";\n        --this.pointer;\n    } else {\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"relative\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\n    if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\n    this.url.scheme = this.base.scheme;\n    if (isNaN(c)) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n    } else if (c === 47) {\n        this.state = \"relative slash\";\n    } else if (c === 63) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice();\n        this.url.query = this.base.query;\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (isSpecial(this.url) && c === 92) {\n        this.parseError = true;\n        this.state = \"relative slash\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.url.path = this.base.path.slice(0, this.base.path.length - 1);\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\n    if (isSpecial(this.url) && (c === 47 || c === 92)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"special authority ignore slashes\";\n    } else if (c === 47) {\n        this.state = \"authority\";\n    } else {\n        this.url.username = this.base.username;\n        this.url.password = this.base.password;\n        this.url.host = this.base.host;\n        this.url.port = this.base.port;\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\n    if (c === 47 && this.input[this.pointer + 1] === 47) {\n        this.state = \"special authority ignore slashes\";\n        ++this.pointer;\n    } else {\n        this.parseError = true;\n        this.state = \"special authority ignore slashes\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\n    if (c !== 47 && c !== 92) {\n        this.state = \"authority\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\n    if (c === 64) {\n        this.parseError = true;\n        if (this.atFlag) {\n            this.buffer = \"%40\" + this.buffer;\n        }\n        this.atFlag = true;\n        // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\n        const len = countSymbols(this.buffer);\n        for(let pointer = 0; pointer < len; ++pointer){\n            const codePoint = this.buffer.codePointAt(pointer);\n            if (codePoint === 58 && !this.passwordTokenSeenFlag) {\n                this.passwordTokenSeenFlag = true;\n                continue;\n            }\n            const encodedCodePoints = percentEncodeChar(codePoint, isUserinfoPercentEncode);\n            if (this.passwordTokenSeenFlag) {\n                this.url.password += encodedCodePoints;\n            } else {\n                this.url.username += encodedCodePoints;\n            }\n        }\n        this.buffer = \"\";\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        if (this.atFlag && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        this.pointer -= countSymbols(this.buffer) + 1;\n        this.buffer = \"\";\n        this.state = \"host\";\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse hostname\"] = URLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\n    if (this.stateOverride && this.url.scheme === \"file\") {\n        --this.pointer;\n        this.state = \"file host\";\n    } else if (c === 58 && !this.arrFlag) {\n        if (this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"port\";\n        if (this.stateOverride === \"hostname\") {\n            return false;\n        }\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92) {\n        --this.pointer;\n        if (isSpecial(this.url) && this.buffer === \"\") {\n            this.parseError = true;\n            return failure;\n        } else if (this.stateOverride && this.buffer === \"\" && (includesCredentials(this.url) || this.url.port !== null)) {\n            this.parseError = true;\n            return false;\n        }\n        const host = parseHost(this.buffer, isSpecial(this.url));\n        if (host === failure) {\n            return failure;\n        }\n        this.url.host = host;\n        this.buffer = \"\";\n        this.state = \"path start\";\n        if (this.stateOverride) {\n            return false;\n        }\n    } else {\n        if (c === 91) {\n            this.arrFlag = true;\n        } else if (c === 93) {\n            this.arrFlag = false;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\n    if (isASCIIDigit(c)) {\n        this.buffer += cStr;\n    } else if (isNaN(c) || c === 47 || c === 63 || c === 35 || isSpecial(this.url) && c === 92 || this.stateOverride) {\n        if (this.buffer !== \"\") {\n            const port = parseInt(this.buffer);\n            if (port > Math.pow(2, 16) - 1) {\n                this.parseError = true;\n                return failure;\n            }\n            this.url.port = port === defaultPort(this.url.scheme) ? null : port;\n            this.buffer = \"\";\n        }\n        if (this.stateOverride) {\n            return false;\n        }\n        this.state = \"path start\";\n        --this.pointer;\n    } else {\n        this.parseError = true;\n        return failure;\n    }\n    return true;\n};\nconst fileOtherwiseCodePoints = new Set([\n    47,\n    92,\n    63,\n    35\n]);\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\n    this.url.scheme = \"file\";\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file slash\";\n    } else if (this.base !== null && this.base.scheme === \"file\") {\n        if (isNaN(c)) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n        } else if (c === 63) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = \"\";\n            this.state = \"query\";\n        } else if (c === 35) {\n            this.url.host = this.base.host;\n            this.url.path = this.base.path.slice();\n            this.url.query = this.base.query;\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        } else {\n            if (this.input.length - this.pointer - 1 === 0 || // remaining consists of 0 code points\n            !isWindowsDriveLetterCodePoints(c, this.input[this.pointer + 1]) || this.input.length - this.pointer - 1 >= 2 && // remaining has at least 2 code points\n            !fileOtherwiseCodePoints.has(this.input[this.pointer + 2])) {\n                this.url.host = this.base.host;\n                this.url.path = this.base.path.slice();\n                shortenPath(this.url);\n            } else {\n                this.parseError = true;\n            }\n            this.state = \"path\";\n            --this.pointer;\n        }\n    } else {\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\n    if (c === 47 || c === 92) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"file host\";\n    } else {\n        if (this.base !== null && this.base.scheme === \"file\") {\n            if (isNormalizedWindowsDriveLetterString(this.base.path[0])) {\n                this.url.path.push(this.base.path[0]);\n            } else {\n                this.url.host = this.base.host;\n            }\n        }\n        this.state = \"path\";\n        --this.pointer;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\n    if (isNaN(c) || c === 47 || c === 92 || c === 63 || c === 35) {\n        --this.pointer;\n        if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\n            this.parseError = true;\n            this.state = \"path\";\n        } else if (this.buffer === \"\") {\n            this.url.host = \"\";\n            if (this.stateOverride) {\n                return false;\n            }\n            this.state = \"path start\";\n        } else {\n            let host = parseHost(this.buffer, isSpecial(this.url));\n            if (host === failure) {\n                return failure;\n            }\n            if (host === \"localhost\") {\n                host = \"\";\n            }\n            this.url.host = host;\n            if (this.stateOverride) {\n                return false;\n            }\n            this.buffer = \"\";\n            this.state = \"path start\";\n        }\n    } else {\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\n    if (isSpecial(this.url)) {\n        if (c === 92) {\n            this.parseError = true;\n        }\n        this.state = \"path\";\n        if (c !== 47 && c !== 92) {\n            --this.pointer;\n        }\n    } else if (!this.stateOverride && c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (!this.stateOverride && c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else if (c !== undefined) {\n        this.state = \"path\";\n        if (c !== 47) {\n            --this.pointer;\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\n    if (isNaN(c) || c === 47 || isSpecial(this.url) && c === 92 || !this.stateOverride && (c === 63 || c === 35)) {\n        if (isSpecial(this.url) && c === 92) {\n            this.parseError = true;\n        }\n        if (isDoubleDot(this.buffer)) {\n            shortenPath(this.url);\n            if (c !== 47 && !(isSpecial(this.url) && c === 92)) {\n                this.url.path.push(\"\");\n            }\n        } else if (isSingleDot(this.buffer) && c !== 47 && !(isSpecial(this.url) && c === 92)) {\n            this.url.path.push(\"\");\n        } else if (!isSingleDot(this.buffer)) {\n            if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\n                if (this.url.host !== \"\" && this.url.host !== null) {\n                    this.parseError = true;\n                    this.url.host = \"\";\n                }\n                this.buffer = this.buffer[0] + \":\";\n            }\n            this.url.path.push(this.buffer);\n        }\n        this.buffer = \"\";\n        if (this.url.scheme === \"file\" && (c === undefined || c === 63 || c === 35)) {\n            while(this.url.path.length > 1 && this.url.path[0] === \"\"){\n                this.parseError = true;\n                this.url.path.shift();\n            }\n        }\n        if (c === 63) {\n            this.url.query = \"\";\n            this.state = \"query\";\n        }\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += percentEncodeChar(c, isPathPercentEncode);\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse cannot-be-a-base-URL path\"] = function parseCannotBeABaseURLPath(c) {\n    if (c === 63) {\n        this.url.query = \"\";\n        this.state = \"query\";\n    } else if (c === 35) {\n        this.url.fragment = \"\";\n        this.state = \"fragment\";\n    } else {\n        // TODO: Add: not a URL code point\n        if (!isNaN(c) && c !== 37) {\n            this.parseError = true;\n        }\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        if (!isNaN(c)) {\n            this.url.path[0] = this.url.path[0] + percentEncodeChar(c, isC0ControlPercentEncode);\n        }\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\n    if (isNaN(c) || !this.stateOverride && c === 35) {\n        if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\n            this.encodingOverride = \"utf-8\";\n        }\n        const buffer = new Buffer(this.buffer); // TODO: Use encoding override instead\n        for(let i = 0; i < buffer.length; ++i){\n            if (buffer[i] < 0x21 || buffer[i] > 0x7E || buffer[i] === 0x22 || buffer[i] === 0x23 || buffer[i] === 0x3C || buffer[i] === 0x3E) {\n                this.url.query += percentEncode(buffer[i]);\n            } else {\n                this.url.query += String.fromCodePoint(buffer[i]);\n            }\n        }\n        this.buffer = \"\";\n        if (c === 35) {\n            this.url.fragment = \"\";\n            this.state = \"fragment\";\n        }\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.buffer += cStr;\n    }\n    return true;\n};\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\n    if (isNaN(c)) {} else if (c === 0x0) {\n        this.parseError = true;\n    } else {\n        // TODO: If c is not a URL code point and not \"%\", parse error.\n        if (c === 37 && (!isASCIIHex(this.input[this.pointer + 1]) || !isASCIIHex(this.input[this.pointer + 2]))) {\n            this.parseError = true;\n        }\n        this.url.fragment += percentEncodeChar(c, isC0ControlPercentEncode);\n    }\n    return true;\n};\nfunction serializeURL(url, excludeFragment) {\n    let output = url.scheme + \":\";\n    if (url.host !== null) {\n        output += \"//\";\n        if (url.username !== \"\" || url.password !== \"\") {\n            output += url.username;\n            if (url.password !== \"\") {\n                output += \":\" + url.password;\n            }\n            output += \"@\";\n        }\n        output += serializeHost(url.host);\n        if (url.port !== null) {\n            output += \":\" + url.port;\n        }\n    } else if (url.host === null && url.scheme === \"file\") {\n        output += \"//\";\n    }\n    if (url.cannotBeABaseURL) {\n        output += url.path[0];\n    } else {\n        for (const string of url.path){\n            output += \"/\" + string;\n        }\n    }\n    if (url.query !== null) {\n        output += \"?\" + url.query;\n    }\n    if (!excludeFragment && url.fragment !== null) {\n        output += \"#\" + url.fragment;\n    }\n    return output;\n}\nfunction serializeOrigin(tuple) {\n    let result = tuple.scheme + \"://\";\n    result += serializeHost(tuple.host);\n    if (tuple.port !== null) {\n        result += \":\" + tuple.port;\n    }\n    return result;\n}\nmodule.exports.serializeURL = serializeURL;\nmodule.exports.serializeURLOrigin = function(url) {\n    // https://url.spec.whatwg.org/#concept-url-origin\n    switch(url.scheme){\n        case \"blob\":\n            try {\n                return module.exports.serializeURLOrigin(module.exports.parseURL(url.path[0]));\n            } catch (e) {\n                // serializing an opaque origin returns \"null\"\n                return \"null\";\n            }\n        case \"ftp\":\n        case \"gopher\":\n        case \"http\":\n        case \"https\":\n        case \"ws\":\n        case \"wss\":\n            return serializeOrigin({\n                scheme: url.scheme,\n                host: url.host,\n                port: url.port\n            });\n        case \"file\":\n            // spec says \"exercise to the reader\", chrome says \"file://\"\n            return \"file://\";\n        default:\n            // serializing an opaque origin returns \"null\"\n            return \"null\";\n    }\n};\nmodule.exports.basicURLParse = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\n    if (usm.failure) {\n        return \"failure\";\n    }\n    return usm.url;\n};\nmodule.exports.setTheUsername = function(url, username) {\n    url.username = \"\";\n    const decoded = punycode.ucs2.decode(username);\n    for(let i = 0; i < decoded.length; ++i){\n        url.username += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.setThePassword = function(url, password) {\n    url.password = \"\";\n    const decoded = punycode.ucs2.decode(password);\n    for(let i = 0; i < decoded.length; ++i){\n        url.password += percentEncodeChar(decoded[i], isUserinfoPercentEncode);\n    }\n};\nmodule.exports.serializeHost = serializeHost;\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\nmodule.exports.serializeInteger = function(integer) {\n    return String(integer);\n};\nmodule.exports.parseURL = function(input, options) {\n    if (options === undefined) {\n        options = {};\n    }\n    // We don't handle blobs, so this just delegates:\n    return module.exports.basicURLParse(input, {\n        baseURL: options.baseURL,\n        encodingOverride: options.encodingOverride\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/url-state-machine.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/utils.js":
/*!**************************************************************************************!*\
  !*** ../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/utils.js ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("\nmodule.exports.mixin = function mixin(target, source) {\n    const keys = Object.getOwnPropertyNames(source);\n    for(let i = 0; i < keys.length; ++i){\n        Object.defineProperty(target, keys[i], Object.getOwnPropertyDescriptor(source, keys[i]));\n    }\n};\nmodule.exports.wrapperSymbol = Symbol(\"wrapper\");\nmodule.exports.implSymbol = Symbol(\"impl\");\nmodule.exports.wrapperForImpl = function(impl) {\n    return impl[module.exports.wrapperSymbol];\n};\nmodule.exports.implForWrapper = function(wrapper) {\n    return wrapper[module.exports.implSymbol];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/utils.js\n");

/***/ })

};
;