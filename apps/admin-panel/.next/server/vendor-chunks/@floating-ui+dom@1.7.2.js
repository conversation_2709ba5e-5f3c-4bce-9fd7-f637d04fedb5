"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+dom@1.7.2";
exports.ids = ["vendor-chunks/@floating-ui+dom@1.7.2"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!**************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+core@1.7.2/node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n\n\n\n\nfunction getCssDimensions(element) {\n    const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n    // In testing environments, the `width` and `height` properties are empty\n    // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n    let width = parseFloat(css.width) || 0;\n    let height = parseFloat(css.height) || 0;\n    const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n    const offsetWidth = hasOffset ? element.offsetWidth : width;\n    const offsetHeight = hasOffset ? element.offsetHeight : height;\n    const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n    if (shouldFallback) {\n        width = offsetWidth;\n        height = offsetHeight;\n    }\n    return {\n        width,\n        height,\n        $: shouldFallback\n    };\n}\nfunction unwrapElement(element) {\n    return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n    const domElement = unwrapElement(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n        return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    }\n    const rect = domElement.getBoundingClientRect();\n    const { width, height, $ } = getCssDimensions(domElement);\n    let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n    let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n    // 0, NaN, or Infinity should always fallback to 1.\n    if (!x || !Number.isFinite(x)) {\n        x = 1;\n    }\n    if (!y || !Number.isFinite(y)) {\n        y = 1;\n    }\n    return {\n        x,\n        y\n    };\n}\nconst noOffsets = /*#__PURE__*/ (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n        return noOffsets;\n    }\n    return {\n        x: win.visualViewport.offsetLeft,\n        y: win.visualViewport.offsetTop\n    };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n    if (isFixed === void 0) {\n        isFixed = false;\n    }\n    if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n        return false;\n    }\n    return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n    if (includeScale === void 0) {\n        includeScale = false;\n    }\n    if (isFixedStrategy === void 0) {\n        isFixedStrategy = false;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const domElement = unwrapElement(element);\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    if (includeScale) {\n        if (offsetParent) {\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n                scale = getScale(offsetParent);\n            }\n        } else {\n            scale = getScale(element);\n        }\n    }\n    const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    let x = (clientRect.left + visualOffsets.x) / scale.x;\n    let y = (clientRect.top + visualOffsets.y) / scale.y;\n    let width = clientRect.width / scale.x;\n    let height = clientRect.height / scale.y;\n    if (domElement) {\n        const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n        const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n        let currentWin = win;\n        let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        while(currentIFrame && offsetParent && offsetWin !== currentWin){\n            const iframeScale = getScale(currentIFrame);\n            const iframeRect = currentIFrame.getBoundingClientRect();\n            const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n            const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n            const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n            x *= iframeScale.x;\n            y *= iframeScale.y;\n            width *= iframeScale.x;\n            height *= iframeScale.y;\n            x += left;\n            y += top;\n            currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n            currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        }\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n        width,\n        height,\n        x,\n        y\n    });\n}\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n    const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n    if (!rect) {\n        return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n    }\n    return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n    if (ignoreScrollbarX === void 0) {\n        ignoreScrollbarX = false;\n    }\n    const htmlRect = documentElement.getBoundingClientRect();\n    const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : // RTL <body> scrollbar.\n    getWindowScrollBarX(documentElement, htmlRect));\n    const y = htmlRect.top + scroll.scrollTop;\n    return {\n        x,\n        y\n    };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n    let { elements, rect, offsetParent, strategy } = _ref;\n    const isFixed = strategy === \"fixed\";\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n    if (offsetParent === documentElement || topLayer && isFixed) {\n        return rect;\n    }\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n            const offsetRect = getBoundingClientRect(offsetParent);\n            scale = getScale(offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        }\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    return {\n        width: rect.width * scale.x,\n        height: rect.height * scale.y,\n        x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n        y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n    };\n}\nfunction getClientRects(element) {\n    return Array.from(element.getClientRects());\n}\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n    const body = element.ownerDocument.body;\n    const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n    const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n    let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n    const y = -scroll.scrollTop;\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === \"rtl\") {\n        x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getViewportRect(element, strategy) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const visualViewport = win.visualViewport;\n    let width = html.clientWidth;\n    let height = html.clientHeight;\n    let x = 0;\n    let y = 0;\n    if (visualViewport) {\n        width = visualViewport.width;\n        height = visualViewport.height;\n        const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n        if (!visualViewportBased || visualViewportBased && strategy === \"fixed\") {\n            x = visualViewport.offsetLeft;\n            y = visualViewport.offsetTop;\n        }\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nconst absoluteOrFixed = /*#__PURE__*/ new Set([\n    \"absolute\",\n    \"fixed\"\n]);\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n    const clientRect = getBoundingClientRect(element, true, strategy === \"fixed\");\n    const top = clientRect.top + element.clientTop;\n    const left = clientRect.left + element.clientLeft;\n    const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const width = element.clientWidth * scale.x;\n    const height = element.clientHeight * scale.y;\n    const x = left * scale.x;\n    const y = top * scale.y;\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n    let rect;\n    if (clippingAncestor === \"viewport\") {\n        rect = getViewportRect(element, strategy);\n    } else if (clippingAncestor === \"document\") {\n        rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n    } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n        rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n    } else {\n        const visualOffsets = getVisualOffsets(element);\n        rect = {\n            x: clippingAncestor.x - visualOffsets.x,\n            y: clippingAncestor.y - visualOffsets.y,\n            width: clippingAncestor.width,\n            height: clippingAncestor.height\n        };\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n    const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n        return false;\n    }\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === \"fixed\" || hasFixedPositionAncestor(parentNode, stopNode);\n}\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n    const cachedResult = cache.get(element);\n    if (cachedResult) {\n        return cachedResult;\n    }\n    let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter((el)=>(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== \"body\");\n    let currentContainingBlockComputedStyle = null;\n    const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\";\n    let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    while((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)){\n        const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n        const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n        if (!currentNodeIsContaining && computedStyle.position === \"fixed\") {\n            currentContainingBlockComputedStyle = null;\n        }\n        const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === \"static\" && !!currentContainingBlockComputedStyle && absoluteOrFixed.has(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n        if (shouldDropCurrentNode) {\n            // Drop non-containing blocks.\n            result = result.filter((ancestor)=>ancestor !== currentNode);\n        } else {\n            // Record last containing block for next iteration.\n            currentContainingBlockComputedStyle = computedStyle;\n        }\n        currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n    }\n    cache.set(element, result);\n    return result;\n}\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n    let { element, boundary, rootBoundary, strategy } = _ref;\n    const elementClippingAncestors = boundary === \"clippingAncestors\" ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n    const clippingAncestors = [\n        ...elementClippingAncestors,\n        rootBoundary\n    ];\n    const firstClippingAncestor = clippingAncestors[0];\n    const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor)=>{\n        const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n        accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n        accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n        accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n        accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n        return accRect;\n    }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n    return {\n        width: clippingRect.right - clippingRect.left,\n        height: clippingRect.bottom - clippingRect.top,\n        x: clippingRect.left,\n        y: clippingRect.top\n    };\n}\nfunction getDimensions(element) {\n    const { width, height } = getCssDimensions(element);\n    return {\n        width,\n        height\n    };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const isFixed = strategy === \"fixed\";\n    const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n    // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n    function setLeftRTLScrollbarOffset() {\n        offsets.x = getWindowScrollBarX(documentElement);\n    }\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if (isOffsetParentAnElement) {\n            const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        } else if (documentElement) {\n            setLeftRTLScrollbarOffset();\n        }\n    }\n    if (isFixed && !isOffsetParentAnElement && documentElement) {\n        setLeftRTLScrollbarOffset();\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n    const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n    return {\n        x,\n        y,\n        width: rect.width,\n        height: rect.height\n    };\n}\nfunction isStaticPositioned(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"static\";\n}\nfunction getTrueOffsetParent(element, polyfill) {\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\") {\n        return null;\n    }\n    if (polyfill) {\n        return polyfill(element);\n    }\n    let rawOffsetParent = element.offsetParent;\n    // Firefox returns the <html> element as the offsetParent if it's non-static,\n    // while Chrome and Safari return the <body> element. The <body> element must\n    // be used to perform the correct calculations even if the <html> element is\n    // non-static.\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n        rawOffsetParent = rawOffsetParent.ownerDocument.body;\n    }\n    return rawOffsetParent;\n}\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n        return win;\n    }\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n        let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n        while(svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)){\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n                return svgOffsetParent;\n            }\n            svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n        }\n        return win;\n    }\n    let offsetParent = getTrueOffsetParent(element, polyfill);\n    while(offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)){\n        offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n    }\n    if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n        return win;\n    }\n    return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function(data) {\n    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n    const getDimensionsFn = this.getDimensions;\n    const floatingDimensions = await getDimensionsFn(data.floating);\n    return {\n        reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n        floating: {\n            x: 0,\n            y: 0,\n            width: floatingDimensions.width,\n            height: floatingDimensions.height\n        }\n    };\n};\nfunction isRTL(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === \"rtl\";\n}\nconst platform = {\n    convertOffsetParentRelativeRectToViewportRelativeRect,\n    getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n    getClippingRect,\n    getOffsetParent,\n    getElementRects,\n    getClientRects,\n    getDimensions,\n    getScale,\n    isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n    isRTL\n};\nfunction rectsAreEqual(a, b) {\n    return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n    let io = null;\n    let timeoutId;\n    const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    function cleanup() {\n        var _io;\n        clearTimeout(timeoutId);\n        (_io = io) == null || _io.disconnect();\n        io = null;\n    }\n    function refresh(skip, threshold) {\n        if (skip === void 0) {\n            skip = false;\n        }\n        if (threshold === void 0) {\n            threshold = 1;\n        }\n        cleanup();\n        const elementRectForRootMargin = element.getBoundingClientRect();\n        const { left, top, width, height } = elementRectForRootMargin;\n        if (!skip) {\n            onMove();\n        }\n        if (!width || !height) {\n            return;\n        }\n        const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n        const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n        const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n        const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n        const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n        const options = {\n            rootMargin,\n            threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n        };\n        let isFirstUpdate = true;\n        function handleObserve(entries) {\n            const ratio = entries[0].intersectionRatio;\n            if (ratio !== threshold) {\n                if (!isFirstUpdate) {\n                    return refresh();\n                }\n                if (!ratio) {\n                    // If the reference is clipped, the ratio is 0. Throttle the refresh\n                    // to prevent an infinite loop of updates.\n                    timeoutId = setTimeout(()=>{\n                        refresh(false, 1e-7);\n                    }, 1000);\n                } else {\n                    refresh(false, ratio);\n                }\n            }\n            if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n                // It's possible that even though the ratio is reported as 1, the\n                // element is not actually fully within the IntersectionObserver's root\n                // area anymore. This can happen under performance constraints. This may\n                // be a bug in the browser's IntersectionObserver implementation. To\n                // work around this, we compare the element's bounding rect now with\n                // what it was at the time we created the IntersectionObserver. If they\n                // are not equal then the element moved, so we refresh.\n                refresh();\n            }\n            isFirstUpdate = false;\n        }\n        // Older browsers don't support a `document` as the root and will throw an\n        // error.\n        try {\n            io = new IntersectionObserver(handleObserve, {\n                ...options,\n                // Handle <iframe>s\n                root: root.ownerDocument\n            });\n        } catch (_e) {\n            io = new IntersectionObserver(handleObserve, options);\n        }\n        io.observe(element);\n    }\n    refresh(true);\n    return cleanup;\n}\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */ function autoUpdate(reference, floating, update, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    const { ancestorScroll = true, ancestorResize = true, elementResize = typeof ResizeObserver === \"function\", layoutShift = typeof IntersectionObserver === \"function\", animationFrame = false } = options;\n    const referenceEl = unwrapElement(reference);\n    const ancestors = ancestorScroll || ancestorResize ? [\n        ...referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : [],\n        ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)\n    ] : [];\n    ancestors.forEach((ancestor)=>{\n        ancestorScroll && ancestor.addEventListener(\"scroll\", update, {\n            passive: true\n        });\n        ancestorResize && ancestor.addEventListener(\"resize\", update);\n    });\n    const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n    let reobserveFrame = -1;\n    let resizeObserver = null;\n    if (elementResize) {\n        resizeObserver = new ResizeObserver((_ref)=>{\n            let [firstEntry] = _ref;\n            if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n                // Prevent update loops when using the `size` middleware.\n                // https://github.com/floating-ui/floating-ui/issues/1740\n                resizeObserver.unobserve(floating);\n                cancelAnimationFrame(reobserveFrame);\n                reobserveFrame = requestAnimationFrame(()=>{\n                    var _resizeObserver;\n                    (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n                });\n            }\n            update();\n        });\n        if (referenceEl && !animationFrame) {\n            resizeObserver.observe(referenceEl);\n        }\n        resizeObserver.observe(floating);\n    }\n    let frameId;\n    let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n    if (animationFrame) {\n        frameLoop();\n    }\n    function frameLoop() {\n        const nextRefRect = getBoundingClientRect(reference);\n        if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n            update();\n        }\n        prevRefRect = nextRefRect;\n        frameId = requestAnimationFrame(frameLoop);\n    }\n    update();\n    return ()=>{\n        var _resizeObserver2;\n        ancestors.forEach((ancestor)=>{\n            ancestorScroll && ancestor.removeEventListener(\"scroll\", update);\n            ancestorResize && ancestor.removeEventListener(\"resize\", update);\n        });\n        cleanupIo == null || cleanupIo();\n        (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n        resizeObserver = null;\n        if (animationFrame) {\n            cancelAnimationFrame(frameId);\n        }\n    };\n}\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ const detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */ const computePosition = (reference, floating, options)=>{\n    // This caches the expensive `getClippingElementAncestors` function so that\n    // multiple lifecycle resets re-use the same result. It only lives for a\n    // single call. If other functions become expensive, we can add them as well.\n    const cache = new Map();\n    const mergedOptions = {\n        platform,\n        ...options\n    };\n    const platformWithCache = {\n        ...mergedOptions.platform,\n        _c: cache\n    };\n    return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, {\n        ...mergedOptions,\n        platform: platformWithCache\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ })

};
;