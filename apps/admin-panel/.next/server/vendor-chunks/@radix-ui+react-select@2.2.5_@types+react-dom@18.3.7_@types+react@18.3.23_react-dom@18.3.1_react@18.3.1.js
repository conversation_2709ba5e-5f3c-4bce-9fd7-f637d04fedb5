"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/../../node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/../../node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: SELECT_NAME\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange,\n        caller: SELECT_NAME\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectBubbleInput, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            as: Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    // Prevents the content from going off the starting edge of the\n                    // viewport. It may still go off the ending edge, but this can be\n                    // controlled by the user since they may want to manage overflow in a\n                    // specific way.\n                    // https://github.com/radix-ui/primitives/issues/2049\n                    Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSelect, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        if (!select) return;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select, {\n        ...props,\n        style: {\n            ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        },\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;