"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-use-size@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // src/switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: SWITCH_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SwitchBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSwitch, control, checked, bubbles = true, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            setChecked.call(input, checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@radix-ui+react-switch@1.2.5_@types+react-dom@18.3.7_@types+react@18.3.23_react-dom@18.3.1_react@18.3.1/node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ })

};
;