"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/get-nonce@1.0.1";
exports.ids = ["vendor-chunks/get-nonce@1.0.1"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js":
/*!********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getNonce: () => (/* binding */ getNonce),\n/* harmony export */   setNonce: () => (/* binding */ setNonce)\n/* harmony export */ });\nvar currentNonce;\nvar setNonce = function(nonce) {\n    currentNonce = nonce;\n};\nvar getNonce = function() {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (true) {\n        return __webpack_require__.nc;\n    }\n    return undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2dldC1ub25jZUAxLjAuMS9ub2RlX21vZHVsZXMvZ2V0LW5vbmNlL2Rpc3QvZXMyMDE1L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsSUFBSUE7QUFDRyxJQUFJQyxXQUFXLFNBQVVDLEtBQUs7SUFDakNGLGVBQWVFO0FBQ25CLEVBQUU7QUFDSyxJQUFJQyxXQUFXO0lBQ2xCLElBQUlILGNBQWM7UUFDZCxPQUFPQTtJQUNYO0lBQ0EsSUFBSSxJQUE2QixFQUFhO1FBQzFDLE9BQU9JLHNCQUFpQkE7SUFDNUI7SUFDQSxPQUFPQztBQUNYLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2dldC1ub25jZUAxLjAuMS9ub2RlX21vZHVsZXMvZ2V0LW5vbmNlL2Rpc3QvZXMyMDE1L2luZGV4LmpzP2VhMDkiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIGN1cnJlbnROb25jZTtcbmV4cG9ydCB2YXIgc2V0Tm9uY2UgPSBmdW5jdGlvbiAobm9uY2UpIHtcbiAgICBjdXJyZW50Tm9uY2UgPSBub25jZTtcbn07XG5leHBvcnQgdmFyIGdldE5vbmNlID0gZnVuY3Rpb24gKCkge1xuICAgIGlmIChjdXJyZW50Tm9uY2UpIHtcbiAgICAgICAgcmV0dXJuIGN1cnJlbnROb25jZTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBfX3dlYnBhY2tfbm9uY2VfXyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIF9fd2VicGFja19ub25jZV9fO1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xufTtcbiJdLCJuYW1lcyI6WyJjdXJyZW50Tm9uY2UiLCJzZXROb25jZSIsIm5vbmNlIiwiZ2V0Tm9uY2UiLCJfX3dlYnBhY2tfbm9uY2VfXyIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/get-nonce@1.0.1/node_modules/get-nonce/dist/es2015/index.js\n");

/***/ })

};
;