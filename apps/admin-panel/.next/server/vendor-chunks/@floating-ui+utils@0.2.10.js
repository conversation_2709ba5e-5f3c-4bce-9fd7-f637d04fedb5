"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+utils@0.2.10";
exports.ids = ["vendor-chunks/@floating-ui+utils@0.2.10"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction getNodeName(node) {\n    if (isNode(node)) {\n        return (node.nodeName || \"\").toLowerCase();\n    }\n    // Mocked nodes in testing environments may not be instances of Node. By\n    // returning `#document` an infinite loop won't occur.\n    // https://github.com/floating-ui/floating-ui/issues/2317\n    return \"#document\";\n}\nfunction getWindow(node) {\n    var _node$ownerDocument;\n    return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n    var _ref;\n    return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n    if (!hasWindow() || typeof ShadowRoot === \"undefined\") {\n        return false;\n    }\n    return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nconst invalidOverflowDisplayValues = /*#__PURE__*/ new Set([\n    \"inline\",\n    \"contents\"\n]);\nfunction isOverflowElement(element) {\n    const { overflow, overflowX, overflowY, display } = getComputedStyle(element);\n    return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !invalidOverflowDisplayValues.has(display);\n}\nconst tableElements = /*#__PURE__*/ new Set([\n    \"table\",\n    \"td\",\n    \"th\"\n]);\nfunction isTableElement(element) {\n    return tableElements.has(getNodeName(element));\n}\nconst topLayerSelectors = [\n    \":popover-open\",\n    \":modal\"\n];\nfunction isTopLayer(element) {\n    return topLayerSelectors.some((selector)=>{\n        try {\n            return element.matches(selector);\n        } catch (_e) {\n            return false;\n        }\n    });\n}\nconst transformProperties = [\n    \"transform\",\n    \"translate\",\n    \"scale\",\n    \"rotate\",\n    \"perspective\"\n];\nconst willChangeValues = [\n    \"transform\",\n    \"translate\",\n    \"scale\",\n    \"rotate\",\n    \"perspective\",\n    \"filter\"\n];\nconst containValues = [\n    \"paint\",\n    \"layout\",\n    \"strict\",\n    \"content\"\n];\nfunction isContainingBlock(elementOrCss) {\n    const webkit = isWebKit();\n    const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n    return transformProperties.some((value)=>css[value] ? css[value] !== \"none\" : false) || (css.containerType ? css.containerType !== \"normal\" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== \"none\" : false) || !webkit && (css.filter ? css.filter !== \"none\" : false) || willChangeValues.some((value)=>(css.willChange || \"\").includes(value)) || containValues.some((value)=>(css.contain || \"\").includes(value));\n}\nfunction getContainingBlock(element) {\n    let currentNode = getParentNode(element);\n    while(isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)){\n        if (isContainingBlock(currentNode)) {\n            return currentNode;\n        } else if (isTopLayer(currentNode)) {\n            return null;\n        }\n        currentNode = getParentNode(currentNode);\n    }\n    return null;\n}\nfunction isWebKit() {\n    if (typeof CSS === \"undefined\" || !CSS.supports) return false;\n    return CSS.supports(\"-webkit-backdrop-filter\", \"none\");\n}\nconst lastTraversableNodeNames = /*#__PURE__*/ new Set([\n    \"html\",\n    \"body\",\n    \"#document\"\n]);\nfunction isLastTraversableNode(node) {\n    return lastTraversableNodeNames.has(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n    return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n    if (isElement(element)) {\n        return {\n            scrollLeft: element.scrollLeft,\n            scrollTop: element.scrollTop\n        };\n    }\n    return {\n        scrollLeft: element.scrollX,\n        scrollTop: element.scrollY\n    };\n}\nfunction getParentNode(node) {\n    if (getNodeName(node) === \"html\") {\n        return node;\n    }\n    const result = // Step into the shadow DOM of the parent of a slotted node.\n    node.assignedSlot || // DOM Element detected.\n    node.parentNode || // ShadowRoot detected.\n    isShadowRoot(node) && node.host || // Fallback.\n    getDocumentElement(node);\n    return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n    const parentNode = getParentNode(node);\n    if (isLastTraversableNode(parentNode)) {\n        return node.ownerDocument ? node.ownerDocument.body : node.body;\n    }\n    if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n        return parentNode;\n    }\n    return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n    var _node$ownerDocument2;\n    if (list === void 0) {\n        list = [];\n    }\n    if (traverseIframes === void 0) {\n        traverseIframes = true;\n    }\n    const scrollableAncestor = getNearestOverflowAncestor(node);\n    const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n    const win = getWindow(scrollableAncestor);\n    if (isBody) {\n        const frameElement = getFrameElement(win);\n        return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n    }\n    return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n    return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */ const sides = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nconst alignments = [\n    \"start\",\n    \"end\"\n];\nconst placements = /*#__PURE__*/ sides.reduce((acc, side)=>acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = (v)=>({\n        x: v,\n        y: v\n    });\nconst oppositeSideMap = {\n    left: \"right\",\n    right: \"left\",\n    bottom: \"top\",\n    top: \"bottom\"\n};\nconst oppositeAlignmentMap = {\n    start: \"end\",\n    end: \"start\"\n};\nfunction clamp(start, value, end) {\n    return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n    return typeof value === \"function\" ? value(param) : value;\n}\nfunction getSide(placement) {\n    return placement.split(\"-\")[0];\n}\nfunction getAlignment(placement) {\n    return placement.split(\"-\")[1];\n}\nfunction getOppositeAxis(axis) {\n    return axis === \"x\" ? \"y\" : \"x\";\n}\nfunction getAxisLength(axis) {\n    return axis === \"y\" ? \"height\" : \"width\";\n}\nconst yAxisSides = /*#__PURE__*/ new Set([\n    \"top\",\n    \"bottom\"\n]);\nfunction getSideAxis(placement) {\n    return yAxisSides.has(getSide(placement)) ? \"y\" : \"x\";\n}\nfunction getAlignmentAxis(placement) {\n    return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n    if (rtl === void 0) {\n        rtl = false;\n    }\n    const alignment = getAlignment(placement);\n    const alignmentAxis = getAlignmentAxis(placement);\n    const length = getAxisLength(alignmentAxis);\n    let mainAlignmentSide = alignmentAxis === \"x\" ? alignment === (rtl ? \"end\" : \"start\") ? \"right\" : \"left\" : alignment === \"start\" ? \"bottom\" : \"top\";\n    if (rects.reference[length] > rects.floating[length]) {\n        mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n    }\n    return [\n        mainAlignmentSide,\n        getOppositePlacement(mainAlignmentSide)\n    ];\n}\nfunction getExpandedPlacements(placement) {\n    const oppositePlacement = getOppositePlacement(placement);\n    return [\n        getOppositeAlignmentPlacement(placement),\n        oppositePlacement,\n        getOppositeAlignmentPlacement(oppositePlacement)\n    ];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n    return placement.replace(/start|end/g, (alignment)=>oppositeAlignmentMap[alignment]);\n}\nconst lrPlacement = [\n    \"left\",\n    \"right\"\n];\nconst rlPlacement = [\n    \"right\",\n    \"left\"\n];\nconst tbPlacement = [\n    \"top\",\n    \"bottom\"\n];\nconst btPlacement = [\n    \"bottom\",\n    \"top\"\n];\nfunction getSideList(side, isStart, rtl) {\n    switch(side){\n        case \"top\":\n        case \"bottom\":\n            if (rtl) return isStart ? rlPlacement : lrPlacement;\n            return isStart ? lrPlacement : rlPlacement;\n        case \"left\":\n        case \"right\":\n            return isStart ? tbPlacement : btPlacement;\n        default:\n            return [];\n    }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n    const alignment = getAlignment(placement);\n    let list = getSideList(getSide(placement), direction === \"start\", rtl);\n    if (alignment) {\n        list = list.map((side)=>side + \"-\" + alignment);\n        if (flipAlignment) {\n            list = list.concat(list.map(getOppositeAlignmentPlacement));\n        }\n    }\n    return list;\n}\nfunction getOppositePlacement(placement) {\n    return placement.replace(/left|right|bottom|top/g, (side)=>oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n    return {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...padding\n    };\n}\nfunction getPaddingObject(padding) {\n    return typeof padding !== \"number\" ? expandPaddingObject(padding) : {\n        top: padding,\n        right: padding,\n        bottom: padding,\n        left: padding\n    };\n}\nfunction rectToClientRect(rect) {\n    const { x, y, width, height } = rect;\n    return {\n        width,\n        height,\n        top: y,\n        left: x,\n        right: x + width,\n        bottom: y + height,\n        x,\n        y\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@floating-ui+utils@0.2.10/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;