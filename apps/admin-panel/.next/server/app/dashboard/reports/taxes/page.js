/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/reports/taxes/page";
exports.ids = ["app/dashboard/reports/taxes/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?9e3e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?f29d":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freports%2Ftaxes%2Fpage&page=%2Fdashboard%2Freports%2Ftaxes%2Fpage&appPaths=%2Fdashboard%2Freports%2Ftaxes%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&appDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freports%2Ftaxes%2Fpage&page=%2Fdashboard%2Freports%2Ftaxes%2Fpage&appPaths=%2Fdashboard%2Freports%2Ftaxes%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&appDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?c487\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'reports',\n        {\n        children: [\n        'taxes',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/reports/taxes/page.tsx */ \"(rsc)/./src/app/dashboard/reports/taxes/page.tsx\")), \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\")), \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/reports/taxes/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/reports/taxes/page\",\n        pathname: \"/dashboard/reports/taxes\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freports%2Ftaxes%2Fpage&page=%2Fdashboard%2Freports%2Ftaxes%2Fpage&appPaths=%2Fdashboard%2Freports%2Ftaxes%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&appDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMC40X0BiYWJlbCtjb3JlQDcuMjguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZpdmlucm9la2ltYW4lMkZEZXNrdG9wJTJGTnV0cmlQcm8lMkZhcHBzJTJGYWRtaW4tcGFuZWwlMkZzcmMlMkZhcHAlMkZkYXNoYm9hcmQlMkZsYXlvdXQudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8/NzBlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9pdmlucm9la2ltYW4vRGVza3RvcC9OdXRyaVByby9hcHBzL2FkbWluLXBhbmVsL3NyYy9hcHAvZGFzaGJvYXJkL2xheW91dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Flayout.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/reports/taxes/page.tsx */ \"(ssr)/./src/app/dashboard/reports/taxes/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMC40X0BiYWJlbCtjb3JlQDcuMjguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZpdmlucm9la2ltYW4lMkZEZXNrdG9wJTJGTnV0cmlQcm8lMkZhcHBzJTJGYWRtaW4tcGFuZWwlMkZzcmMlMkZhcHAlMkZkYXNoYm9hcmQlMkZyZXBvcnRzJTJGdGF4ZXMlMkZwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvP2ZlOGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvaXZpbnJvZWtpbWFuL0Rlc2t0b3AvTnV0cmlQcm8vYXBwcy9hZG1pbi1wYW5lbC9zcmMvYXBwL2Rhc2hib2FyZC9yZXBvcnRzL3RheGVzL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Flib%2Fauth-context.tsx&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Flib%2Fauth-context.tsx&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/auth-context.tsx */ \"(ssr)/./src/lib/auth-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMC40X0BiYWJlbCtjb3JlQDcuMjguMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGVXNlcnMlMkZpdmlucm9la2ltYW4lMkZEZXNrdG9wJTJGTnV0cmlQcm8lMkZhcHBzJTJGYWRtaW4tcGFuZWwlMkZzcmMlMkZsaWIlMkZhdXRoLWNvbnRleHQudHN4Jm1vZHVsZXM9JTJGVXNlcnMlMkZpdmlucm9la2ltYW4lMkZEZXNrdG9wJTJGTnV0cmlQcm8lMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNC4wLjRfJTQwYmFiZWwlMkJjb3JlJTQwNy4yOC4wX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRlVzZXJzJTJGaXZpbnJvZWtpbWFuJTJGRGVza3RvcCUyRk51dHJpUHJvJTJGYXBwcyUyRmFkbWluLXBhbmVsJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLz8zZjQzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2l2aW5yb2VraW1hbi9EZXNrdG9wL051dHJpUHJvL2FwcHMvYWRtaW4tcGFuZWwvc3JjL2xpYi9hdXRoLWNvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Flib%2Fauth-context.tsx&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.28.0_react-dom%4018.3.1_react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(ssr)/./src/lib/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(ssr)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Reports\",\n        href: \"/dashboard\",\n        icon: \"\\uD83D\\uDCCA\",\n        hasSubmenu: true,\n        submenu: [\n            {\n                name: \"Sales summary\",\n                href: \"/dashboard/reports/sales-summary\",\n                icon: \"\\uD83D\\uDCC8\"\n            },\n            {\n                name: \"Sales by item\",\n                href: \"/dashboard/reports/sales-by-item\",\n                icon: \"\\uD83D\\uDCE6\"\n            },\n            {\n                name: \"Sales by category\",\n                href: \"/dashboard/reports/sales-by-category\",\n                icon: \"\\uD83D\\uDCC2\"\n            },\n            {\n                name: \"Sales by employee\",\n                href: \"/dashboard/reports/sales-by-employee\",\n                icon: \"\\uD83D\\uDC64\"\n            },\n            {\n                name: \"Sales by payment type\",\n                href: \"/dashboard/reports/sales-by-payment\",\n                icon: \"\\uD83D\\uDCB3\"\n            },\n            {\n                name: \"Receipts\",\n                href: \"/dashboard/reports/receipts\",\n                icon: \"\\uD83E\\uDDFE\"\n            },\n            {\n                name: \"Sales by modifier\",\n                href: \"/dashboard/reports/sales-by-modifier\",\n                icon: \"\\uD83D\\uDD27\"\n            },\n            {\n                name: \"Discounts\",\n                href: \"/dashboard/reports/discounts\",\n                icon: \"\\uD83C\\uDFF7️\"\n            },\n            {\n                name: \"Taxes\",\n                href: \"/dashboard/reports/taxes\",\n                icon: \"\\uD83D\\uDCB0\"\n            }\n        ]\n    },\n    {\n        name: \"Inventory\",\n        href: \"/dashboard/inventory\",\n        icon: \"\\uD83D\\uDCE6\"\n    },\n    {\n        name: \"Clients\",\n        href: \"/dashboard/clients\",\n        icon: \"\\uD83D\\uDC65\"\n    },\n    {\n        name: \"Retail\",\n        href: \"/dashboard/retail\",\n        icon: \"\\uD83D\\uDED2\"\n    },\n    {\n        name: \"Wholesale\",\n        href: \"/dashboard/wholesale\",\n        icon: \"\\uD83C\\uDFE2\"\n    },\n    {\n        name: \"Purchase Orders\",\n        href: \"/dashboard/purchase-orders\",\n        icon: \"\\uD83D\\uDE9A\"\n    },\n    {\n        name: \"Coaches\",\n        href: \"/dashboard/coaches\",\n        icon: \"\\uD83C\\uDFC3‍♂️\"\n    },\n    {\n        name: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: \"⚙️\"\n    }\n];\nfunction DashboardLayout({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, signOut } = (0,_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [expandedMenus, setExpandedMenus] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([\n        \"Reports\"\n    ]) // Reports expanded by default\n    ;\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n        } catch (error) {\n            console.error(\"Sign out failed:\", error);\n        }\n    };\n    const toggleSubmenu = (menuName)=>{\n        setExpandedMenus((prev)=>prev.includes(menuName) ? prev.filter((name)=>name !== menuName) : [\n                ...prev,\n                menuName\n            ]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden md:flex md:flex-col md:w-64 bg-white shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-bold text-white\",\n                            children: \"NutriPro Admin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 px-4 py-6 space-y-1\",\n                        children: navigation.map((item)=>{\n                            const isActive = pathname === item.href;\n                            const isExpanded = expandedMenus.includes(item.name);\n                            const hasActiveSubmenu = item.submenu?.some((subItem)=>pathname === subItem.href);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    item.hasSubmenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleSubmenu(item.name),\n                                        className: `w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${isActive || hasActiveSubmenu ? \"bg-blue-50 text-blue-700 border-r-2 border-blue-700\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-lg\",\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 21\n                                            }, this),\n                                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: `flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${isActive ? \"bg-blue-50 text-blue-700 border-r-2 border-blue-700\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-lg\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.hasSubmenu && isExpanded && item.submenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-4 mt-1 space-y-1\",\n                                        children: item.submenu.map((subItem)=>{\n                                            const isSubActive = pathname === subItem.href;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: subItem.href,\n                                                className: `flex items-center px-4 py-2 text-sm rounded-lg transition-all duration-200 ${isSubActive ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-3 text-base\",\n                                                        children: subItem.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    subItem.name\n                                                ]\n                                            }, subItem.name, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                            children: user?.email || \"Demo User\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: \"Administrator\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleSignOut,\n                                    className: \"ml-3 flex-shrink-0\",\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden bg-white shadow-sm border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between px-4 py-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"NutriPro Admin\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: handleSignOut,\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto bg-gray-50\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/reports/taxes/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/reports/taxes/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TaxesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_nutripro_ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent!=!@nutripro/ui */ \"(ssr)/__barrel_optimize__?names=Card,CardContent!=!../../packages/ui/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction TaxesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                            children: \"Taxes\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Tax collection and reporting\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_nutripro_ui__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                    className: \"border-blue-200 bg-blue-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_nutripro_ui__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl mr-3\",\n                                    children: \"\\uD83D\\uDEA7\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-900\",\n                                            children: \"Report Coming Soon\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-700 text-sm mt-1\",\n                                            children: \"Tax collection summary and detailed reporting.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/reports/taxes/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth-context.tsx":
/*!**********************************!*\
  !*** ./src/lib/auth-context.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nutripro_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutripro/auth */ \"(ssr)/../../packages/auth/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Check if we have valid Supabase configuration\n    const hasValidConfig =  true && \"https://teynwtqgdtnwjfxvfbav.supabase.co\" !== \"https://placeholder.supabase.co\";\n    const supabase = hasValidConfig ? (0,_nutripro_auth__WEBPACK_IMPORTED_MODULE_2__.getAuthClient)() : null;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!hasValidConfig) {\n            // If no valid Supabase config, just set loading to false\n            setLoading(false);\n            setError(\"Supabase not configured - using demo mode\");\n            return;\n        }\n        // Get initial session\n        const getInitialSession = async ()=>{\n            try {\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setError(error.message);\n                } else {\n                    setUser(session?.user || null);\n                }\n            } catch (err) {\n                console.error(\"Error in getInitialSession:\", err);\n                setError(\"Failed to get session\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.email);\n            setUser(session?.user || null);\n            setLoading(false);\n            setError(null);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        supabase,\n        hasValidConfig\n    ]);\n    const signIn = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!hasValidConfig) {\n                // Demo mode - simulate successful login\n                setTimeout(()=>{\n                    setUser({\n                        id: \"demo-user\",\n                        email: credentials.email,\n                        role: \"admin\"\n                    });\n                    setLoading(false);\n                }, 1000);\n                return;\n            }\n            const { error } = await supabase.auth.signInWithPassword({\n                email: credentials.email,\n                password: credentials.password\n            });\n            if (error) {\n                setError(error.message);\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"Sign in error:\", err);\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signUp = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const { error } = await supabase.auth.signUp({\n                email: credentials.email,\n                password: credentials.password,\n                options: {\n                    data: {\n                        role: credentials.role || \"staff\"\n                    }\n                }\n            });\n            if (error) {\n                setError(error.message);\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"Sign up error:\", err);\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            if (!hasValidConfig) {\n                // Demo mode - simulate sign out\n                setUser(null);\n                setLoading(false);\n                return;\n            }\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                setError(error.message);\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"Sign out error:\", err);\n            throw err;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (email)=>{\n        try {\n            setError(null);\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: `${window.location.origin}/auth/reset-password`\n            });\n            if (error) {\n                setError(error.message);\n                throw error;\n            }\n        } catch (err) {\n            console.error(\"Reset password error:\", err);\n            throw err;\n        }\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/lib/auth-context.tsx\",\n        lineNumber: 177,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts":
/*!*************************************************************************!*\
  !*** __barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts ***!
  \*************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* reexport safe */ _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_button_tsx__WEBPACK_IMPORTED_MODULE_0__.Button)\n/* harmony export */ });\n/* harmony import */ var _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_button_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../packages/ui/src/components/ui/button.tsx */ \"(ssr)/../../packages/ui/src/components/ui/button.tsx\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CdXR0b24hPSEuLi8uLi9wYWNrYWdlcy91aS9zcmMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFDc0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vcGFja2FnZXMvdWkvc3JjL2luZGV4LnRzP2MyZDEiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBCdXR0b24gfSBmcm9tIFwiL1VzZXJzL2l2aW5yb2VraW1hbi9EZXNrdG9wL051dHJpUHJvL3BhY2thZ2VzL3VpL3NyYy9jb21wb25lbnRzL3VpL2J1dHRvbi50c3hcIiJdLCJuYW1lcyI6WyJCdXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=Card,CardContent!=!../../packages/ui/src/index.ts":
/*!***********************************************************************************!*\
  !*** __barrel_optimize__?names=Card,CardContent!=!../../packages/ui/src/index.ts ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* reexport safe */ _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_card_tsx__WEBPACK_IMPORTED_MODULE_0__.Card),\n/* harmony export */   CardContent: () => (/* reexport safe */ _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_card_tsx__WEBPACK_IMPORTED_MODULE_0__.CardContent)\n/* harmony export */ });\n/* harmony import */ var _Users_ivinroekiman_Desktop_NutriPro_packages_ui_src_components_ui_card_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../packages/ui/src/components/ui/card.tsx */ \"(ssr)/../../packages/ui/src/components/ui/card.tsx\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DYXJkLENhcmRDb250ZW50IT0hLi4vLi4vcGFja2FnZXMvdWkvc3JjL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUNrRztBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL3BhY2thZ2VzL3VpL3NyYy9pbmRleC50cz9jMmQxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgQ2FyZCB9IGZyb20gXCIvVXNlcnMvaXZpbnJvZWtpbWFuL0Rlc2t0b3AvTnV0cmlQcm8vcGFja2FnZXMvdWkvc3JjL2NvbXBvbmVudHMvdWkvY2FyZC50c3hcIlxuZXhwb3J0IHsgQ2FyZENvbnRlbnQgfSBmcm9tIFwiL1VzZXJzL2l2aW5yb2VraW1hbi9EZXNrdG9wL051dHJpUHJvL3BhY2thZ2VzL3VpL3NyYy9jb21wb25lbnRzL3VpL2NhcmQudHN4XCIiXSwibmFtZXMiOlsiQ2FyZCIsIkNhcmRDb250ZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=Card,CardContent!=!../../packages/ui/src/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/src/client.ts":
/*!*****************************************!*\
  !*** ../../packages/auth/src/client.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthClient: () => (/* binding */ createAuthClient),\n/* harmony export */   getAuthClient: () => (/* binding */ getAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/../../node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.50.3/node_modules/@supabase/ssr/dist/index.mjs\");\n\nfunction createAuthClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://teynwtqgdtnwjfxvfbav.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRleW53dHFnZHRud2pmeHZmYmF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5MDQ0MzgsImV4cCI6MjA2NzQ4MDQzOH0.oUbKSxySN4P6NzS4TrGOXtGP1kHMVDPc4eVQhkreV-I\");\n}\n// Create a singleton client for browser usage\nlet authClient = null;\nfunction getAuthClient() {\n    if (!authClient) {\n        authClient = createAuthClient();\n    }\n    return authClient;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvYXV0aC9zcmMvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtRDtBQUc1QyxTQUFTQztJQUNkLE9BQU9ELGtFQUFtQkEsQ0FDeEJFLDBDQUFvQyxFQUNwQ0Esa05BQXlDO0FBRTdDO0FBRUEsOENBQThDO0FBQzlDLElBQUlJLGFBQXlEO0FBRXRELFNBQVNDO0lBQ2QsSUFBSSxDQUFDRCxZQUFZO1FBQ2ZBLGFBQWFMO0lBQ2Y7SUFDQSxPQUFPSztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4uLy4uL3BhY2thZ2VzL2F1dGgvc3JjL2NsaWVudC50cz80ZDc0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJ1xuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gJ0BudXRyaXByby9kYXRhYmFzZSdcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUF1dGhDbGllbnQoKSB7XG4gIHJldHVybiBjcmVhdGVCcm93c2VyQ2xpZW50PERhdGFiYXNlPihcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIVxuICApXG59XG5cbi8vIENyZWF0ZSBhIHNpbmdsZXRvbiBjbGllbnQgZm9yIGJyb3dzZXIgdXNhZ2VcbmxldCBhdXRoQ2xpZW50OiBSZXR1cm5UeXBlPHR5cGVvZiBjcmVhdGVBdXRoQ2xpZW50PiB8IG51bGwgPSBudWxsXG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRBdXRoQ2xpZW50KCkge1xuICBpZiAoIWF1dGhDbGllbnQpIHtcbiAgICBhdXRoQ2xpZW50ID0gY3JlYXRlQXV0aENsaWVudCgpXG4gIH1cbiAgcmV0dXJuIGF1dGhDbGllbnRcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQXV0aENsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImF1dGhDbGllbnQiLCJnZXRBdXRoQ2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/src/client.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/src/index.ts":
/*!****************************************!*\
  !*** ../../packages/auth/src/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createAuthClient),\n/* harmony export */   getAuthClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.getAuthClient)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/../../packages/auth/src/client.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(ssr)/../../packages/auth/src/types.ts\");\n// Export auth utilities\n\n // Server-side exports should be imported directly from './server'\n // to avoid importing server-only code in client components\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvYXV0aC9zcmMvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHdCQUF3QjtBQUNBO0FBQ0QsQ0FFdkIsa0VBQWtFO0NBQ2xFLDJEQUEyRCIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9wYWNrYWdlcy9hdXRoL3NyYy9pbmRleC50cz9iZmIzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydCBhdXRoIHV0aWxpdGllc1xuZXhwb3J0ICogZnJvbSAnLi9jbGllbnQnXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJ1xuXG4vLyBTZXJ2ZXItc2lkZSBleHBvcnRzIHNob3VsZCBiZSBpbXBvcnRlZCBkaXJlY3RseSBmcm9tICcuL3NlcnZlcidcbi8vIHRvIGF2b2lkIGltcG9ydGluZyBzZXJ2ZXItb25seSBjb2RlIGluIGNsaWVudCBjb21wb25lbnRzXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/src/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/auth/src/types.ts":
/*!****************************************!*\
  !*** ../../packages/auth/src/types.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvYXV0aC9zcmMvdHlwZXMudHMiLCJtYXBwaW5ncyI6IjtBQTRCQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BudXRyaXByby9hZG1pbi1wYW5lbC8uLi8uLi9wYWNrYWdlcy9hdXRoL3NyYy90eXBlcy50cz8wMzU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgVXNlciBhcyBTdXBhYmFzZVVzZXIgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbmV4cG9ydCB0eXBlIFVzZXJSb2xlID0gJ2FkbWluJyB8ICdtYW5hZ2VyJyB8ICdzdGFmZicgfCAndmlld2VyJ1xuXG5leHBvcnQgaW50ZXJmYWNlIFVzZXIgZXh0ZW5kcyBTdXBhYmFzZVVzZXIge1xuICByb2xlPzogVXNlclJvbGU7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aFN0YXRlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIExvZ2luQ3JlZGVudGlhbHMge1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNpZ25VcENyZWRlbnRpYWxzIGV4dGVuZHMgTG9naW5DcmVkZW50aWFscyB7XG4gIHJvbGU/OiBVc2VyUm9sZTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUgZXh0ZW5kcyBBdXRoU3RhdGUge1xuICBzaWduSW46IChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgc2lnblVwOiAoY3JlZGVudGlhbHM6IFNpZ25VcENyZWRlbnRpYWxzKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBzaWduT3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICByZXNldFBhc3N3b3JkOiAoZW1haWw6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPjtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/auth/src/types.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/ui/src/components/ui/button.tsx":
/*!******************************************************!*\
  !*** ../../packages/ui/src/components/ui/button.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/../../packages/ui/src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui/src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui/src/components/ui/card.tsx":
/*!****************************************************!*\
  !*** ../../packages/ui/src/components/ui/card.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/../../packages/ui/src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/packages/ui/src/components/ui/card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui/src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/../../packages/ui/src/lib/utils.ts":
/*!******************************************!*\
  !*** ../../packages/ui/src/lib/utils.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvdWkvc3JjL2xpYi91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AbnV0cmlwcm8vYWRtaW4tcGFuZWwvLi4vLi4vcGFja2FnZXMvdWkvc3JjL2xpYi91dGlscy50cz9kOTMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/ui/src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83YjgxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjk0MGM3N2M5MDU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/dashboard/reports/taxes/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/reports/taxes/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/dashboard/reports/taxes/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./src/lib/auth-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"NutriPro Admin Panel\",\n    description: \"Complete business management dashboard for NutriPro nutrition store\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGdCO0FBQzJCO0FBSTFDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsMkRBQVlBOzBCQUNWSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQG51dHJpcHJvL2FkbWluLXBhbmVsLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tICdAL2xpYi9hdXRoLWNvbnRleHQnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdOdXRyaVBybyBBZG1pbiBQYW5lbCcsXG4gIGRlc2NyaXB0aW9uOiAnQ29tcGxldGUgYnVzaW5lc3MgbWFuYWdlbWVudCBkYXNoYm9hcmQgZm9yIE51dHJpUHJvIG51dHJpdGlvbiBzdG9yZScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJBdXRoUHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-context.tsx":
/*!**********************************!*\
  !*** ./src/lib/auth-context.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/lib/auth-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/lib/auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/lib/auth-context.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1","vendor-chunks/tr46@0.0.3","vendor-chunks/@supabase+auth-js@2.70.0","vendor-chunks/ws@8.18.3","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/@supabase+realtime-js@2.11.15","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@supabase+supabase-js@2.50.3","vendor-chunks/lucide-react@0.303.0_react@18.3.1","vendor-chunks/@supabase+ssr@0.1.0_@supabase+supabase-js@2.50.3","vendor-chunks/@supabase+functions-js@2.4.5","vendor-chunks/ramda@0.29.1","vendor-chunks/cookie@0.5.0","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/@swc+helpers@0.5.2","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/isows@1.0.7_ws@8.18.3","vendor-chunks/clsx@2.1.1"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Freports%2Ftaxes%2Fpage&page=%2Fdashboard%2Freports%2Ftaxes%2Fpage&appPaths=%2Fdashboard%2Freports%2Ftaxes%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Freports%2Ftaxes%2Fpage.tsx&appDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fivinroekiman%2FDesktop%2FNutriPro%2Fapps%2Fadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();