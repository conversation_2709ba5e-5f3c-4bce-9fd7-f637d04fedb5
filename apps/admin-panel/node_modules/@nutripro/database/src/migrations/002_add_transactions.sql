-- Add Transaction Tables Migration
-- This adds the transactions and transaction_items tables to support order management

-- Add transaction_type enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE transaction_type AS ENUM ('sale', 'return', 'wholesale_order', 'adjustment');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_number varchar(20) UNIQUE NOT NULL,
  
  -- References
  customer_id uuid REFERENCES customers(id),
  staff_id uuid, -- Reference to auth.users
  
  -- Transaction Details
  transaction_type transaction_type NOT NULL,
  status transaction_status DEFAULT 'pending',
  
  -- Financial
  subtotal decimal(10,2) NOT NULL,
  tax_rate decimal(5,4) DEFAULT 0,
  tax_amount decimal(10,2) DEFAULT 0,
  discount_amount decimal(10,2) DEFAULT 0,
  total_amount decimal(10,2) NOT NULL,
  
  -- Payment
  payment_method payment_method,
  payment_reference varchar(100),
  
  -- Additional Info
  notes text,
  internal_notes text,
  
  -- Offline sync support
  synced_at timestamptz,
  sync_version integer DEFAULT 1,
  
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Transaction Items table
CREATE TABLE IF NOT EXISTS transaction_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid REFERENCES transactions(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id),
  
  -- Item Details
  quantity integer NOT NULL CHECK (quantity > 0),
  unit_price decimal(10,2) NOT NULL,
  discount_amount decimal(10,2) DEFAULT 0,
  line_total decimal(10,2) NOT NULL,
  
  -- Product Info (snapshot for historical accuracy)
  product_name varchar(200) NOT NULL,
  product_sku varchar(50) NOT NULL,
  
  -- Batch tracking (for future FIFO implementation)
  batch_number varchar(50),
  expiry_date date,
  
  created_at timestamptz DEFAULT now()
);

-- Create indexes for transactions
CREATE INDEX IF NOT EXISTS idx_transactions_number ON transactions(transaction_number);
CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_staff ON transactions(staff_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_sync ON transactions(synced_at);

-- Create indexes for transaction items
CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id);
CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id);
CREATE INDEX IF NOT EXISTS idx_transaction_items_sku ON transaction_items(product_sku);

-- Add foreign key constraint for coaches if it doesn't exist
DO $$ BEGIN
    ALTER TABLE customers ADD CONSTRAINT fk_customers_coach 
      FOREIGN KEY (assigned_coach_id) REFERENCES coaches(id);
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add updated_at trigger for transactions
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
