import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Customer = Database['public']['Tables']['customers']['Row']
type CustomerInsert = Database['public']['Tables']['customers']['Insert']
type CustomerUpdate = Database['public']['Tables']['customers']['Update']

export class CustomerQueries {
  private supabase = createClient()

  async getAll() {
    const { data, error } = await this.supabase
      .from('customers')
      .select(`
        *,
        coaches (id, first_name, last_name)
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('customers')
      .select(`
        *,
        coaches (id, first_name, last_name, email)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async getByType(type: 'retail' | 'wholesale') {
    const { data, error } = await this.supabase
      .from('customers')
      .select(`
        *,
        coaches (id, first_name, last_name)
      `)
      .eq('customer_type', type)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async getByCoach(coachId: string) {
    const { data, error } = await this.supabase
      .from('customers')
      .select('*')
      .eq('assigned_coach_id', coachId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data
  }

  async search(query: string) {
    const { data, error } = await this.supabase
      .from('customers')
      .select(`
        *,
        coaches (id, first_name, last_name)
      `)
      .or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%,company_name.ilike.%${query}%`)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(50)

    if (error) throw error
    return data
  }

  async create(customer: CustomerInsert) {
    const { data, error } = await this.supabase
      .from('customers')
      .insert(customer)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async update(id: string, updates: CustomerUpdate) {
    const { data, error } = await this.supabase
      .from('customers')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateLoyaltyPoints(id: string, points: number) {
    const { data, error } = await this.supabase
      .from('customers')
      .update({ loyalty_points: points })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateStoreCredit(id: string, credit: number) {
    const { data, error } = await this.supabase
      .from('customers')
      .update({ store_credit: credit })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getStats() {
    const { data: totalCustomers, error: totalError } = await this.supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('is_active', true)

    const { data: retailCustomers, error: retailError } = await this.supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('customer_type', 'retail')
      .eq('is_active', true)

    const { data: wholesaleCustomers, error: wholesaleError } = await this.supabase
      .from('customers')
      .select('id', { count: 'exact' })
      .eq('customer_type', 'wholesale')
      .eq('is_active', true)

    if (totalError || retailError || wholesaleError) {
      throw totalError || retailError || wholesaleError
    }

    return {
      total: totalCustomers?.length || 0,
      retail: retailCustomers?.length || 0,
      wholesale: wholesaleCustomers?.length || 0
    }
  }
}
