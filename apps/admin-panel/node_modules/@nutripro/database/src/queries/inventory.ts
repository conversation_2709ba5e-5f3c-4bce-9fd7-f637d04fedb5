import { createClient } from '../client'
import type { Database } from '../types/supabase'

type ProductBatch = Database['public']['Tables']['product_batches']['Row']
type ProductBatchInsert = Database['public']['Tables']['product_batches']['Insert']
type ProductBatchUpdate = Database['public']['Tables']['product_batches']['Update']

export interface ProductBatchWithProduct extends ProductBatch {
  products?: {
    id: string
    name: string
    sku: string
    stock_quantity: number
  }
}

export interface InventoryAlert {
  id: string
  product_id: string
  product_name: string
  sku: string
  alert_type: 'low_stock' | 'expiry_warning' | 'out_of_stock'
  current_stock: number
  min_stock: number
  days_until_expiry?: number
  batch_number?: string
  severity: 'high' | 'medium' | 'low'
}

export interface FIFOBatch {
  batch_id: string
  batch_number: string
  available_quantity: number
  expiry_date: string
  unit_cost: number
}

export class InventoryQueries {
  private supabase = createClient()

  // Product Batch Management
  async getAllBatches(limit = 100, offset = 0) {
    const { data, error } = await this.supabase
      .from('product_batches')
      .select(`
        *,
        products (
          id,
          name,
          sku,
          stock_quantity
        )
      `)
      .order('expiry_date', { ascending: true })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return data as ProductBatchWithProduct[]
  }

  async getBatchesByProduct(productId: string) {
    const { data, error } = await this.supabase
      .from('product_batches')
      .select('*')
      .eq('product_id', productId)
      .eq('status', 'active')
      .order('fifo_priority', { ascending: true })

    if (error) throw error
    return data as ProductBatch[]
  }

  async getExpiringBatches(daysAhead = 30) {
    const futureDate = new Date()
    futureDate.setDate(futureDate.getDate() + daysAhead)

    const { data, error } = await this.supabase
      .from('product_batches')
      .select(`
        *,
        products (
          id,
          name,
          sku
        )
      `)
      .lte('expiry_date', futureDate.toISOString().split('T')[0])
      .eq('status', 'active')
      .gt('quantity_available', 0)
      .order('expiry_date', { ascending: true })

    if (error) throw error
    return data as ProductBatchWithProduct[]
  }

  async createBatch(batchData: Omit<ProductBatchInsert, 'id' | 'created_at' | 'updated_at'>) {
    // Get the next FIFO priority for this product
    const { data: lastBatch, error: priorityError } = await this.supabase
      .from('product_batches')
      .select('fifo_priority')
      .eq('product_id', batchData.product_id)
      .order('fifo_priority', { ascending: false })
      .limit(1)
      .single()

    const nextPriority = lastBatch ? lastBatch.fifo_priority + 1 : 1

    const { data, error } = await this.supabase
      .from('product_batches')
      .insert({
        ...batchData,
        fifo_priority: nextPriority
      })
      .select()
      .single()

    if (error) throw error
    return data as ProductBatch
  }

  async updateBatch(id: string, updates: ProductBatchUpdate) {
    const { data, error } = await this.supabase
      .from('product_batches')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as ProductBatch
  }

  // FIFO Batch Selection
  async getFIFOBatches(productId: string, quantityNeeded: number): Promise<FIFOBatch[]> {
    const { data, error } = await this.supabase
      .from('product_batches')
      .select('id, batch_number, quantity_available, expiry_date, unit_cost')
      .eq('product_id', productId)
      .eq('status', 'active')
      .gt('quantity_available', 0)
      .gt('expiry_date', new Date().toISOString().split('T')[0])
      .order('fifo_priority', { ascending: true })
      .order('expiry_date', { ascending: true })

    if (error) throw error

    const batches: FIFOBatch[] = []
    let remainingQuantity = quantityNeeded

    for (const batch of data) {
      if (remainingQuantity <= 0) break

      const quantityFromBatch = Math.min(batch.quantity_available, remainingQuantity)
      
      batches.push({
        batch_id: batch.id,
        batch_number: batch.batch_number,
        available_quantity: quantityFromBatch,
        expiry_date: batch.expiry_date,
        unit_cost: batch.unit_cost
      })

      remainingQuantity -= quantityFromBatch
    }

    return batches
  }

  // Inventory Alerts
  async getInventoryAlerts(): Promise<InventoryAlert[]> {
    const alerts: InventoryAlert[] = []

    // Get low stock alerts
    const { data: lowStockProducts, error: lowStockError } = await this.supabase
      .from('products')
      .select('id, name, sku, stock_quantity, min_stock_level')
      .lte('stock_quantity', this.supabase.rpc('min_stock_level'))

    if (!lowStockError && lowStockProducts) {
      for (const product of lowStockProducts) {
        alerts.push({
          id: `low-stock-${product.id}`,
          product_id: product.id,
          product_name: product.name,
          sku: product.sku,
          alert_type: product.stock_quantity === 0 ? 'out_of_stock' : 'low_stock',
          current_stock: product.stock_quantity,
          min_stock: product.min_stock_level,
          severity: product.stock_quantity === 0 ? 'high' : 
                   product.stock_quantity <= product.min_stock_level / 2 ? 'medium' : 'low'
        })
      }
    }

    // Get expiry warnings
    const expiringBatches = await this.getExpiringBatches(30)
    for (const batch of expiringBatches) {
      const daysUntilExpiry = Math.ceil(
        (new Date(batch.expiry_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      )

      alerts.push({
        id: `expiry-${batch.id}`,
        product_id: batch.product_id!,
        product_name: batch.products?.name || 'Unknown Product',
        sku: batch.products?.sku || 'Unknown SKU',
        alert_type: 'expiry_warning',
        current_stock: batch.quantity_available,
        min_stock: 0,
        days_until_expiry: daysUntilExpiry,
        batch_number: batch.batch_number,
        severity: daysUntilExpiry <= 7 ? 'high' : daysUntilExpiry <= 14 ? 'medium' : 'low'
      })
    }

    return alerts
  }

  // Stock Movements
  async recordStockMovement(data: {
    product_id: string
    movement_type: string
    quantity: number
    reference_id?: string
    reference_type?: string
  }) {
    // Get current stock
    const { data: product, error: productError } = await this.supabase
      .from('products')
      .select('stock_quantity')
      .eq('id', data.product_id)
      .single()

    if (productError) throw productError

    const stockBefore = product.stock_quantity
    const stockAfter = stockBefore + data.quantity

    // Record the movement
    const { data: movement, error: movementError } = await this.supabase
      .from('stock_movements')
      .insert({
        product_id: data.product_id,
        movement_type: data.movement_type,
        quantity: data.quantity,
        reference_id: data.reference_id,
        reference_type: data.reference_type,
        stock_before: stockBefore,
        stock_after: stockAfter
      })
      .select()
      .single()

    if (movementError) throw movementError

    // Update product stock
    await this.supabase
      .from('products')
      .update({ stock_quantity: Math.max(0, stockAfter) })
      .eq('id', data.product_id)

    return movement
  }

  // Inventory Adjustments
  async createAdjustment(data: {
    product_id: string
    adjustment_type: string
    quantity_change: number
    reason?: string
    notes?: string
    unit_cost?: number
  }) {
    const { data: adjustment, error } = await this.supabase
      .from('inventory_adjustments')
      .insert({
        product_id: data.product_id,
        adjustment_type: data.adjustment_type,
        quantity_change: data.quantity_change,
        reason: data.reason,
        notes: data.notes,
        unit_cost: data.unit_cost,
        total_cost: data.unit_cost ? data.unit_cost * Math.abs(data.quantity_change) : null
      })
      .select()
      .single()

    if (error) throw error

    // Record stock movement
    await this.recordStockMovement({
      product_id: data.product_id,
      movement_type: 'adjustment',
      quantity: data.quantity_change,
      reference_id: adjustment.id,
      reference_type: 'adjustment'
    })

    return adjustment
  }

  async getAdjustmentHistory(productId?: string, limit = 50) {
    let query = this.supabase
      .from('inventory_adjustments')
      .select(`
        *,
        products (
          id,
          name,
          sku
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (productId) {
      query = query.eq('product_id', productId)
    }

    const { data, error } = await query

    if (error) throw error
    return data
  }

  // FIFO Batch Processing for Sales
  async processFIFOSale(productId: string, quantityNeeded: number) {
    const batches = await this.getFIFOBatches(productId, quantityNeeded)
    const batchMovements = []

    for (const batch of batches) {
      // Update batch quantity
      await this.supabase
        .from('product_batches')
        .update({
          quantity_available: this.supabase.rpc('quantity_available') - batch.available_quantity,
          quantity_sold: this.supabase.rpc('quantity_sold') + batch.available_quantity
        })
        .eq('id', batch.batch_id)

      // Record batch movement
      batchMovements.push({
        batch_id: batch.batch_id,
        movement_type: 'sold',
        quantity: batch.available_quantity,
        unit_cost: batch.unit_cost
      })
    }

    return batchMovements
  }

  // Batch Receiving (from Purchase Orders)
  async receiveBatch(data: {
    product_id: string
    batch_number: string
    expiry_date: string
    quantity_received: number
    unit_cost: number
    purchase_order_id?: string
  }) {
    // Get next FIFO priority
    const { data: lastBatch } = await this.supabase
      .from('product_batches')
      .select('fifo_priority')
      .eq('product_id', data.product_id)
      .order('fifo_priority', { ascending: false })
      .limit(1)
      .single()

    const nextPriority = lastBatch ? lastBatch.fifo_priority + 1 : 1

    // Create new batch
    const batch = await this.createBatch({
      product_id: data.product_id,
      batch_number: data.batch_number,
      expiry_date: data.expiry_date,
      received_date: new Date().toISOString().split('T')[0],
      quantity_received: data.quantity_received,
      quantity_available: data.quantity_received,
      quantity_sold: 0,
      quantity_expired: 0,
      quantity_returned: 0,
      unit_cost: data.unit_cost,
      total_cost: data.unit_cost * data.quantity_received,
      fifo_priority: nextPriority,
      status: 'active'
    })

    // Record batch movement
    await this.supabase
      .from('batch_movements')
      .insert({
        batch_id: batch.id,
        movement_type: 'received',
        quantity: data.quantity_received,
        unit_cost: data.unit_cost,
        reference_number: data.purchase_order_id ? `PO-${data.purchase_order_id}` : null
      })

    return batch
  }

  // Inventory Statistics
  async getInventoryStats() {
    // Total products
    const { data: totalProducts, error: totalError } = await this.supabase
      .from('products')
      .select('id', { count: 'exact' })

    // Low stock products
    const { data: lowStockProducts, error: lowStockError } = await this.supabase
      .from('products')
      .select('id', { count: 'exact' })
      .lte('stock_quantity', this.supabase.rpc('min_stock_level'))

    // Out of stock products
    const { data: outOfStockProducts, error: outOfStockError } = await this.supabase
      .from('products')
      .select('id', { count: 'exact' })
      .eq('stock_quantity', 0)

    // Expiring batches (next 30 days)
    const expiringBatches = await this.getExpiringBatches(30)

    if (totalError || lowStockError || outOfStockError) {
      throw totalError || lowStockError || outOfStockError
    }

    return {
      totalProducts: totalProducts?.length || 0,
      lowStockProducts: lowStockProducts?.length || 0,
      outOfStockProducts: outOfStockProducts?.length || 0,
      expiringBatches: expiringBatches.length
    }
  }
}
