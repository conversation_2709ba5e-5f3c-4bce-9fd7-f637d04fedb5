-- NutriPro Sample Data for Development
-- This creates sample data for testing the application

-- Insert sample categories
INSERT INTO categories (name, description, sort_order) VALUES
('Protein Supplements', 'Whey, casein, and plant-based protein powders', 1),
('Pre-Workout', 'Energy and performance boosters', 2),
('Post-Workout', 'Recovery and muscle building supplements', 3),
('Vitamins & Minerals', 'Essential vitamins and mineral supplements', 4),
('Weight Management', 'Fat burners and weight loss supplements', 5),
('Health & Wellness', 'General health and wellness products', 6);

-- Insert sample brands
INSERT INTO brands (name, description, website_url) VALUES
('Optimum Nutrition', 'Leading sports nutrition brand', 'https://www.optimumnutrition.com'),
('MuscleTech', 'Science-driven sports nutrition', 'https://www.muscletech.com'),
('BSN', 'Bold Sports Nutrition', 'https://www.bsn.com'),
('Dymatize', 'Premium sports nutrition', 'https://www.dymatize.com'),
('Quest Nutrition', 'Clean label nutrition', 'https://www.questnutrition.com'),
('NOW Foods', 'Natural health products', 'https://www.nowfoods.com');

-- Insert sample vendors
INSERT INTO vendors (name, contact_person, email, phone, preferred_currency, payment_terms) VALUES
('NutriSource USA', 'John Smith', '<EMAIL>', '******-0123', 'USD', 'Net 30'),
('European Supplements', 'Maria Garcia', '<EMAIL>', '+31-20-1234567', 'EUR', 'Net 15'),
('Caribbean Distributors', 'Carlos Rodriguez', '<EMAIL>', '+************', 'AWG', 'Net 30'),
('Global Nutrition Inc', 'Sarah Johnson', '<EMAIL>', '+1-************', 'USD', 'Net 45');

-- Insert sample products
INSERT INTO products (
  sku, name, description, category_id, brand_id, vendor_id,
  retail_price, wholesale_price, cost_price, purchase_currency,
  stock_quantity, min_stock_level, wholesale_available,
  serving_size, servings_per_container, has_variants, variant_type
) VALUES
(
  'ON-WHEY-5LB',
  'Gold Standard 100% Whey Protein 5lbs',
  'The world''s best-selling whey protein powder',
  (SELECT id FROM categories WHERE name = 'Protein Supplements'),
  (SELECT id FROM brands WHERE name = 'Optimum Nutrition'),
  (SELECT id FROM vendors WHERE name = 'NutriSource USA'),
  189.99, 145.00, 120.00, 'USD',
  25, 5, true,
  '30g', 74, true, 'Flavor'
),
(
  'MT-NITRO-2LB',
  'Nitro-Tech Whey Protein 2lbs',
  'Superior whey protein with creatine',
  (SELECT id FROM categories WHERE name = 'Protein Supplements'),
  (SELECT id FROM brands WHERE name = 'MuscleTech'),
  (SELECT id FROM vendors WHERE name = 'NutriSource USA'),
  89.99, 68.00, 55.00, 'USD',
  40, 10, true,
  '30g', 30, true, 'Flavor'
),
(
  'BSN-AMINO-30',
  'Amino X BCAA Formula',
  'Effervescent BCAA formula for endurance',
  (SELECT id FROM categories WHERE name = 'Post-Workout'),
  (SELECT id FROM brands WHERE name = 'BSN'),
  (SELECT id FROM vendors WHERE name = 'European Supplements'),
  65.99, 48.00, 38.00, 'EUR',
  30, 8, true,
  '14.5g', 30, true, 'Flavor'
),
(
  'NOW-VIT-D3',
  'Vitamin D3 2000 IU',
  'High potency vitamin D3 for bone health',
  (SELECT id FROM categories WHERE name = 'Vitamins & Minerals'),
  (SELECT id FROM brands WHERE name = 'NOW Foods'),
  (SELECT id FROM vendors WHERE name = 'Caribbean Distributors'),
  25.99, 18.00, 12.00, 'AWG',
  100, 20, false,
  '1 softgel', 120, false, null
);

-- Insert sample product variants
INSERT INTO product_variants (product_id, variant_name, variant_value, sku_suffix, stock_quantity) VALUES
-- ON Whey variants
((SELECT id FROM products WHERE sku = 'ON-WHEY-5LB'), 'Flavor', 'Double Rich Chocolate', '-CHOC', 8),
((SELECT id FROM products WHERE sku = 'ON-WHEY-5LB'), 'Flavor', 'Vanilla Ice Cream', '-VAN', 6),
((SELECT id FROM products WHERE sku = 'ON-WHEY-5LB'), 'Flavor', 'Strawberry', '-STRAW', 4),
((SELECT id FROM products WHERE sku = 'ON-WHEY-5LB'), 'Flavor', 'Cookies & Cream', '-COOK', 7),

-- MuscleTech variants
((SELECT id FROM products WHERE sku = 'MT-NITRO-2LB'), 'Flavor', 'Chocolate', '-CHOC', 15),
((SELECT id FROM products WHERE sku = 'MT-NITRO-2LB'), 'Flavor', 'Vanilla', '-VAN', 12),
((SELECT id FROM products WHERE sku = 'MT-NITRO-2LB'), 'Flavor', 'Strawberry', '-STRAW', 13),

-- BSN Amino X variants
((SELECT id FROM products WHERE sku = 'BSN-AMINO-30'), 'Flavor', 'Blue Raspberry', '-BLUE', 10),
((SELECT id FROM products WHERE sku = 'BSN-AMINO-30'), 'Flavor', 'Watermelon', '-WATER', 8),
((SELECT id FROM products WHERE sku = 'BSN-AMINO-30'), 'Flavor', 'Fruit Punch', '-FRUIT', 12);

-- Insert sample coaches
INSERT INTO coaches (first_name, last_name, email, phone, monthly_credit_amount, referral_percentage) VALUES
('Mike', 'Johnson', '<EMAIL>', '+************', 500.00, 8.5),
('Sarah', 'Williams', '<EMAIL>', '+************', 750.00, 10.0),
('Carlos', 'Martinez', '<EMAIL>', '+************', 400.00, 7.5),
('Lisa', 'Thompson', '<EMAIL>', '+************', 600.00, 9.0);

-- Insert sample customers
INSERT INTO customers (
  customer_type, first_name, last_name, email, phone,
  address_line1, city, postal_code, loyalty_points, store_credit,
  membership_tier, assigned_coach_id
) VALUES
(
  'retail', 'John', 'Doe', '<EMAIL>', '+************',
  'Main Street 123', 'Oranjestad', '12345', 150, 25.50,
  'regular', (SELECT id FROM coaches WHERE email = '<EMAIL>')
),
(
  'retail', 'Maria', 'Santos', '<EMAIL>', '+************',
  'Palm Beach Road 456', 'Palm Beach', '12346', 320, 0.00,
  'premium', (SELECT id FROM coaches WHERE email = '<EMAIL>')
),
(
  'wholesale', 'Fitness', 'Center Pro', '<EMAIL>', '+************',
  'Industrial Zone 789', 'San Nicolas', '12347', 0, 150.00,
  'regular', null
),
(
  'retail', 'Alex', 'Rodriguez', '<EMAIL>', '+************',
  'Eagle Beach Blvd 321', 'Eagle Beach', '12348', 85, 12.25,
  'regular', (SELECT id FROM coaches WHERE email = '<EMAIL>')
);
