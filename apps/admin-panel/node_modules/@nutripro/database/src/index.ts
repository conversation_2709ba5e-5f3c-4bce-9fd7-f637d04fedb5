// Export database client
export { supabase, createClient } from "./client";

// Export query functions
export { ProductQueries } from "./queries/products";
export { CategoryQueries } from "./queries/categories";
export { BrandQueries } from "./queries/brands";
export { VendorQueries } from "./queries/vendors";
export { CustomerQueries } from "./queries/customers";
export { TransactionQueries } from "./queries/transactions";
export { PurchaseOrderQueries } from "./queries/purchase-orders";
export { InventoryQueries } from "./queries/inventory";

// Export types
export type { TransactionWithItems, CreateTransactionData } from "./queries/transactions";
export type { PurchaseOrderWithItems, CreatePurchaseOrderData } from "./queries/purchase-orders";
export type { ProductBatchWithProduct, InventoryAlert, FIFOBatch } from "./queries/inventory";

// Export types
export * from "./types/supabase";

// Export utilities
export * from "./utils";
