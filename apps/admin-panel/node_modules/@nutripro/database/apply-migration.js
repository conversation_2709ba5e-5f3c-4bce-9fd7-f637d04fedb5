const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '../../apps/admin-panel/.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function applyMigration() {
  try {
    console.log('🚀 Applying transaction tables migration...')
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'src/migrations/002_add_transactions.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
    
    console.log(`📝 Executing ${statements.length} SQL statements...`)
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`  ${i + 1}/${statements.length}: Executing statement...`)
      
      const { error } = await supabase.rpc('exec_sql', { 
        sql: statement 
      })
      
      if (error) {
        console.error(`❌ Error in statement ${i + 1}:`, error)
        console.error('Statement:', statement)
        // Continue with other statements
      } else {
        console.log(`  ✅ Statement ${i + 1} completed`)
      }
    }
    
    console.log('✅ Migration application completed!')
    console.log('📊 Checking if tables were created...')
    
    // Verify tables exist
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['transactions', 'transaction_items'])
    
    if (tablesError) {
      console.error('❌ Error checking tables:', tablesError)
    } else {
      console.log('📋 Tables found:', tables.map(t => t.table_name))
    }
    
  } catch (err) {
    console.error('❌ Error applying migration:', err)
    process.exit(1)
  }
}

applyMigration()
