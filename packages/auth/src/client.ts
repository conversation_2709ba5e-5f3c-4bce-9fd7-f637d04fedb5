import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@nutripro/database'

export function createAuthClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Create a singleton client for browser usage
let authClient: ReturnType<typeof createAuthClient> | null = null

export function getAuthClient() {
  if (!authClient) {
    authClient = createAuthClient()
  }
  return authClient
}
