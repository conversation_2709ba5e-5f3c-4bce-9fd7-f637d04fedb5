#!/usr/bin/env node

// <PERSON>ript to create test orders to demonstrate the new invoice numbering system
const { createClient } = require('@supabase/supabase-js')

// Initialize Supabase client
const supabaseUrl = 'https://teynwtqgdtnwjfxvfbav.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRleW53dHFnZHRud2pmeHZmYmF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU4NTU4NzQsImV4cCI6MjA1MTQzMTg3NH0.Ej4Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
const supabase = createClient(supabaseUrl, supabaseKey)

async function createTestOrders() {
  console.log('🚀 Creating test orders to demonstrate invoice numbering system...\n')

  try {
    // First, get some customers and products
    console.log('📊 Fetching customers and products...')

    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('id, first_name, last_name, company_name, customer_type')
      .limit(4)

    if (customersError) throw customersError

    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, sku, retail_price, wholesale_price')
      .limit(3)

    if (productsError) throw productsError

    console.log(`✅ Found ${customers.length} customers and ${products.length} products\n`)

    // Create test orders
    const testOrders = [
      {
        customer: customers.find(c => c.customer_type === 'retail'),
        products: [products[0]],
        type: 'Retail Order'
      },
      {
        customer: customers.find(c => c.customer_type === 'wholesale'),
        products: [products[1], products[2]],
        type: 'Wholesale Order'
      },
      {
        customer: customers.find(c => c.customer_type === 'retail' && c.id !== customers.find(c => c.customer_type === 'retail')?.id),
        products: [products[0], products[1]],
        type: 'Another Retail Order'
      },
      {
        customer: null, // Walk-in customer
        products: [products[2]],
        type: 'Walk-in Order'
      }
    ]

    for (let i = 0; i < testOrders.length; i++) {
      const order = testOrders[i]
      console.log(`📝 Creating ${order.type}...`)

      // Calculate totals
      const subtotal = order.products.reduce((sum, product) => {
        const price = order.customer?.customer_type === 'wholesale'
          ? (product.wholesale_price || product.retail_price)
          : product.retail_price
        return sum + price
      }, 0)

      const taxRate = 0.0625 // 6.25%
      const taxAmount = subtotal * taxRate
      const total = subtotal + taxAmount

      // Create transaction
      const transactionData = {
        customer_id: order.customer?.id || null,
        transaction_type: order.customer?.customer_type === 'wholesale' ? 'wholesale_order' : 'sale',
        status: 'pending',
        subtotal: subtotal,
        tax_rate: taxRate,
        tax_amount: taxAmount,
        discount_amount: 0,
        total_amount: total,
        payment_method: 'cash',
        notes: `Test ${order.type} - Created by script`
      }

      const { data: newTransaction, error: transactionError } = await supabase
        .from('transactions')
        .insert(transactionData)
        .select()
        .single()

      if (transactionError) throw transactionError

      // Create transaction items
      const items = order.products.map(product => ({
        transaction_id: newTransaction.id,
        product_id: product.id,
        quantity: 1,
        unit_price: order.customer?.customer_type === 'wholesale'
          ? (product.wholesale_price || product.retail_price)
          : product.retail_price,
        discount_amount: 0,
        line_total: order.customer?.customer_type === 'wholesale'
          ? (product.wholesale_price || product.retail_price)
          : product.retail_price,
        product_name: product.name,
        product_sku: product.sku
      }))

      const { error: itemsError } = await supabase
        .from('transaction_items')
        .insert(items)

      if (itemsError) throw itemsError

      const customerName = order.customer
        ? `${order.customer.first_name} ${order.customer.last_name}`.trim() || order.customer.company_name
        : 'Walk-in Customer'

      console.log(`   ✅ Order ${newTransaction.transaction_number} created for ${customerName}`)
      console.log(`      Type: ${newTransaction.transaction_type}`)
      console.log(`      Total: AWG ${total.toFixed(2)}`)
      console.log(`      Status: ${newTransaction.status}\n`)
    }

    console.log('🎉 All test orders created successfully!')
    console.log('\n📋 Next steps:')
    console.log('1. Go to http://localhost:3000/dashboard/orders to view the orders')
    console.log('2. Click "Complete Order" on pending orders to generate invoice numbers')
    console.log('3. Notice the different invoice number formats:')
    console.log('   - Retail orders get RE-202501-00001 format')
    console.log('   - Wholesale orders get WH-202501-00001 format')
    console.log('   - All orders start with OR-202501-00001 format')

  } catch (error) {
    console.error('❌ Error creating test orders:', error)
  }
}

// Run the script
createTestOrders()