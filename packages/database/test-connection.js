// Simple test script to verify database connection
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://teynwtqgdtnwjfxvfbav.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRleW53dHFnZHRud2pmeHZmYmF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5MDQ0MzgsImV4cCI6MjA2NzQ4MDQzOH0.oUbKSxySN4P6NzS4TrGOXtGP1kHMVDPc4eVQhkreV-I'

async function testConnection() {
  console.log('🔗 Testing NutriPro database connection...')
  
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  try {
    // Test basic connection
    console.log('📊 Testing products query...')
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('id, name, sku, retail_price, stock_quantity')
      .limit(5)
    
    if (productsError) {
      console.error('❌ Products query failed:', productsError)
      return
    }
    
    console.log(`✅ Found ${products.length} products:`)
    products.forEach(product => {
      console.log(`  - ${product.name} (${product.sku}) - AWG ${product.retail_price} - Stock: ${product.stock_quantity}`)
    })
    
    // Test customers query
    console.log('\n👥 Testing customers query...')
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('id, first_name, last_name, customer_type, loyalty_points')
      .limit(5)
    
    if (customersError) {
      console.error('❌ Customers query failed:', customersError)
      return
    }
    
    console.log(`✅ Found ${customers.length} customers:`)
    customers.forEach(customer => {
      console.log(`  - ${customer.first_name} ${customer.last_name} (${customer.customer_type}) - ${customer.loyalty_points} points`)
    })
    
    // Test categories query
    console.log('\n📂 Testing categories query...')
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name, description')
      .limit(5)
    
    if (categoriesError) {
      console.error('❌ Categories query failed:', categoriesError)
      return
    }
    
    console.log(`✅ Found ${categories.length} categories:`)
    categories.forEach(category => {
      console.log(`  - ${category.name}: ${category.description}`)
    })
    
    console.log('\n🎉 Database connection test completed successfully!')
    console.log('✅ All queries executed without errors')
    console.log('✅ Sample data is properly loaded')
    console.log('✅ NutriPro database is ready for use!')
    
  } catch (error) {
    console.error('❌ Database connection test failed:', error)
  }
}

testConnection()
