import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Category = Database['public']['Tables']['categories']['Row']
type CategoryInsert = Database['public']['Tables']['categories']['Insert']
type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export class CategoryQueries {
  private supabase = createClient()

  async getAll() {
    const { data, error } = await this.supabase
      .from('categories')
      .select(`
        *,
        parent:categories!parent_id (id, name),
        children:categories!parent_id (id, name, sort_order)
      `)
      .eq('is_active', true)
      .order('sort_order')
      .order('name')

    if (error) throw error
    return data
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('categories')
      .select(`
        *,
        parent:categories!parent_id (id, name),
        children:categories!parent_id (id, name, sort_order)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async getTopLevel() {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .is('parent_id', null)
      .eq('is_active', true)
      .order('sort_order')
      .order('name')

    if (error) throw error
    return data
  }

  async getByParent(parentId: string) {
    const { data, error } = await this.supabase
      .from('categories')
      .select('*')
      .eq('parent_id', parentId)
      .eq('is_active', true)
      .order('sort_order')
      .order('name')

    if (error) throw error
    return data
  }

  async search(query: string) {
    const { data, error } = await this.supabase
      .from('categories')
      .select(`
        *,
        parent:categories!parent_id (id, name)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name')
      .limit(50)

    if (error) throw error
    return data
  }

  async create(category: CategoryInsert) {
    const { data, error } = await this.supabase
      .from('categories')
      .insert(category)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async update(id: string, updates: CategoryUpdate) {
    const { data, error } = await this.supabase
      .from('categories')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async delete(id: string) {
    // Check if category has children
    const { data: children } = await this.supabase
      .from('categories')
      .select('id')
      .eq('parent_id', id)
      .eq('is_active', true)

    if (children && children.length > 0) {
      throw new Error('Cannot delete category with active subcategories')
    }

    // Check if category has products
    const { data: products } = await this.supabase
      .from('products')
      .select('id')
      .eq('category_id', id)
      .eq('is_active', true)

    if (products && products.length > 0) {
      throw new Error('Cannot delete category with active products')
    }

    // Soft delete
    const { data, error } = await this.supabase
      .from('categories')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async reorder(categoryId: string, newSortOrder: number) {
    const { data, error } = await this.supabase
      .from('categories')
      .update({ sort_order: newSortOrder })
      .eq('id', categoryId)
      .select()
      .single()

    if (error) throw error
    return data
  }
}
