import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Product = Database['public']['Tables']['products']['Row']
type ProductInsert = Database['public']['Tables']['products']['Insert']
type ProductUpdate = Database['public']['Tables']['products']['Update']

export class ProductQueries {
  private supabase = createClient()

  async getAll() {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name),
        vendors (id, name),
        product_variants (*)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name),
        vendors (id, name),
        product_variants (*)
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async getBySku(sku: string) {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name),
        vendors (id, name),
        product_variants (*)
      `)
      .eq('sku', sku)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async getByCategory(categoryId: string) {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name),
        vendors (id, name)
      `)
      .eq('category_id', categoryId)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }

  async getLowStock(threshold?: number) {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name)
      `)
      .lte('stock_quantity', threshold || 10)
      .eq('is_active', true)
      .order('stock_quantity')

    if (error) throw error
    return data
  }

  async create(product: ProductInsert) {
    const { data, error } = await this.supabase
      .from('products')
      .insert(product)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async update(id: string, updates: ProductUpdate) {
    const { data, error } = await this.supabase
      .from('products')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateStock(id: string, quantity: number) {
    const { data, error } = await this.supabase
      .from('products')
      .update({ stock_quantity: quantity })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async search(query: string) {
    const { data, error } = await this.supabase
      .from('products')
      .select(`
        *,
        categories (id, name),
        brands (id, name)
      `)
      .or(`name.ilike.%${query}%,sku.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name')
      .limit(50)

    if (error) throw error
    return data
  }
}
