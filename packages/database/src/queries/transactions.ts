import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Transaction = Database['public']['Tables']['transactions']['Row']
type TransactionInsert = Database['public']['Tables']['transactions']['Insert']
type TransactionUpdate = Database['public']['Tables']['transactions']['Update']
type TransactionItem = Database['public']['Tables']['transaction_items']['Row']
type TransactionItemInsert = Database['public']['Tables']['transaction_items']['Insert']
type TransactionItemUpdate = Database['public']['Tables']['transaction_items']['Update']

export interface TransactionWithItems extends Transaction {
  transaction_items: TransactionItem[]
  customers?: {
    id: string
    first_name: string | null
    last_name: string | null
    company_name: string | null
    customer_type: string
  }
}

export interface CreateTransactionData {
  transaction: Omit<TransactionInsert, 'id' | 'created_at' | 'updated_at'>
  items: Omit<TransactionItemInsert, 'id' | 'transaction_id' | 'created_at'>[]
}

export class TransactionQueries {
  private supabase = createClient()

  // Generate unique transaction number with new format
  private async generateTransactionNumber(transactionType: 'sale' | 'return' | 'wholesale_order' | 'adjustment', customerType?: 'retail' | 'wholesale'): Promise<string> {
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const yearMonth = `${year}${month}`

    // Determine prefix based on transaction type and customer type
    let prefix: string
    if (transactionType === 'sale' || transactionType === 'return') {
      // For orders, use OR prefix
      prefix = 'OR'
    } else if (transactionType === 'wholesale_order') {
      prefix = 'OR' // Orders use OR regardless of type
    } else {
      prefix = 'TXN' // Keep existing format for adjustments
    }

    // Get the next sequence number for this prefix and month
    const sequenceNumber = await this.getNextSequenceNumber(prefix, yearMonth)

    if (prefix === 'TXN') {
      // Keep old format for adjustments
      const timestamp = Date.now().toString().slice(-6)
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
      return `${prefix}${timestamp}${random}`
    }

    return `${prefix}-${yearMonth}-${sequenceNumber.toString().padStart(5, '0')}`
  }

  // Get next sequence number for a given prefix and month
  private async getNextSequenceNumber(prefix: string, yearMonth: string): Promise<number> {
    // Query for the highest sequence number for this prefix and month
    const pattern = `${prefix}-${yearMonth}-%`

    const { data, error } = await this.supabase
      .from('transactions')
      .select('transaction_number')
      .like('transaction_number', pattern)
      .order('transaction_number', { ascending: false })
      .limit(1)

    if (error) throw error

    if (!data || data.length === 0) {
      return 1 // First transaction of the month
    }

    // Extract sequence number from the last transaction
    const lastNumber = data[0].transaction_number
    const parts = lastNumber.split('-')
    if (parts.length === 3) {
      const lastSequence = parseInt(parts[2], 10)
      return lastSequence + 1
    }

    return 1
  }

  // Generate invoice number (separate from order number)
  private async generateInvoiceNumber(customerType: 'retail' | 'wholesale'): Promise<string> {
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const yearMonth = `${year}${month}`

    // Determine prefix based on customer type
    const prefix = customerType === 'wholesale' ? 'WH' : 'RE'

    // Get the next sequence number for this prefix and month
    const sequenceNumber = await this.getNextInvoiceSequenceNumber(prefix, yearMonth)

    return `${prefix}-${yearMonth}-${sequenceNumber.toString().padStart(5, '0')}`
  }

  // Get next invoice sequence number for a given prefix and month
  private async getNextInvoiceSequenceNumber(prefix: string, yearMonth: string): Promise<number> {
    // Query for the highest sequence number for this prefix and month
    const pattern = `${prefix}-${yearMonth}-%`

    const { data, error } = await this.supabase
      .from('transactions')
      .select('invoice_number')
      .like('invoice_number', pattern)
      .not('invoice_number', 'is', null)
      .order('invoice_number', { ascending: false })
      .limit(1)

    if (error) throw error

    if (!data || data.length === 0) {
      return 1 // First invoice of the month
    }

    // Extract sequence number from the last invoice
    const lastNumber = data[0].invoice_number
    if (lastNumber) {
      const parts = lastNumber.split('-')
      if (parts.length === 3) {
        const lastSequence = parseInt(parts[2], 10)
        return lastSequence + 1
      }
    }

    return 1
  }

  // Generate and assign invoice number to a transaction
  async generateAndAssignInvoiceNumber(transactionId: string, customerType: 'retail' | 'wholesale'): Promise<string> {
    const invoiceNumber = await this.generateInvoiceNumber(customerType)

    const { error } = await this.supabase
      .from('transactions')
      .update({ invoice_number: invoiceNumber })
      .eq('id', transactionId)

    if (error) throw error

    return invoiceNumber
  }

  // Complete a transaction and generate invoice number
  async completeTransaction(transactionId: string): Promise<void> {
    // Get transaction details to determine customer type
    const { data: transaction, error: fetchError } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          customer_type
        )
      `)
      .eq('id', transactionId)
      .single()

    if (fetchError) throw fetchError
    if (!transaction) throw new Error('Transaction not found')

    // Determine customer type (default to retail if no customer)
    const customerType = transaction.customers?.customer_type || 'retail'

    // Generate invoice number if not already assigned
    let invoiceNumber = transaction.invoice_number
    if (!invoiceNumber) {
      invoiceNumber = await this.generateInvoiceNumber(customerType as 'retail' | 'wholesale')
    }

    // Update transaction status and invoice number
    const { error: updateError } = await this.supabase
      .from('transactions')
      .update({
        status: 'completed',
        invoice_number: invoiceNumber
      })
      .eq('id', transactionId)

    if (updateError) throw updateError
  }

  async getAll(limit = 50, offset = 0) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          company_name,
          customer_type
        ),
        transaction_items (*)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return data as TransactionWithItems[]
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          company_name,
          customer_type,
          email,
          phone
        ),
        transaction_items (
          *,
          products (
            id,
            name,
            sku,
            retail_price
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as TransactionWithItems
  }

  async getByCustomer(customerId: string, limit = 20) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        transaction_items (*)
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as TransactionWithItems[]
  }

  async getByStatus(status: string, limit = 50) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          company_name
        ),
        transaction_items (*)
      `)
      .eq('status', status)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as TransactionWithItems[]
  }

  async getByDateRange(startDate: string, endDate: string) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          company_name
        ),
        transaction_items (*)
      `)
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as TransactionWithItems[]
  }

  async create(transactionData: CreateTransactionData) {
    const { transaction, items } = transactionData

    // Get customer type if customer_id is provided
    let customerType: 'retail' | 'wholesale' | undefined
    if (transaction.customer_id) {
      const { data: customer, error: customerError } = await this.supabase
        .from('customers')
        .select('customer_type')
        .eq('id', transaction.customer_id)
        .single()

      if (!customerError && customer) {
        customerType = customer.customer_type as 'retail' | 'wholesale'
      }
    }

    // Generate transaction number if not provided
    const transactionNumber = transaction.transaction_number || await this.generateTransactionNumber(
      transaction.transaction_type as 'sale' | 'return' | 'wholesale_order' | 'adjustment',
      customerType
    )

    // Start a transaction
    const { data: newTransaction, error: transactionError } = await this.supabase
      .from('transactions')
      .insert({
        ...transaction,
        transaction_number: transactionNumber
      })
      .select()
      .single()

    if (transactionError) throw transactionError

    // Insert transaction items
    const itemsWithTransactionId = items.map(item => ({
      ...item,
      transaction_id: newTransaction.id
    }))

    const { data: newItems, error: itemsError } = await this.supabase
      .from('transaction_items')
      .insert(itemsWithTransactionId)
      .select()

    if (itemsError) {
      // Rollback transaction if items insertion fails
      await this.supabase
        .from('transactions')
        .delete()
        .eq('id', newTransaction.id)
      throw itemsError
    }

    // Update inventory for completed sales
    if (newTransaction.status === 'completed' && newTransaction.transaction_type === 'sale') {
      await this.updateInventoryForTransaction(newTransaction.id, 'decrease')
    }

    // Return the complete transaction with items
    return {
      ...newTransaction,
      transaction_items: newItems
    } as TransactionWithItems
  }

  // Update inventory based on transaction
  private async updateInventoryForTransaction(transactionId: string, operation: 'increase' | 'decrease') {
    const { data: items, error } = await this.supabase
      .from('transaction_items')
      .select('product_id, quantity')
      .eq('transaction_id', transactionId)

    if (error) throw error

    for (const item of items) {
      if (item.product_id) {
        const { data: product, error: productError } = await this.supabase
          .from('products')
          .select('stock_quantity')
          .eq('id', item.product_id)
          .single()

        if (productError) continue // Skip if product not found

        const newQuantity = operation === 'decrease'
          ? product.stock_quantity - item.quantity
          : product.stock_quantity + item.quantity

        await this.supabase
          .from('products')
          .update({ stock_quantity: Math.max(0, newQuantity) })
          .eq('id', item.product_id)
      }
    }
  }

  async update(id: string, updates: TransactionUpdate) {
    const { data, error } = await this.supabase
      .from('transactions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateStatus(id: string, status: string, notes?: string) {
    // Get current transaction to check previous status
    const { data: currentTransaction, error: fetchError } = await this.supabase
      .from('transactions')
      .select('status, transaction_type')
      .eq('id', id)
      .single()

    if (fetchError) throw fetchError

    const updates: TransactionUpdate = { status }
    if (notes) {
      updates.internal_notes = notes
    }

    const { data: updatedTransaction, error: updateError } = await this.supabase
      .from('transactions')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (updateError) throw updateError

    // Handle inventory updates based on status changes
    const previousStatus = currentTransaction.status
    const transactionType = currentTransaction.transaction_type

    // Update inventory when completing a sale
    if (status === 'completed' && previousStatus !== 'completed' && transactionType === 'sale') {
      await this.updateInventoryForTransaction(id, 'decrease')
    }

    // Restore inventory when cancelling a completed sale
    if (status === 'cancelled' && previousStatus === 'completed' && transactionType === 'sale') {
      await this.updateInventoryForTransaction(id, 'increase')
    }

    return updatedTransaction
  }

  async addItem(transactionId: string, item: Omit<TransactionItemInsert, 'transaction_id'>) {
    const { data, error } = await this.supabase
      .from('transaction_items')
      .insert({
        ...item,
        transaction_id: transactionId
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateItem(itemId: string, updates: TransactionItemUpdate) {
    const { data, error } = await this.supabase
      .from('transaction_items')
      .update(updates)
      .eq('id', itemId)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async removeItem(itemId: string) {
    const { error } = await this.supabase
      .from('transaction_items')
      .delete()
      .eq('id', itemId)

    if (error) throw error
    return true
  }

  async getStats() {
    // Get total transactions count
    const { data: totalTransactions, error: totalError } = await this.supabase
      .from('transactions')
      .select('id', { count: 'exact' })

    // Get completed transactions count
    const { data: completedTransactions, error: completedError } = await this.supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('status', 'completed')

    // Get pending transactions count
    const { data: pendingTransactions, error: pendingError } = await this.supabase
      .from('transactions')
      .select('id', { count: 'exact' })
      .eq('status', 'pending')

    // Get today's sales
    const today = new Date().toISOString().split('T')[0]
    const { data: todaySales, error: todayError } = await this.supabase
      .from('transactions')
      .select('total_amount')
      .eq('status', 'completed')
      .gte('created_at', `${today}T00:00:00`)
      .lte('created_at', `${today}T23:59:59`)

    if (totalError || completedError || pendingError || todayError) {
      throw totalError || completedError || pendingError || todayError
    }

    const todayTotal = todaySales?.reduce((sum, t) => sum + (t.total_amount || 0), 0) || 0

    return {
      total: totalTransactions?.length || 0,
      completed: completedTransactions?.length || 0,
      pending: pendingTransactions?.length || 0,
      todayTotal
    }
  }

  async search(query: string, limit = 50) {
    const { data, error } = await this.supabase
      .from('transactions')
      .select(`
        *,
        customers (
          id,
          first_name,
          last_name,
          company_name
        ),
        transaction_items (*)
      `)
      .or(`transaction_number.ilike.%${query}%,notes.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as TransactionWithItems[]
  }
}
