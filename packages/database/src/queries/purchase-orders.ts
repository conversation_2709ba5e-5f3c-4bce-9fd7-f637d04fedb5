import { createClient } from '../client'
import type { Database } from '../types/supabase'

type PurchaseOrder = Database['public']['Tables']['purchase_orders']['Row']
type PurchaseOrderInsert = Database['public']['Tables']['purchase_orders']['Insert']
type PurchaseOrderUpdate = Database['public']['Tables']['purchase_orders']['Update']

export interface PurchaseOrderWithItems extends PurchaseOrder {
  purchase_order_items: {
    id: string
    product_id: string | null
    quantity_ordered: number
    quantity_received: number
    unit_cost: number
    line_total: number
    product_name: string
    product_sku: string
    batch_number: string | null
    expiry_date: string | null
    received_date: string | null
  }[]
  vendors?: {
    id: string
    name: string
    contact_email: string | null
    preferred_currency: string
  }
}

export interface CreatePurchaseOrderData {
  purchase_order: Omit<PurchaseOrderInsert, 'id' | 'created_at' | 'updated_at'>
  items: {
    product_id: string
    quantity_ordered: number
    unit_cost: number
    line_total: number
    product_name: string
    product_sku: string
  }[]
}

export class PurchaseOrderQueries {
  private supabase = createClient()

  // Generate unique PO number with new format
  private async generatePONumber(): Promise<string> {
    const now = new Date()
    const year = now.getFullYear()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const yearMonth = `${year}${month}`

    const prefix = 'PO'

    // Get the next sequence number for this prefix and month
    const sequenceNumber = await this.getNextPOSequenceNumber(prefix, yearMonth)

    return `${prefix}-${yearMonth}-${sequenceNumber.toString().padStart(5, '0')}`
  }

  // Get next PO sequence number for a given prefix and month
  private async getNextPOSequenceNumber(prefix: string, yearMonth: string): Promise<number> {
    // Query for the highest sequence number for this prefix and month
    const pattern = `${prefix}-${yearMonth}-%`

    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select('po_number')
      .like('po_number', pattern)
      .order('po_number', { ascending: false })
      .limit(1)

    if (error) throw error

    if (!data || data.length === 0) {
      return 1 // First PO of the month
    }

    // Extract sequence number from the last PO
    const lastNumber = data[0].po_number
    const parts = lastNumber.split('-')
    if (parts.length === 3) {
      const lastSequence = parseInt(parts[2], 10)
      return lastSequence + 1
    }

    return 1
  }

  async getAll(limit = 50, offset = 0) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select(`
        *,
        vendors (
          id,
          name,
          contact_email,
          preferred_currency
        ),
        purchase_order_items (*)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return data as PurchaseOrderWithItems[]
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select(`
        *,
        vendors (
          id,
          name,
          contact_email,
          preferred_currency,
          address_line1,
          city,
          country
        ),
        purchase_order_items (
          *,
          products (
            id,
            name,
            sku,
            purchase_price
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as PurchaseOrderWithItems
  }

  async getByVendor(vendorId: string, limit = 20) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select(`
        *,
        purchase_order_items (*)
      `)
      .eq('vendor_id', vendorId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as PurchaseOrderWithItems[]
  }

  async getByStatus(status: string, limit = 50) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select(`
        *,
        vendors (
          id,
          name,
          contact_email
        ),
        purchase_order_items (*)
      `)
      .eq('status', status)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as PurchaseOrderWithItems[]
  }

  async create(purchaseOrderData: CreatePurchaseOrderData) {
    const { purchase_order, items } = purchaseOrderData

    // Generate PO number if not provided
    const poNumber = purchase_order.po_number || await this.generatePONumber()

    // Create the purchase order
    const { data: newPO, error: poError } = await this.supabase
      .from('purchase_orders')
      .insert({
        ...purchase_order,
        po_number: poNumber
      })
      .select()
      .single()

    if (poError) throw poError

    // Insert purchase order items
    const itemsWithPOId = items.map(item => ({
      ...item,
      purchase_order_id: newPO.id
    }))

    const { data: newItems, error: itemsError } = await this.supabase
      .from('purchase_order_items')
      .insert(itemsWithPOId)
      .select()

    if (itemsError) {
      // Rollback PO if items insertion fails
      await this.supabase
        .from('purchase_orders')
        .delete()
        .eq('id', newPO.id)
      throw itemsError
    }

    // Return the complete PO with items
    return {
      ...newPO,
      purchase_order_items: newItems
    } as PurchaseOrderWithItems
  }

  async update(id: string, updates: PurchaseOrderUpdate) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async updateStatus(id: string, status: string, notes?: string) {
    const updates: PurchaseOrderUpdate = { status }
    if (notes) {
      updates.internal_notes = notes
    }

    return this.update(id, updates)
  }

  async receiveItems(poId: string, receivedItems: {
    item_id: string
    quantity_received: number
    batch_number?: string
    expiry_date?: string
  }[]) {
    // Update each item with received quantities
    for (const item of receivedItems) {
      const { error } = await this.supabase
        .from('purchase_order_items')
        .update({
          quantity_received: item.quantity_received,
          batch_number: item.batch_number,
          expiry_date: item.expiry_date,
          received_date: new Date().toISOString()
        })
        .eq('id', item.item_id)

      if (error) throw error

      // Update product stock levels
      const { data: poItem, error: itemError } = await this.supabase
        .from('purchase_order_items')
        .select('product_id, quantity_received')
        .eq('id', item.item_id)
        .single()

      if (itemError) continue

      if (poItem.product_id) {
        // Get current stock
        const { data: product, error: productError } = await this.supabase
          .from('products')
          .select('stock_quantity')
          .eq('id', poItem.product_id)
          .single()

        if (productError) continue

        // Update stock
        await this.supabase
          .from('products')
          .update({
            stock_quantity: product.stock_quantity + item.quantity_received
          })
          .eq('id', poItem.product_id)
      }
    }

    // Check if PO is fully received
    const { data: poItems, error: itemsError } = await this.supabase
      .from('purchase_order_items')
      .select('quantity_ordered, quantity_received')
      .eq('purchase_order_id', poId)

    if (itemsError) throw itemsError

    const isFullyReceived = poItems.every(item => 
      item.quantity_received >= item.quantity_ordered
    )

    const isPartiallyReceived = poItems.some(item => 
      item.quantity_received > 0
    )

    // Update PO status
    let newStatus = 'sent'
    if (isFullyReceived) {
      newStatus = 'received'
    } else if (isPartiallyReceived) {
      newStatus = 'partially_received'
    }

    await this.updateStatus(poId, newStatus)

    return true
  }

  async getStats() {
    // Get total POs count
    const { data: totalPOs, error: totalError } = await this.supabase
      .from('purchase_orders')
      .select('id', { count: 'exact' })

    // Get pending POs count
    const { data: pendingPOs, error: pendingError } = await this.supabase
      .from('purchase_orders')
      .select('id', { count: 'exact' })
      .in('status', ['draft', 'pending_approval', 'approved', 'sent'])

    // Get this month's PO value
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const { data: monthlyPOs, error: monthlyError } = await this.supabase
      .from('purchase_orders')
      .select('total_amount_awg')
      .gte('created_at', startOfMonth.toISOString())

    if (totalError || pendingError || monthlyError) {
      throw totalError || pendingError || monthlyError
    }

    const monthlyTotal = monthlyPOs?.reduce((sum, po) => sum + (po.total_amount_awg || 0), 0) || 0

    return {
      total: totalPOs?.length || 0,
      pending: pendingPOs?.length || 0,
      monthlyTotal
    }
  }

  async search(query: string, limit = 50) {
    const { data, error } = await this.supabase
      .from('purchase_orders')
      .select(`
        *,
        vendors (
          id,
          name,
          contact_email
        ),
        purchase_order_items (*)
      `)
      .or(`po_number.ilike.%${query}%,notes.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data as PurchaseOrderWithItems[]
  }
}
