import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Brand = Database['public']['Tables']['brands']['Row']
type BrandInsert = Database['public']['Tables']['brands']['Insert']
type BrandUpdate = Database['public']['Tables']['brands']['Update']

export class BrandQueries {
  private supabase = createClient()

  async getAll() {
    const { data, error } = await this.supabase
      .from('brands')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('brands')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async search(query: string) {
    const { data, error } = await this.supabase
      .from('brands')
      .select('*')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name')
      .limit(50)

    if (error) throw error
    return data
  }

  async create(brand: BrandInsert) {
    const { data, error } = await this.supabase
      .from('brands')
      .insert(brand)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async update(id: string, updates: BrandUpdate) {
    const { data, error } = await this.supabase
      .from('brands')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async delete(id: string) {
    // Check if brand has products
    const { data: products } = await this.supabase
      .from('products')
      .select('id')
      .eq('brand_id', id)
      .eq('is_active', true)

    if (products && products.length > 0) {
      throw new Error('Cannot delete brand with active products')
    }

    // Soft delete
    const { data, error } = await this.supabase
      .from('brands')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getProductCount(brandId: string) {
    const { count, error } = await this.supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('brand_id', brandId)
      .eq('is_active', true)

    if (error) throw error
    return count || 0
  }

  async getBrandsWithProductCounts() {
    const { data, error } = await this.supabase
      .from('brands')
      .select(`
        *,
        products!inner(count)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}
