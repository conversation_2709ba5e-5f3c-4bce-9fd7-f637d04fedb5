import { createClient } from '../client'
import type { Database } from '../types/supabase'

type Vendor = Database['public']['Tables']['vendors']['Row']
type VendorInsert = Database['public']['Tables']['vendors']['Insert']
type VendorUpdate = Database['public']['Tables']['vendors']['Update']

export class VendorQueries {
  private supabase = createClient()

  async getAll() {
    const { data, error } = await this.supabase
      .from('vendors')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }

  async getById(id: string) {
    const { data, error } = await this.supabase
      .from('vendors')
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return data
  }

  async search(query: string) {
    const { data, error } = await this.supabase
      .from('vendors')
      .select('*')
      .or(`name.ilike.%${query}%,contact_person.ilike.%${query}%,email.ilike.%${query}%`)
      .eq('is_active', true)
      .order('name')
      .limit(50)

    if (error) throw error
    return data
  }

  async create(vendor: VendorInsert) {
    const { data, error } = await this.supabase
      .from('vendors')
      .insert(vendor)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async update(id: string, updates: VendorUpdate) {
    const { data, error } = await this.supabase
      .from('vendors')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async delete(id: string) {
    // Check if vendor has products
    const { data: products } = await this.supabase
      .from('products')
      .select('id')
      .eq('vendor_id', id)
      .eq('is_active', true)

    if (products && products.length > 0) {
      throw new Error('Cannot delete vendor with active products')
    }

    // Soft delete
    const { data, error } = await this.supabase
      .from('vendors')
      .update({ is_active: false })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async getProductCount(vendorId: string) {
    const { count, error } = await this.supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', vendorId)
      .eq('is_active', true)

    if (error) throw error
    return count || 0
  }

  async getVendorsWithProductCounts() {
    const { data, error } = await this.supabase
      .from('vendors')
      .select(`
        *,
        products!inner(count)
      `)
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data
  }
}
