-- Add Invoice Numbering Migration
-- This adds invoice_number field to transactions table and creates functions for proper numbering

-- Add invoice_number field to transactions table
ALTER TABLE transactions 
ADD COLUMN IF NOT EXISTS invoice_number varchar(20) UNIQUE;

-- Create index for invoice numbers
CREATE INDEX IF NOT EXISTS idx_transactions_invoice_number ON transactions(invoice_number);

-- Add comment to clarify the difference
COMMENT ON COLUMN transactions.transaction_number IS 'Order number (OR-YYYYMM-NNNNN format)';
COMMENT ON COLUMN transactions.invoice_number IS 'Invoice number (RE-YYYYMM-NNNNN for retail, WH-YYYYMM-NNNNN for wholesale)';
