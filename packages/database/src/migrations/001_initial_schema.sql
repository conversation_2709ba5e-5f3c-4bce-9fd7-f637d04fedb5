-- NutriPro Database Schema - Initial Migration
-- This creates the core tables for the NutriPro system

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'staff', 'viewer');
CREATE TYPE customer_type AS ENUM ('retail', 'wholesale');
CREATE TYPE transaction_status AS ENUM ('pending', 'processing', 'completed', 'cancelled', 'refunded');
CREATE TYPE payment_method AS ENUM ('cash', 'card', 'bank_transfer', 'store_credit');
CREATE TYPE transaction_type AS ENUM ('sale', 'return', 'wholesale_order', 'adjustment');

-- Categories table
CREATE TABLE categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(100) NOT NULL,
  description text,
  parent_id uuid REFERENCES categories(id),
  sort_order integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Brands table
CREATE TABLE brands (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(100) NOT NULL,
  description text,
  logo_url text,
  website_url text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Vendors table
CREATE TABLE vendors (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar(200) NOT NULL,
  contact_person varchar(100),
  email varchar(255),
  phone varchar(20),
  address text,
  preferred_currency varchar(3) DEFAULT 'AWG',
  payment_terms text,
  notes text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Products table
CREATE TABLE products (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  sku varchar(50) UNIQUE NOT NULL,
  barcode varchar(50),
  name varchar(200) NOT NULL,
  description text,
  category_id uuid REFERENCES categories(id),
  brand_id uuid REFERENCES brands(id),
  vendor_id uuid REFERENCES vendors(id),
  
  -- Pricing (all in AWG unless specified)
  retail_price decimal(10,2) NOT NULL,
  landing_cost decimal(10,2),
  purchase_price decimal(10,2),
  purchase_currency varchar(3) DEFAULT 'AWG',
  wholesale_price decimal(10,2),
  wholesale_available boolean DEFAULT false,
  
  -- Inventory
  stock_quantity integer DEFAULT 0,
  min_stock_level integer DEFAULT 0,
  max_stock_level integer,
  
  -- Product Details
  notes text,
  weight decimal(8,2), -- in grams
  dimensions jsonb, -- {length, width, height}
  images text[], -- Array of image URLs
  
  -- Supplement-specific fields
  serving_size varchar(50),
  servings_per_container integer,
  ingredients text[],
  allergens text[],
  expiry_tracking boolean DEFAULT false,
  
  -- Product Variants
  has_variants boolean DEFAULT false,
  variant_type varchar(50),
  
  -- Wholesale specific
  min_order_quantity integer DEFAULT 1,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Product Variants table
CREATE TABLE product_variants (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id) ON DELETE CASCADE,
  variant_name varchar(100) NOT NULL, -- e.g., "Chocolate", "Vanilla"
  variant_value varchar(100) NOT NULL, -- e.g., "Chocolate Flavor"
  sku_suffix varchar(20), -- e.g., "-CHOC"
  price_adjustment decimal(10,2) DEFAULT 0,
  stock_quantity integer DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now(),
  
  UNIQUE(product_id, variant_name)
);

-- Customers table
CREATE TABLE customers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_type customer_type DEFAULT 'retail',
  
  -- Basic Info
  first_name varchar(100),
  last_name varchar(100),
  company_name varchar(200), -- For wholesale customers
  email varchar(255),
  phone varchar(20),
  
  -- Address
  address_line1 text,
  address_line2 text,
  city varchar(100),
  postal_code varchar(20),
  country varchar(100) DEFAULT 'Aruba',
  
  -- Business Info (for wholesale)
  tax_id varchar(50),
  business_license varchar(100),
  
  -- Loyalty & Credits
  loyalty_points integer DEFAULT 0,
  store_credit decimal(10,2) DEFAULT 0,
  membership_tier varchar(50) DEFAULT 'regular', -- 'regular', 'premium'
  membership_expires_at timestamptz,
  
  -- Coach Assignment
  assigned_coach_id uuid,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Coaches table
CREATE TABLE coaches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid, -- Reference to auth.users
  
  -- Basic Info
  first_name varchar(100) NOT NULL,
  last_name varchar(100) NOT NULL,
  email varchar(255) NOT NULL,
  phone varchar(20),
  
  -- Coach Program
  monthly_credit_amount decimal(10,2) DEFAULT 0,
  current_credit_balance decimal(10,2) DEFAULT 0,
  referral_percentage decimal(5,2) DEFAULT 0, -- Individual percentage
  
  -- Performance Tracking
  total_referrals integer DEFAULT 0,
  total_sales decimal(12,2) DEFAULT 0,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes
CREATE INDEX idx_categories_parent ON categories(parent_id);
CREATE INDEX idx_categories_active ON categories(is_active);

CREATE INDEX idx_brands_active ON brands(is_active);

CREATE INDEX idx_vendors_active ON vendors(is_active);

CREATE INDEX idx_products_sku ON products(sku);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand_id);
CREATE INDEX idx_products_vendor ON products(vendor_id);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_stock ON products(stock_quantity);
CREATE INDEX idx_products_wholesale ON products(wholesale_available);

CREATE INDEX idx_product_variants_product ON product_variants(product_id);
CREATE INDEX idx_product_variants_active ON product_variants(is_active);

CREATE INDEX idx_customers_type ON customers(customer_type);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_coach ON customers(assigned_coach_id);
CREATE INDEX idx_customers_active ON customers(is_active);

CREATE INDEX idx_coaches_user ON coaches(user_id);
CREATE INDEX idx_coaches_active ON coaches(is_active);

-- Transactions table
CREATE TABLE transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_number varchar(20) UNIQUE NOT NULL,

  -- References
  customer_id uuid REFERENCES customers(id),
  staff_id uuid, -- Reference to auth.users

  -- Transaction Details
  transaction_type transaction_type NOT NULL,
  status transaction_status DEFAULT 'pending',

  -- Financial
  subtotal decimal(10,2) NOT NULL,
  tax_rate decimal(5,4) DEFAULT 0,
  tax_amount decimal(10,2) DEFAULT 0,
  discount_amount decimal(10,2) DEFAULT 0,
  total_amount decimal(10,2) NOT NULL,

  -- Payment
  payment_method payment_method,
  payment_reference varchar(100),

  -- Additional Info
  notes text,
  internal_notes text,

  -- Offline sync support
  synced_at timestamptz,
  sync_version integer DEFAULT 1,

  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Transaction Items table
CREATE TABLE transaction_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid REFERENCES transactions(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id),

  -- Item Details
  quantity integer NOT NULL CHECK (quantity > 0),
  unit_price decimal(10,2) NOT NULL,
  discount_amount decimal(10,2) DEFAULT 0,
  line_total decimal(10,2) NOT NULL,

  -- Product Info (snapshot for historical accuracy)
  product_name varchar(200) NOT NULL,
  product_sku varchar(50) NOT NULL,

  -- Batch tracking (for future FIFO implementation)
  batch_number varchar(50),
  expiry_date date,

  created_at timestamptz DEFAULT now()
);

-- Create indexes for transactions
CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_customer ON transactions(customer_id);
CREATE INDEX idx_transactions_staff ON transactions(staff_id);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_date ON transactions(created_at);
CREATE INDEX idx_transactions_sync ON transactions(synced_at);

-- Create indexes for transaction items
CREATE INDEX idx_transaction_items_transaction ON transaction_items(transaction_id);
CREATE INDEX idx_transaction_items_product ON transaction_items(product_id);
CREATE INDEX idx_transaction_items_sku ON transaction_items(product_sku);

-- Add foreign key constraint for coaches
ALTER TABLE customers ADD CONSTRAINT fk_customers_coach
  FOREIGN KEY (assigned_coach_id) REFERENCES coaches(id);
