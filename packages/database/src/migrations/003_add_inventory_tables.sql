-- Add Advanced Inventory Management Tables
-- This adds product batches, purchase orders, and inventory tracking tables

-- Add new enums for inventory management
DO $$ BEGIN
    CREATE TYPE batch_status_enum AS ENUM ('active', 'expired', 'recalled', 'depleted');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE batch_movement_enum AS ENUM ('received', 'sold', 'expired', 'returned', 'adjusted');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE po_status_enum AS ENUM ('draft', 'pending_approval', 'approved', 'sent', 'partially_received', 'received', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE adjustment_type_enum AS ENUM ('restock', 'damage', 'theft', 'expired', 'count_correction', 'return');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE movement_type_enum AS ENUM ('sale', 'purchase', 'adjustment', 'transfer', 'return');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Product Batches table for FIFO and expiry tracking
CREATE TABLE IF NOT EXISTS product_batches (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  variant_id uuid REFERENCES product_variants(id), -- NULL if master product has no variants

  -- Batch Information
  batch_number varchar(100) NOT NULL,
  expiry_date date NOT NULL,
  received_date date NOT NULL,

  -- Inventory Tracking
  quantity_received integer NOT NULL,
  quantity_available integer NOT NULL,
  quantity_sold integer DEFAULT 0,
  quantity_expired integer DEFAULT 0,
  quantity_returned integer DEFAULT 0,

  -- Cost Tracking (for FIFO costing)
  unit_cost decimal(10,2) NOT NULL,
  total_cost decimal(10,2) NOT NULL,

  -- FIFO Priority (lower number = older batch, sell first)
  fifo_priority integer NOT NULL,

  -- Status
  status batch_status_enum DEFAULT 'active',
  notes text,

  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Purchase Orders table
CREATE TABLE IF NOT EXISTS purchase_orders (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  po_number varchar(20) UNIQUE NOT NULL,
  vendor_id uuid REFERENCES vendors(id),

  -- Order Details
  status po_status_enum DEFAULT 'draft',
  order_date timestamptz DEFAULT now(),
  expected_delivery_date date,
  actual_delivery_date date,

  -- Financial (in vendor's preferred currency)
  currency varchar(3) NOT NULL DEFAULT 'USD',
  subtotal decimal(10,2) NOT NULL DEFAULT 0,
  tax_amount decimal(10,2) DEFAULT 0,
  shipping_cost decimal(10,2) DEFAULT 0,
  discount_amount decimal(10,2) DEFAULT 0,
  total_amount decimal(10,2) NOT NULL DEFAULT 0,

  -- AWG equivalent (for reporting)
  total_amount_awg decimal(10,2),
  exchange_rate decimal(10,6) DEFAULT 1.0,

  -- Additional Info
  notes text,
  internal_notes text,

  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Purchase Order Items table
CREATE TABLE IF NOT EXISTS purchase_order_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  purchase_order_id uuid REFERENCES purchase_orders(id) ON DELETE CASCADE,
  product_id uuid REFERENCES products(id),

  -- Order Details
  quantity_ordered integer NOT NULL CHECK (quantity_ordered > 0),
  quantity_received integer DEFAULT 0,
  unit_cost decimal(10,2) NOT NULL,
  line_total decimal(10,2) NOT NULL,

  -- Product Info (snapshot for historical accuracy)
  product_name varchar(200) NOT NULL,
  product_sku varchar(50) NOT NULL,

  -- Receiving Details
  batch_number varchar(50),
  expiry_date date,
  received_date timestamptz,

  created_at timestamptz DEFAULT now()
);

-- Inventory Adjustments table
CREATE TABLE IF NOT EXISTS inventory_adjustments (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  staff_id uuid, -- Reference to auth.users
  
  adjustment_type adjustment_type_enum NOT NULL,
  quantity_change integer NOT NULL, -- positive or negative
  reason varchar(200),
  notes text,
  
  -- Cost tracking
  unit_cost decimal(10,2),
  total_cost decimal(10,2),
  
  created_at timestamptz DEFAULT now()
);

-- Stock Movements table for tracking all inventory changes
CREATE TABLE IF NOT EXISTS stock_movements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id uuid REFERENCES products(id),
  
  movement_type movement_type_enum NOT NULL,
  quantity integer NOT NULL,
  reference_id uuid, -- transaction_id, adjustment_id, or po_id
  reference_type varchar(50),
  
  -- Stock levels after movement
  stock_before integer NOT NULL,
  stock_after integer NOT NULL,
  
  created_at timestamptz DEFAULT now()
);

-- Batch Movements table to track which batch items came from
CREATE TABLE IF NOT EXISTS batch_movements (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id uuid REFERENCES product_batches(id),
  transaction_item_id uuid REFERENCES transaction_items(id),

  -- Movement Details
  movement_type batch_movement_enum NOT NULL,
  quantity integer NOT NULL,
  unit_cost decimal(10,2), -- For FIFO costing

  -- Reference Information
  reference_number varchar(100), -- PO number, return number, etc.
  notes text,

  created_at timestamptz DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_product_batches_product ON product_batches(product_id);
CREATE INDEX IF NOT EXISTS idx_product_batches_variant ON product_batches(variant_id);
CREATE INDEX IF NOT EXISTS idx_product_batches_expiry ON product_batches(expiry_date);
CREATE INDEX IF NOT EXISTS idx_product_batches_fifo ON product_batches(fifo_priority);
CREATE INDEX IF NOT EXISTS idx_product_batches_status ON product_batches(status);

CREATE INDEX IF NOT EXISTS idx_purchase_orders_vendor ON purchase_orders(vendor_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_date ON purchase_orders(order_date);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_po_number ON purchase_orders(po_number);

CREATE INDEX IF NOT EXISTS idx_po_items_po ON purchase_order_items(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_po_items_product ON purchase_order_items(product_id);

CREATE INDEX IF NOT EXISTS idx_inventory_adjustments_product ON inventory_adjustments(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_adjustments_date ON inventory_adjustments(created_at);

CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON stock_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_movements_reference ON stock_movements(reference_id, reference_type);

CREATE INDEX IF NOT EXISTS idx_batch_movements_batch ON batch_movements(batch_id);
CREATE INDEX IF NOT EXISTS idx_batch_movements_transaction ON batch_movements(transaction_item_id);

-- Unique constraint for batch numbers per product
CREATE UNIQUE INDEX IF NOT EXISTS idx_product_batches_unique ON product_batches(
  COALESCE(variant_id, product_id), batch_number
);

-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_product_batches_updated_at BEFORE UPDATE ON product_batches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchase_orders_updated_at BEFORE UPDATE ON purchase_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
